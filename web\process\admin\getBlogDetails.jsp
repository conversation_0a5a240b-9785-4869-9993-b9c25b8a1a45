<%--
    Document   : getBlogDetails
    Created on : Jun 15, 2025
    Author     : Arqeta
    Description: Get blog details for modal view
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<%
    String idStr = request.getParameter("id");
    
    if (idStr != null && !idStr.trim().isEmpty()) {
        try {
            int id = Integer.parseInt(idStr);
            
            PreparedStatement ps = conn.prepareStatement("SELECT * FROM blog WHERE id = ?");
            ps.setInt(1, id);
            ResultSet rs = ps.executeQuery();
            
            if (rs.next()) {
                String title = rs.getString("title");
                String image = rs.getString("image");
                String content = rs.getString("content");
                String createdAt = rs.getString("created_at");
                String updatedAt = rs.getString("updated_at");
%>
                <div class="blog-detail">
                    <div class="blog-image">
                        <% if (image != null && !image.isEmpty()) { %>
                            <img src="../dist/img/<%= image %>" alt="<%= title %>" 
                                 onerror="this.src='../dist/img/default-blog.png';">
                        <% } else { %>
                            <div class="no-image-placeholder">
                                <i data-feather="image"></i>
                                <p>Tidak ada gambar</p>
                            </div>
                        <% } %>
                    </div>
                    <div class="blog-info">
                        <h4><%= title %></h4>
                        <div class="blog-meta">
                            <p><strong>Dibuat:</strong> <%= createdAt %></p>
                            <p><strong>Diperbarui:</strong> <%= updatedAt %></p>
                        </div>
                        <div class="blog-content">
                            <h5>Konten:</h5>
                            <div class="content-text">
                                <%= content.replace("\n", "<br>") %>
                            </div>
                        </div>
                    </div>
                </div>
<%
            } else {
                out.println("<p>Blog tidak ditemukan.</p>");
            }
            
            rs.close();
            ps.close();
            
        } catch (NumberFormatException e) {
            out.println("<p>ID blog tidak valid.</p>");
        } catch (SQLException e) {
            out.println("<p>Error: " + e.getMessage() + "</p>");
        }
    } else {
        out.println("<p>ID blog tidak diberikan.</p>");
    }
%>
