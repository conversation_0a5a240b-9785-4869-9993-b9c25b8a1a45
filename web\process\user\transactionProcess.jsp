<%--
    Document   : transactionProcess
    Created on : Jun 14, 2025
    Author     : Arqeta
    Description: Process untuk menangani transaksi pembelian layanan
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>
<%@page import="java.security.MessageDigest"%>
<%@page import="java.math.BigDecimal"%>

<%
    // Cek apakah user sudah login
    String userId = (String) session.getAttribute("userId");
    if (userId == null) {
        response.sendRedirect("../form/signin.jsp");
        return;
    }

    String serviceId = request.getParameter("serviceId");
    String serviceName = request.getParameter("serviceName");
    String servicePriceStr = request.getParameter("servicePrice");
    String quantityStr = request.getParameter("quantity");
    String totalPriceStr = request.getParameter("totalPrice");

    if (serviceId == null || serviceName == null || servicePriceStr == null || 
        quantityStr == null || totalPriceStr == null) {
        session.setAttribute("message", "Data transaksi tidak lengkap!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../home.jsp");
        return;
    }

    try {
        // Parse values
        int quantity = Integer.parseInt(quantityStr);
        
        // Extract price from formatted string (remove "Rp " and commas)
        String cleanPriceStr = servicePriceStr.replaceAll("[^\\d]", "");
        String cleanTotalStr = totalPriceStr.replaceAll("[^\\d]", "");
        
        BigDecimal servicePrice = new BigDecimal(cleanPriceStr);
        BigDecimal totalPrice = new BigDecimal(cleanTotalStr);

        // Get service details to verify
        PreparedStatement psService = conn.prepareStatement("SELECT * FROM services WHERE id = ?");
        psService.setInt(1, Integer.parseInt(serviceId));
        ResultSet rsService = psService.executeQuery();

        if (!rsService.next()) {
            session.setAttribute("message", "Layanan tidak ditemukan!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../home.jsp");
            return;
        }

        // Verify price matches
        BigDecimal actualPrice = rsService.getBigDecimal("price");
        if (actualPrice.compareTo(servicePrice) != 0) {
            session.setAttribute("message", "Harga layanan tidak sesuai!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../home.jsp");
            return;
        }

        // Check if service has enough quantity
        int availableQuantity = rsService.getInt("quantity");
        if (availableQuantity < quantity) {
            session.setAttribute("message", "Kuantitas layanan tidak mencukupi!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../home.jsp");
            return;
        }

        rsService.close();
        psService.close();

        // Insert transaction
        PreparedStatement psTransaction = conn.prepareStatement(
            "INSERT INTO transaction (name, amount, unit, total, user_id, service_id, status) VALUES (?, ?, ?, ?, ?, ?, ?)"
        );
        
        psTransaction.setString(1, serviceName);
        psTransaction.setBigDecimal(2, servicePrice);
        psTransaction.setInt(3, quantity);
        psTransaction.setBigDecimal(4, totalPrice);
        psTransaction.setInt(5, Integer.parseInt(userId));
        psTransaction.setInt(6, Integer.parseInt(serviceId));
        psTransaction.setString(7, "pending");

        int result = psTransaction.executeUpdate();
        psTransaction.close();

        if (result > 0) {
            // Update service quantity
            PreparedStatement psUpdate = conn.prepareStatement(
                "UPDATE services SET quantity = quantity - ? WHERE id = ?"
            );
            psUpdate.setInt(1, quantity);
            psUpdate.setInt(2, Integer.parseInt(serviceId));
            psUpdate.executeUpdate();
            psUpdate.close();

            session.setAttribute("message", "Transaksi berhasil! Silakan tunggu konfirmasi dari admin.");
            session.setAttribute("messageType", "success");
        } else {
            session.setAttribute("message", "Gagal memproses transaksi!");
            session.setAttribute("messageType", "error");
        }

    } catch (NumberFormatException e) {
        session.setAttribute("message", "Format data tidak valid!");
        session.setAttribute("messageType", "error");
    } catch (SQLException e) {
        session.setAttribute("message", "Terjadi kesalahan database: " + e.getMessage());
        session.setAttribute("messageType", "error");
    } catch (Exception e) {
        session.setAttribute("message", "Terjadi kesalahan: " + e.getMessage());
        session.setAttribute("messageType", "error");
    }

    response.sendRedirect("../../home.jsp");
%>
