<%-- 
    Document    : deleteAccount
    Created on  : Jun 14, 2025 
    Author      : Arqeta
    Description : Process untuk menghapus akun admin/user dengan validasi keamanan
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Periksa apakah user sudah login sebagai admin
    String adminId = (String) session.getAttribute("adminId");
    if (adminId == null) {
        response.sendRedirect("../../form/signin.jsp");
        return;
    }

    // Ambil parameter dari form
    String id = request.getParameter("id");
    String type = request.getParameter("type");

    // Validasi input
    if (id == null || type == null || id.trim().isEmpty() || type.trim().isEmpty()) {
        session.setAttribute("message", "Parameter tidak valid!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=register&type=admin");
        return;
    }

    // Validasi tipe akun
    if (!type.equals("admin") && !type.equals("user")) {
        session.setAttribute("message", "Tipe akun tidak valid!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=register&type=admin");
        return;
    }

    try {
        int accountId = Integer.parseInt(id);

        // Cegah admin menghapus akunnya sendiri
        if (type.equals("admin") && accountId == Integer.parseInt(adminId)) {
            session.setAttribute("message", "Anda tidak dapat menghapus akun Anda sendiri!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../dashboardadmin.jsp?page=register&type=" + type);
            return;
        }

        // Periksa apakah akun ada di database
        String checkQuery = "SELECT COUNT(*) FROM " + type + " WHERE id = ?";
        PreparedStatement checkPs = conn.prepareStatement(checkQuery);
        checkPs.setInt(1, accountId);
        
        ResultSet checkRs = checkPs.executeQuery();
        checkRs.next();

        if (checkRs.getInt(1) == 0) {
            session.setAttribute("message", "Akun tidak ditemukan!");
            session.setAttribute("messageType", "error");
            
            checkRs.close();
            checkPs.close();
            
            response.sendRedirect("../../dashboardadmin.jsp?page=register&type=" + type);
            return;
        }

        checkRs.close();
        checkPs.close();

        // Hapus akun dari database
        String deleteQuery = "DELETE FROM " + type + " WHERE id = ?";
        PreparedStatement deletePs = conn.prepareStatement(deleteQuery);
        deletePs.setInt(1, accountId);
        
        int result = deletePs.executeUpdate();
        deletePs.close();

        if (result > 0) {
            session.setAttribute("message", "Akun " + type + " berhasil dihapus!");
            session.setAttribute("messageType", "success");
        } else {
            session.setAttribute("message", "Gagal menghapus akun!");
            session.setAttribute("messageType", "error");
        }

    } catch (NumberFormatException e) {
        session.setAttribute("message", "ID akun tidak valid!");
        session.setAttribute("messageType", "error");
    } catch (Exception e) {
        session.setAttribute("message", "Terjadi kesalahan pada server: " + e.getMessage());
        session.setAttribute("messageType", "error");
    }

    // Redirect kembali ke halaman register
    response.sendRedirect("../../dashboardadmin.jsp?page=register&type=" + type);
%>
