<%-- 
    Document    : forgotpassword 
    Created on  : May 30, 2025, 1:20:47 
    AM Author   : Arqeta 
    Description : Forgot password page for Arqeta website 

--%> <%@page contentType="text/html" pageEncoding="UTF-8"%>

<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Lupa Kata Sandi - Arqeta</title>
    <link rel="stylesheet" href="../dist/css/auth.css" />
    <link
      href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600&display=swap"
      rel="stylesheet"
    />
    <script src="https://unpkg.com/feather-icons"></script>
  </head>
  <body>
    <!-- Disable right click and F12 -->
    <script>
      // Disable right click
      document.addEventListener("contextmenu", function (e) {
        e.preventDefault();
        showNotification("Klik kanan tidak diizinkan!", "warning");
      });

      // Disable F12, Ctrl+Shift+I, Ctrl+U
      document.addEventListener("keydown", function (e) {
        if (
          e.key === "F12" ||
          (e.ctrlKey && e.shiftKey && e.key === "I") ||
          (e.ctrlKey && e.key === "u")
        ) {
          e.preventDefault();
          showNotification("Inspect element tidak diizinkan!", "warning");
        }
      });

      // Disable copy paste
      document.addEventListener("copy", function (e) {
        e.preventDefault();
        showNotification("Copy tidak diizinkan!", "warning");
      });

      document.addEventListener("paste", function (e) {
        e.preventDefault();
        showNotification("Paste tidak diizinkan!", "warning");
      });
    </script>

    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <h1>Lupa Kata Sandi</h1>
          <p>Masukkan email Anda untuk menerima kode OTP</p>
        </div>

        <!-- Step 1: Email Input -->
        <div id="emailStep" class="auth-step active">
          <form
            id="emailForm"
            action="../process/forgotPasswordProcess.jsp"
            method="post"
          >
            <div class="form-group">
              <label for="email">Email</label>
              <div class="input-group">
                <i data-feather="mail"></i>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  placeholder="Masukkan email Anda"
                />
              </div>
            </div>

            <button type="submit" class="btn-auth">Kirim Kode OTP</button>

            <div class="auth-footer">
              <p>Ingat kata sandi? <a href="signin.jsp">Login</a></p>
            </div>
          </form>
        </div>

        <!-- Step 2: OTP Verification -->
        <div id="otpStep" class="auth-step">
          <form
            id="otpForm"
            action="../process/otpVerificationProcess.jsp"
            method="post"
          >
            <input type="hidden" id="hiddenEmail" name="email" value="" />

            <div class="form-group">
              <label for="otp">Kode OTP</label>
              <div class="input-group">
                <i data-feather="key"></i>
                <input
                  type="text"
                  id="otp"
                  name="otp"
                  required
                  placeholder="Masukkan kode OTP"
                  maxlength="6"
                />
              </div>
              <small class="form-help"
                >Kode OTP telah dikirim ke email Anda</small
              >
            </div>

            <button type="submit" class="btn-auth">Verifikasi OTP</button>

            <div class="auth-footer">
              <p>
                <a href="#" onclick="backToEmailStep()">Kembali ke email</a>
              </p>
            </div>
          </form>
        </div>

        <!-- Step 3: Reset Password -->
        <div id="resetStep" class="auth-step">
          <form
            id="resetForm"
            action="../process/resetPasswordProcess.jsp"
            method="post"
          >
            <input type="hidden" id="resetEmail" name="email" value="" />
            <input type="hidden" id="resetOtp" name="otp" value="" />

            <div class="form-group">
              <label for="newPassword">Kata Sandi Baru</label>
              <div class="input-group">
                <i data-feather="lock"></i>
                <input
                  type="password"
                  id="newPassword"
                  name="newPassword"
                  required
                  placeholder="Masukkan kata sandi baru"
                />
                <button
                  type="button"
                  class="toggle-password"
                  onclick="togglePassword('newPassword')"
                >
                  <i data-feather="eye-off"></i>
                </button>
              </div>
              <div class="password-strength" id="passwordStrength">
                <div class="strength-bar">
                  <div class="strength-fill" id="strengthFill"></div>
                </div>
                <span class="strength-text" id="strengthText">Lemah</span>
              </div>
            </div>

            <div class="form-group">
              <label for="confirmPassword">Konfirmasi Kata Sandi</label>
              <div class="input-group">
                <i data-feather="lock"></i>
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  required
                  placeholder="Konfirmasi kata sandi baru"
                />
                <button
                  type="button"
                  class="toggle-password"
                  onclick="togglePassword('confirmPassword')"
                >
                  <i data-feather="eye-off"></i>
                </button>
              </div>
            </div>

            <button type="submit" class="btn-auth">Reset Kata Sandi</button>

            <div class="auth-footer">
              <p><a href="signin.jsp">Kembali ke Login</a></p>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Notification -->
    <div class="notification" id="notification">
      <div class="notification-content">
        <span class="notification-message" id="notificationMessage"></span>
        <span class="close-notification" onclick="closeNotification()"
          >&times;</span
        >
      </div>
    </div>

    <script>
      // Initialize feather icons
      feather.replace();

      // Get URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const step = urlParams.get('step');
      const email = urlParams.get('email');
      const otp = urlParams.get('otp');

      // Show appropriate step based on URL parameters
      if (step === 'otp' && email) {
          showOtpStep(email);
      } else if (step === 'reset' && email && otp) {
          showResetStep(email, otp);
      }

      // Show notification if exists in session
      <%
      String message = (String) session.getAttribute("message");
      String messageType = (String) session.getAttribute("messageType");
      if (message != null) {
          session.removeAttribute("message");
          session.removeAttribute("messageType");
      %>
          showNotification('<%= message %>', '<%= messageType != null ? messageType : "info" %>');
      <% } %>

      function showOtpStep(emailValue) {
          document.getElementById('emailStep').classList.remove('active');
          document.getElementById('otpStep').classList.add('active');
          document.getElementById('hiddenEmail').value = emailValue;

          // Update header
          document.querySelector('.auth-header h1').textContent = 'Verifikasi OTP';
          document.querySelector('.auth-header p').textContent = 'Masukkan kode OTP yang telah dikirim ke email Anda';
      }

      function showResetStep(emailValue, otpValue) {
          document.getElementById('emailStep').classList.remove('active');
          document.getElementById('otpStep').classList.remove('active');
          document.getElementById('resetStep').classList.add('active');
          document.getElementById('resetEmail').value = emailValue;
          document.getElementById('resetOtp').value = otpValue;

          // Update header
          document.querySelector('.auth-header h1').textContent = 'Reset Kata Sandi';
          document.querySelector('.auth-header p').textContent = 'Masukkan kata sandi baru Anda';
      }

      function backToEmailStep() {
          document.getElementById('otpStep').classList.remove('active');
          document.getElementById('emailStep').classList.add('active');

          // Update header
          document.querySelector('.auth-header h1').textContent = 'Lupa Kata Sandi';
          document.querySelector('.auth-header p').textContent = 'Masukkan email Anda untuk menerima kode OTP';

          // Clear form
          document.getElementById('otp').value = '';
      }

      // Password visibility toggle
      function togglePassword(inputId) {
          const input = document.getElementById(inputId);
          const button = input.parentElement.querySelector('.toggle-password');
          const icon = button.querySelector('i');

          if (input.type === 'password') {
              input.type = 'text';
              icon.setAttribute('data-feather', 'eye');
          } else {
              input.type = 'password';
              icon.setAttribute('data-feather', 'eye-off');
          }
          feather.replace();
      }

      // Password strength checker
      document.getElementById('newPassword').addEventListener('input', function() {
          const password = this.value;
          const strengthFill = document.getElementById('strengthFill');
          const strengthText = document.getElementById('strengthText');

          let strength = 0;
          let strengthLabel = 'Lemah';
          let strengthColor = '#ff4757';

          // Check password criteria
          if (password.length >= 8) strength++;
          if (/[a-z]/.test(password)) strength++;
          if (/[A-Z]/.test(password)) strength++;
          if (/[0-9]/.test(password)) strength++;
          if (/[^A-Za-z0-9]/.test(password)) strength++;

          // Determine strength level
          if (strength >= 4) {
              strengthLabel = 'Kuat';
              strengthColor = '#2ed573';
          } else if (strength >= 3) {
              strengthLabel = 'Sedang';
              strengthColor = '#ffa502';
          }

          // Update UI
          strengthFill.style.width = (strength * 20) + '%';
          strengthFill.style.backgroundColor = strengthColor;
          strengthText.textContent = strengthLabel;
          strengthText.style.color = strengthColor;
      });

      // Form validation
      document.getElementById('resetForm').addEventListener('submit', function(e) {
          const newPassword = document.getElementById('newPassword').value;
          const confirmPassword = document.getElementById('confirmPassword').value;

          if (newPassword !== confirmPassword) {
              e.preventDefault();
              showNotification('Konfirmasi kata sandi tidak sesuai!', 'error');
              return;
          }

          if (newPassword.length < 8) {
              e.preventDefault();
              showNotification('Kata sandi minimal 8 karakter!', 'error');
              return;
          }
      });

      // Notification functions
      function showNotification(message, type = 'info') {
          const notification = document.getElementById('notification');
          const messageElement = document.getElementById('notificationMessage');

          messageElement.textContent = message;
          notification.className = 'notification show ' + type;

          // Auto hide after 5 seconds
          setTimeout(() => {
              closeNotification();
          }, 5000);
      }

      function closeNotification() {
          const notification = document.getElementById('notification');
          notification.classList.remove('show');
      }

      // Prevent form refresh on page reload
      if (window.history.replaceState) {
          window.history.replaceState(null, null, window.location.href);
      }
    </script>
  </body>
</html>
