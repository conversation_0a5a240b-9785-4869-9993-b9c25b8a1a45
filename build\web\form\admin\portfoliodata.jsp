<%--
    Document   : portfoliodata
    Created on : May 30, 2025, 4:01:35 PM
    Author     : Arqeta
    Description: Halaman data portfolio di dashboard admin
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<div class="page-content">
    <div class="page-header">
        <h2>Data Portfolio</h2>
        <div class="action-bar">
            <button class="btn-primary" onclick="openAddPortfolioModal()">
                <i data-feather="plus"></i> Tambah Portfolio
            </button>
        </div>
    </div>

    <div class="table-container">
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nama Portfolio</th>
                        <th>Gambar</th>
                        <th>Deskripsi</th>
                        <th>Waktu Pembuatan</th>
                        <th><PERSON><PERSON><PERSON></th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <%
                        try {
                            // Query untuk mengambil data portfolio berurutan berdasarkan ID
                            PreparedStatement ps = conn.prepareStatement("SELECT * FROM portfolio ORDER BY id ASC");
                            ResultSet rs = ps.executeQuery();

                            while (rs.next()) {
                                int id = rs.getInt("id");
                                String name = rs.getString("name");
                                String image = rs.getString("image");
                                String description = rs.getString("description");
                                String createdAt = rs.getString("created_at");
                                String updatedAt = rs.getString("updated_at");
                    %>
                    <tr>
                        <td><%= id %></td>
                        <td><%= name %></td>
                        <td>
                            <% if (image != null && !image.isEmpty()) { %>
                                <img src="dist/img/<%= image %>" alt="<%= name %>" class="table-image"
                                     onerror="this.src='dist/img/default-portfolio.png';">
                            <% } else { %>
                                <span class="no-image">Tidak ada gambar</span>
                            <% } %>
                        </td>
                        <td class="description-cell">
                            <%= description.length() > 100 ? description.substring(0, 100) + "..." : description %>
                        </td>
                        <td><%= createdAt %></td>
                        <td><%= updatedAt %></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-view" onclick="viewPortfolioDetails(<%= id %>)" title="Lihat Detail">
                                    <i data-feather="eye"></i>
                                </button>
                                <button class="btn-edit" onclick="openEditPortfolioModal(<%= id %>, '<%= name.replace("'", "\\'") %>', '<%= image %>', '<%= description.replace("'", "\\'").replace("\n", "\\n") %>')" title="Edit">
                                    <i data-feather="edit"></i>
                                </button>
                                <button class="btn-delete" onclick="openDeletePortfolioModal(<%= id %>, '<%= name.replace("'", "\\'") %>')" title="Hapus">
                                    <i data-feather="trash-2"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <%
                            }
                            rs.close();
                            ps.close();
                        } catch (SQLException e) {
                            out.println("<tr><td colspan='7'>Error: " + e.getMessage() + "</td></tr>");
                        }
                    %>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Tambah Portfolio -->
<div id="addPortfolioModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Tambah Portfolio</h3>
            <span class="close-modal" onclick="closeModal('addPortfolioModal')">&times;</span>
        </div>
        <form id="addPortfolioForm" method="POST" action="process/admin/addPortfolio.jsp" enctype="multipart/form-data">
            <div class="form-group">
                <label for="addPortfolioName">Nama Portfolio</label>
                <input type="text" id="addPortfolioName" name="name" required>
            </div>
            <div class="form-group">
                <label for="addPortfolioImage">Gambar Portfolio</label>
                <input type="file" id="addPortfolioImage" name="image" accept="image/*" required>
                <small class="form-text">Format yang didukung: JPG, PNG, GIF (Maksimal 10MB)</small>
            </div>
            <div class="form-group">
                <label for="addPortfolioDescription">Deskripsi</label>
                <textarea id="addPortfolioDescription" name="description" rows="4" required style="resize: vertical;"></textarea>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn-secondary" onclick="closeModal('addPortfolioModal')">Batal</button>
                <button type="submit" class="btn-primary">Tambah</button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Edit Portfolio -->
<div id="editPortfolioModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Edit Portfolio</h3>
            <span class="close-modal" onclick="closeModal('editPortfolioModal')">&times;</span>
        </div>
        <form id="editPortfolioForm" method="POST" action="process/admin/editPortfolio.jsp" enctype="multipart/form-data">
            <input type="hidden" id="editPortfolioId" name="id">
            <div class="form-group">
                <label for="editPortfolioName">Nama Portfolio</label>
                <input type="text" id="editPortfolioName" name="name" required>
            </div>
            <div class="form-group">
                <label for="editPortfolioImage">Gambar Portfolio (kosongkan jika tidak ingin mengubah)</label>
                <input type="file" id="editPortfolioImage" name="image" accept="image/*">
                <small class="form-text">Format yang didukung: JPG, PNG, GIF (Maksimal 10MB)</small>
            </div>
            <div class="form-group">
                <label for="editPortfolioDescription">Deskripsi</label>
                <textarea id="editPortfolioDescription" name="description" rows="4" required style="resize: vertical;"></textarea>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn-secondary" onclick="closeModal('editPortfolioModal')">Batal</button>
                <button type="submit" class="btn-primary">Update</button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Konfirmasi Hapus -->
<div id="deletePortfolioModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Konfirmasi Hapus</h3>
            <span class="close-modal" onclick="closeModal('deletePortfolioModal')">&times;</span>
        </div>
        <div class="modal-body">
            <p>Apakah Anda yakin ingin menghapus portfolio "<span id="deletePortfolioName"></span>"?</p>
            <p class="warning-text">Tindakan ini tidak dapat dibatalkan.</p>
        </div>
        <div class="modal-actions">
            <button type="button" class="btn-secondary" onclick="closeModal('deletePortfolioModal')">Batal</button>
            <button type="button" class="btn-danger" onclick="confirmDeletePortfolio()">Hapus</button>
        </div>
    </div>
</div>

<!-- Modal Detail Portfolio -->
<div id="portfolioDetailModal" class="modal">
    <div class="modal-content modal-large">
        <div class="modal-header">
            <h3>Detail Portfolio</h3>
            <span class="close-modal" onclick="closeModal('portfolioDetailModal')">&times;</span>
        </div>
        <div class="modal-body">
            <div id="portfolioDetailContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
        <div class="modal-actions">
            <button type="button" class="btn-secondary" onclick="closeModal('portfolioDetailModal')">Tutup</button>
        </div>
    </div>
</div>

<script>
    let deletePortfolioId = null;

    function openAddPortfolioModal() {
        document.getElementById('addPortfolioModal').style.display = 'block';
    }

    function openEditPortfolioModal(id, name, image, description) {
        document.getElementById('editPortfolioId').value = id;
        document.getElementById('editPortfolioName').value = name;
        document.getElementById('editPortfolioDescription').value = description;
        document.getElementById('editPortfolioModal').style.display = 'block';
    }

    function openDeletePortfolioModal(id, name) {
        deletePortfolioId = id;
        document.getElementById('deletePortfolioName').textContent = name;
        document.getElementById('deletePortfolioModal').style.display = 'block';
    }

    function confirmDeletePortfolio() {
        if (deletePortfolioId) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'process/admin/deletePortfolio.jsp';

            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'id';
            idInput.value = deletePortfolioId;

            form.appendChild(idInput);
            document.body.appendChild(form);
            form.submit();
        }
    }

    function viewPortfolioDetails(id) {
        // Load portfolio details via AJAX
        fetch('process/admin/getPortfolioDetails.jsp?id=' + id)
            .then(response => response.text())
            .then(data => {
                document.getElementById('portfolioDetailContent').innerHTML = data;
                document.getElementById('portfolioDetailModal').style.display = 'block';
            })
            .catch(error => {
                console.error('Error loading portfolio details:', error);
                showNotification('Error loading portfolio details', 'error');
            });
    }

    function closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
        // Reset form jika ada
        const form = document.querySelector('#' + modalId + ' form');
        if (form) {
            form.reset();
        }
    }

    // Tutup modal ketika klik di luar modal
    window.onclick = function(event) {
        const modals = ['addPortfolioModal', 'editPortfolioModal', 'deletePortfolioModal', 'portfolioDetailModal'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (event.target === modal) {
                closeModal(modalId);
            }
        });
    }
</script>
