/**
 * Dashboard JavaScript for Arqeta Website
 * Created on : May 30, 2025
 * Author     : Arqeta
 * Description: Dashboard functionality for Arqeta UI Web and Mobile Service website
 */

// Wait for DOM to be fully loaded
document.addEventListener("DOMContentLoaded", function () {
  // Initialize variables for UI elements
  const sidebar = document.getElementById("sidebar");
  const sidebarToggle = document.getElementById("sidebarToggle");
  const mobileMenuToggle = document.getElementById("mobileMenuToggle");
  const themeToggle = document.getElementById("themeToggle");
  const themeIcon = document.getElementById("themeIcon");
  const themeSwitchSidebar = document.getElementById("themeSwitchSidebar");

  // Initialize Feather icons
  if (typeof feather !== "undefined") {
    feather.replace();
  }

  // Theme toggle functionality
  initThemeToggle();

  // Sidebar toggle functionality
  initSidebar();

  // Modal functionality
  initModals();

  // Form functionality
  initForms();

  // File upload preview
  initFileUploads();

  // Handle notifications from session if they exist
  checkSessionNotifications();
});

// Theme toggle functionality
function initThemeToggle() {
  const themeToggle = document.getElementById("themeToggle");
  const themeIcon = document.getElementById("themeIcon");
  const themeSwitchSidebar = document.getElementById("themeSwitchSidebar");

  // Check for saved theme preference or use system preference
  const savedTheme = localStorage.getItem("theme");
  const systemPrefersDark =
    window.matchMedia &&
    window.matchMedia("(prefers-color-scheme: dark)").matches;

  // Set initial theme
  if (savedTheme === "dark" || (!savedTheme && systemPrefersDark)) {
    document.documentElement.setAttribute("data-theme", "dark");
    if (themeIcon) {
      themeIcon.setAttribute("data-feather", "sun");
      if (typeof feather !== "undefined") {
        feather.replace();
      }
    }
    if (themeSwitchSidebar) {
      themeSwitchSidebar.checked = true;
    }
  }

  // Theme toggle click event
  if (themeToggle) {
    themeToggle.addEventListener("click", toggleTheme);
  }

  // Sidebar theme switch change event
  if (themeSwitchSidebar) {
    themeSwitchSidebar.addEventListener("change", function () {
      const theme = this.checked ? "dark" : "light";
      applyTheme(theme);
    });
  }
}

// Toggle between light and dark theme
function toggleTheme() {
  const currentTheme =
    document.documentElement.getAttribute("data-theme") || "light";
  const newTheme = currentTheme === "light" ? "dark" : "light";

  applyTheme(newTheme);
}

// Apply theme and update UI
function applyTheme(theme) {
  document.documentElement.setAttribute("data-theme", theme);
  localStorage.setItem("theme", theme);

  const themeIcon = document.getElementById("themeIcon");
  const themeSwitchSidebar = document.getElementById("themeSwitchSidebar");

  if (themeIcon) {
    themeIcon.setAttribute("data-feather", theme === "dark" ? "sun" : "moon");
    if (typeof feather !== "undefined") {
      feather.replace();
    }
  }

  if (themeSwitchSidebar) {
    themeSwitchSidebar.checked = theme === "dark";
  }
}

// Enhanced Sidebar functionality dengan animasi hamburger yang presisi
function initSidebar() {
  const sidebar = document.getElementById("sidebar");
  const sidebarToggle = document.getElementById("sidebarToggle");
  const mobileMenuToggle = document.getElementById("mobileMenuToggle");

  // Sidebar toggle untuk desktop view dengan animasi yang enhanced
  if (sidebar && sidebarToggle) {
    sidebarToggle.addEventListener("click", function (e) {
      e.preventDefault();

      // Toggle collapsed state dengan animasi smooth
      sidebar.classList.toggle("collapsed");

      // Simpan state ke localStorage untuk persistensi
      localStorage.setItem(
        "sidebarCollapsed",
        sidebar.classList.contains("collapsed")
      );

      // Enhanced animation untuk hamburger icon
      const icon = this.querySelector("i");
      if (icon) {
        if (sidebar.classList.contains("collapsed")) {
          // Animasi ketika sidebar ditutup - ubah icon menjadi X
          icon.style.transform = "rotate(90deg)";
          icon.style.transition = "transform 0.3s ease";

          setTimeout(() => {
            icon.setAttribute("data-feather", "x");
            if (typeof feather !== "undefined") {
              feather.replace();
            }
            icon.style.transform = "rotate(0deg)";
          }, 150);

          this.style.backgroundColor = "rgba(0, 0, 0, 0.05)";
        } else {
          // Animasi ketika sidebar dibuka - ubah icon kembali ke menu
          icon.style.transform = "rotate(-90deg)";
          icon.style.transition = "transform 0.3s ease";

          setTimeout(() => {
            icon.setAttribute("data-feather", "menu");
            if (typeof feather !== "undefined") {
              feather.replace();
            }
            icon.style.transform = "rotate(0deg)";
          }, 150);

          this.style.backgroundColor = "";
        }
      }

      // Trigger custom event untuk komponen lain
      window.dispatchEvent(
        new CustomEvent("sidebarToggled", {
          detail: { collapsed: sidebar.classList.contains("collapsed") },
        })
      );
    });
  }

  // Mobile menu toggle dengan animasi yang enhanced
  if (sidebar && mobileMenuToggle) {
    mobileMenuToggle.addEventListener("click", function (e) {
      e.preventDefault();

      // Toggle mobile-active state
      sidebar.classList.toggle("mobile-active");

      // Enhanced animation untuk mobile hamburger
      const icon = this.querySelector("i");
      if (icon) {
        if (sidebar.classList.contains("mobile-active")) {
          // Animasi ketika mobile menu dibuka - ubah icon menjadi X
          icon.style.transform = "rotate(90deg)";
          icon.style.transition = "transform 0.3s ease";

          setTimeout(() => {
            icon.setAttribute("data-feather", "x");
            if (typeof feather !== "undefined") {
              feather.replace();
            }
            icon.style.transform = "rotate(0deg)";
          }, 150);

          this.style.backgroundColor = "rgba(0, 0, 0, 0.1)";
        } else {
          // Animasi ketika mobile menu ditutup - ubah icon kembali ke menu
          icon.style.transform = "rotate(-90deg)";
          icon.style.transition = "transform 0.3s ease";

          setTimeout(() => {
            icon.setAttribute("data-feather", "menu");
            if (typeof feather !== "undefined") {
              feather.replace();
            }
            icon.style.transform = "rotate(0deg)";
          }, 150);

          this.style.backgroundColor = "";
        }
      }

      // Trigger custom event
      window.dispatchEvent(
        new CustomEvent("mobileMenuToggled", {
          detail: { active: sidebar.classList.contains("mobile-active") },
        })
      );
    });
  }

  // Tutup sidebar ketika menu item diklik pada mobile
  const menuItems = document.querySelectorAll(".sidebar-menu a");
  if (menuItems.length > 0) {
    menuItems.forEach((item) => {
      item.addEventListener("click", function () {
        if (
          window.innerWidth < 768 &&
          sidebar.classList.contains("mobile-active")
        ) {
          sidebar.classList.remove("mobile-active");

          // Reset mobile hamburger animation
          if (mobileMenuToggle) {
            const icon = mobileMenuToggle.querySelector("i");
            if (icon) {
              icon.style.transform = "rotate(0deg)";
              icon.style.transition = "transform 0.3s ease";
              icon.setAttribute("data-feather", "menu");
              if (typeof feather !== "undefined") {
                feather.replace();
              }
              mobileMenuToggle.style.backgroundColor = "";
            }
          }
        }
      });
    });
  }

  // Tutup sidebar ketika klik di luar pada mobile
  document.addEventListener("click", function (event) {
    if (
      window.innerWidth < 768 &&
      sidebar &&
      sidebar.classList.contains("mobile-active") &&
      !sidebar.contains(event.target) &&
      !mobileMenuToggle.contains(event.target)
    ) {
      sidebar.classList.remove("mobile-active");

      // Reset mobile hamburger animation
      if (mobileMenuToggle) {
        const icon = mobileMenuToggle.querySelector("i");
        if (icon) {
          icon.style.transform = "rotate(0deg)";
          icon.style.transition = "transform 0.3s ease";
          icon.setAttribute("data-feather", "menu");
          if (typeof feather !== "undefined") {
            feather.replace();
          }
          mobileMenuToggle.style.backgroundColor = "";
        }
      }
    }
  });

  // Restore sidebar state dari localStorage
  const savedState = localStorage.getItem("sidebarCollapsed");
  if (savedState === "true" && sidebar && sidebarToggle) {
    sidebar.classList.add("collapsed");
    const icon = sidebarToggle.querySelector("i");
    if (icon) {
      icon.setAttribute("data-feather", "x");
      if (typeof feather !== "undefined") {
        feather.replace();
      }
      sidebarToggle.style.backgroundColor = "rgba(0, 0, 0, 0.05)";
    }
  }

  // Handle window resize untuk responsive behavior
  window.addEventListener("resize", function () {
    if (
      window.innerWidth >= 768 &&
      sidebar.classList.contains("mobile-active")
    ) {
      sidebar.classList.remove("mobile-active");

      // Reset mobile hamburger animation
      if (mobileMenuToggle) {
        const icon = mobileMenuToggle.querySelector("i");
        if (icon) {
          icon.style.transform = "rotate(0deg)";
          icon.style.transition = "transform 0.3s ease";
          icon.setAttribute("data-feather", "menu");
          if (typeof feather !== "undefined") {
            feather.replace();
          }
          mobileMenuToggle.style.backgroundColor = "";
        }
      }
    }
  });
}

// Modal functionality
function initModals() {
  // Open modal buttons
  const modalOpenButtons = document.querySelectorAll("[data-modal-target]");
  const modalCloseButtons = document.querySelectorAll("[data-close-modal]");
  const modals = document.querySelectorAll(".modal");

  // Open modals
  modalOpenButtons.forEach((button) => {
    button.addEventListener("click", () => {
      const modalId = button.getAttribute("data-modal-target");
      const modal = document.getElementById(modalId);
      if (modal) {
        openModal(modal);
      }
    });
  });

  // Close modals with close button
  modalCloseButtons.forEach((button) => {
    button.addEventListener("click", () => {
      const modal = button.closest(".modal");
      if (modal) {
        closeModal(modal);
      }
    });
  });

  // Close modal when clicking outside of modal content
  modals.forEach((modal) => {
    modal.addEventListener("click", (e) => {
      if (e.target === modal) {
        closeModal(modal);
      }
    });
  });

  // Close modal with ESC key
  document.addEventListener("keydown", (e) => {
    if (e.key === "Escape") {
      modals.forEach((modal) => {
        if (modal.classList.contains("active")) {
          closeModal(modal);
        }
      });
    }
  });

  // Edit buttons (they need special handling to load data)
  const editButtons = document.querySelectorAll(".btn-action.edit");

  editButtons.forEach((button) => {
    button.addEventListener("click", () => {
      const id = button.getAttribute("data-id");
      const type = button.getAttribute("data-type");
      const modalId = `#${type}Modal`;
      const modal = document.querySelector(modalId);

      if (modal) {
        // Change modal title and button text to "Edit"
        const modalTitle = modal.querySelector(".modal-header h2");
        const submitButton = modal.querySelector(
          ".modal-footer button[type='submit']"
        );

        if (modalTitle)
          modalTitle.textContent = `Edit ${
            type.charAt(0).toUpperCase() + type.slice(1)
          }`;
        if (submitButton) submitButton.textContent = "Simpan Perubahan";

        // Set form action to edit endpoint
        const form = modal.querySelector("form");
        if (form) {
          const originalAction =
            form.getAttribute("data-original-action") || form.action;
          if (!form.getAttribute("data-original-action")) {
            form.setAttribute("data-original-action", originalAction);
          }

          // Update form action for edit
          form.action = `../process/edit${
            type.charAt(0).toUpperCase() + type.slice(1)
          }Process.jsp`;

          // Add hidden id field if not exists
          let idField = form.querySelector("input[name='id']");
          if (!idField) {
            idField = document.createElement("input");
            idField.type = "hidden";
            idField.name = "id";
            form.appendChild(idField);
          }

          idField.value = id;
        }

        // Load data for this item (in real app, this would be an AJAX call)
        loadItemData(type, id, modal);

        openModal(modal);
      }
    });
  });
}

// Open modal
function openModal(modal) {
  modal.classList.add("active");
  document.body.style.overflow = "hidden";
}

// Close modal
function closeModal(modal) {
  modal.classList.remove("active");
  document.body.style.overflow = "";

  // Reset form if exists
  const form = modal.querySelector("form");
  if (form) {
    form.reset();

    // Reset form action to original (for add new item)
    const originalAction = form.getAttribute("data-original-action");
    if (originalAction) {
      form.action = originalAction;
    }

    // Remove id field if exists (for edit)
    const idField = form.querySelector("input[name='id']");
    if (idField) {
      idField.remove();
    }

    // Reset modal title and button text
    const modalTitle = modal.querySelector(".modal-header h2");
    const submitButton = modal.querySelector(
      ".modal-footer button[type='submit']"
    );
    const type = modal.id.replace("Modal", "");

    if (modalTitle)
      modalTitle.textContent = `Tambah ${
        type.charAt(0).toUpperCase() + type.slice(1)
      } Baru`;
    if (submitButton) submitButton.textContent = "Tambah";

    // Reset file upload preview
    const filePreviewContainer = modal.querySelector(".file-preview-container");
    if (filePreviewContainer) {
      filePreviewContainer.innerHTML = "";
      filePreviewContainer.style.display = "none";
    }
  }
}

// Load item data for edit modal
function loadItemData(type, id, modal) {
  // This would be an AJAX call in a real application
  // For now, we'll just simulate by populating form fields from the row data
  // In real implementation, this would fetch data from the server

  // Find the table row with this ID
  const row = document.querySelector(`tr[data-id="${id}"]`);
  if (!row) return;

  // Get form in modal
  const form = modal.querySelector("form");
  if (!form) return;

  // Get all form inputs
  const inputs = form.querySelectorAll(
    "input:not([type='file']), textarea, select"
  );

  // For each input, try to find matching data cell and populate
  inputs.forEach((input) => {
    const name = input.name;
    if (name === "id") return; // Skip id field, already set
    if (name === "password" || name === "confirmPassword") return; // Skip password fields

    // Find cell with matching data attribute
    const cell = row.querySelector(`td[data-field="${name}"]`);
    if (cell) {
      input.value = cell.textContent.trim();
    }
  });

  // Handle image preview for file inputs
  const fileInputs = form.querySelectorAll("input[type='file']");
  fileInputs.forEach((fileInput) => {
    const name = fileInput.name;
    const cell = row.querySelector(`td[data-field="${name}"]`);

    if (cell) {
      const imageSrc = cell.querySelector("img")?.src || "";
      if (imageSrc) {
        const previewContainer = modal.querySelector(".file-preview-container");
        if (previewContainer) {
          previewContainer.innerHTML = `<img src="${imageSrc}" class="file-preview" alt="Preview">`;
          previewContainer.style.display = "block";

          // Add a hidden input to keep the original image if no new file is uploaded
          const originalImageInput = document.createElement("input");
          originalImageInput.type = "hidden";
          originalImageInput.name = `original_${name}`;
          originalImageInput.value = imageSrc.split("/").pop(); // Extract filename from path
          form.appendChild(originalImageInput);
        }
      }
    }
  });
}

// Form functionality
function initForms() {
  // Password toggle functionality
  const passwordToggles = document.querySelectorAll(".password-toggle");
  passwordToggles.forEach((toggle) => {
    toggle.addEventListener("click", function () {
      const passwordInput = this.previousElementSibling;
      const icon = this.querySelector("i");

      if (passwordInput.type === "password") {
        passwordInput.type = "text";
        icon.setAttribute("data-feather", "eye");
      } else {
        passwordInput.type = "password";
        icon.setAttribute("data-feather", "eye-off");
      }

      if (typeof feather !== "undefined") {
        feather.replace();
      }
    });
  });

  // Form validation for password match
  const passwordForms = document.querySelectorAll(
    'form:has(input[type="password"])'
  );
  passwordForms.forEach((form) => {
    form.addEventListener("submit", function (event) {
      const password = form.querySelector('input[name="password"]');
      const confirmPassword = form.querySelector(
        'input[name="confirmPassword"]'
      );

      if (
        password &&
        confirmPassword &&
        password.value !== confirmPassword.value
      ) {
        event.preventDefault();
        showNotification(
          "Kata sandi dan konfirmasi kata sandi tidak cocok.",
          "error"
        );
      }
    });
  });

  // Delete confirmation
  const deleteButtons = document.querySelectorAll("[data-delete]");
  deleteButtons.forEach((button) => {
    button.addEventListener("click", function (event) {
      const confirmDelete = confirm(
        "Apakah Anda yakin ingin menghapus data ini?"
      );
      if (!confirmDelete) {
        event.preventDefault();
      }
    });
  });
}

// File upload functionality
function initFileUploads() {
  const fileInputs = document.querySelectorAll('input[type="file"]');

  fileInputs.forEach((input) => {
    const previewContainerId = input.dataset.previewContainer;
    const previewContainer = document.getElementById(previewContainerId);

    if (input && previewContainer) {
      input.addEventListener("change", function () {
        previewContainer.innerHTML = "";

        if (this.files && this.files[0]) {
          const reader = new FileReader();

          reader.onload = function (e) {
            const img = document.createElement("img");
            img.src = e.target.result;
            img.className = "file-preview";
            previewContainer.appendChild(img);
            previewContainer.style.display = "block";
          };

          reader.readAsDataURL(this.files[0]);
        }
      });
    }
  });
}

// Check session notifications
function checkSessionNotifications() {
  // Check if there are session messages
  const message = document.getElementById("sessionMessage");
  const messageType = document.getElementById("messageType");

  if (message && messageType) {
    const messageText = message.value;
    const messageTypeValue = messageType.value;

    if (messageText) {
      showNotification(messageText, messageTypeValue);
    }
  }
}

// Show notification
function showNotification(message, type = "info") {
  const notification = document.getElementById("notification");
  if (!notification) {
    // Create notification element if it doesn't exist
    createNotificationElement();
    return setTimeout(() => showNotification(message, type), 100);
  }

  const notificationMessage = document.getElementById("notificationMessage");

  notificationMessage.textContent = message;
  notification.className = "notification";
  notification.classList.add(`notification-${type}`);
  notification.classList.add("show");

  // Auto hide after 5 seconds
  setTimeout(() => {
    closeNotification();
  }, 5000);
}

// Close notification
function closeNotification() {
  const notification = document.getElementById("notification");
  if (notification) {
    notification.classList.remove("show");
  }
}

// Create notification element
function createNotificationElement() {
  const notificationDiv = document.createElement("div");
  notificationDiv.id = "notification";
  notificationDiv.className = "notification";

  const notificationContent = document.createElement("div");
  notificationContent.className = "notification-content";

  const notificationMessage = document.createElement("span");
  notificationMessage.id = "notificationMessage";
  notificationMessage.className = "notification-message";

  const closeButton = document.createElement("span");
  closeButton.className = "close-notification";
  closeButton.innerHTML = "&times;";
  closeButton.onclick = closeNotification;

  notificationContent.appendChild(notificationMessage);
  notificationContent.appendChild(closeButton);
  notificationDiv.appendChild(notificationContent);

  document.body.appendChild(notificationDiv);
}

// ===== User Dashboard Specific Functions =====

// Global modal functions for user dashboard
function openModal(modalId) {
  const modal =
    typeof modalId === "string" ? document.getElementById(modalId) : modalId;
  if (modal) {
    modal.classList.add("active");
    document.body.style.overflow = "hidden";
  }
}

function closeModal(modalId) {
  const modal =
    typeof modalId === "string" ? document.getElementById(modalId) : modalId;
  if (modal) {
    modal.classList.remove("active");
    document.body.style.overflow = "";

    // Reset form if exists
    const form = modal.querySelector("form");
    if (form) {
      form.reset();
    }
  }
}

// Enhanced notification system for user dashboard
function showNotificationEnhanced(message, type = "info", duration = 5000) {
  // Remove existing notification if any
  const existingNotification = document.getElementById("notification");
  if (existingNotification) {
    existingNotification.remove();
  }

  // Create notification element
  const notification = document.createElement("div");
  notification.id = "notification";
  notification.className = `notification ${type}`;

  // Set notification content
  notification.innerHTML = `
    <div class="notification-content">
      <div class="notification-icon">
        <i data-feather="${getNotificationIcon(type)}"></i>
      </div>
      <span class="notification-message">${message}</span>
      <button class="close-notification" onclick="closeNotification()">
        <i data-feather="x"></i>
      </button>
    </div>
  `;

  // Add to page
  document.body.appendChild(notification);

  // Initialize feather icons
  if (typeof feather !== "undefined") {
    feather.replace();
  }

  // Show notification with animation
  setTimeout(() => {
    notification.classList.add("show");
  }, 100);

  // Auto hide after duration
  if (duration > 0) {
    setTimeout(() => {
      closeNotification();
    }, duration);
  }
}

function getNotificationIcon(type) {
  switch (type) {
    case "success":
      return "check-circle";
    case "error":
      return "alert-circle";
    case "warning":
      return "alert-triangle";
    case "info":
    default:
      return "info";
  }
}

// Cart functionality for user dashboard
function updateCartCount() {
  fetch("../process/user/updateCart.jsp", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    body: "action=count",
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        const cartCountElements = document.querySelectorAll(".cart-count");
        cartCountElements.forEach((element) => {
          element.textContent = data.totalItems || 0;
        });
      }
    })
    .catch((error) => {
      console.error("Error updating cart count:", error);
    });
}

// Form validation helpers
function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

function validatePassword(password) {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
}

// Enhanced form handling for user dashboard
function handleFormSubmission(form, successCallback, errorCallback) {
  const formData = new FormData(form);

  fetch(form.action, {
    method: "POST",
    body: formData,
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        if (successCallback) {
          successCallback(data);
        } else {
          showNotification(data.message || "Operasi berhasil", "success");
        }
      } else {
        if (errorCallback) {
          errorCallback(data);
        } else {
          showNotification(data.message || "Terjadi kesalahan", "error");
        }
      }
    })
    .catch((error) => {
      console.error("Error:", error);
      if (errorCallback) {
        errorCallback({ message: "Terjadi kesalahan jaringan" });
      } else {
        showNotification("Terjadi kesalahan jaringan", "error");
      }
    });
}

// Loading state management
function setLoadingState(element, isLoading) {
  if (isLoading) {
    element.disabled = true;
    element.innerHTML =
      '<i data-feather="loader" class="spinning"></i> Loading...';
  } else {
    element.disabled = false;
    element.innerHTML = element.getAttribute("data-original-text") || "Submit";
  }

  if (typeof feather !== "undefined") {
    feather.replace();
  }
}

// Back to top functionality
function initBackToTop() {
  const backToTopBtn = document.getElementById("backToTopBtn");

  if (backToTopBtn) {
    window.addEventListener("scroll", function () {
      if (window.pageYOffset > 300) {
        backToTopBtn.classList.add("show");
      } else {
        backToTopBtn.classList.remove("show");
      }
    });

    backToTopBtn.addEventListener("click", function () {
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    });
  }
}
