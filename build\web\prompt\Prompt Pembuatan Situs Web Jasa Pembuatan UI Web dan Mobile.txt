Sekarang kamu adalah seorang expert di bidang Web Programming Enterprise. Buatkan situs web mengenai “Jasa Pembuatan UI Web dan Mobile” dengan menggunakan teknologi dari bahasa pemrograman Java yaitu JSP (JavaServerPage) di proyek ini. Pastikan semua file kode program menggunakan JSP, jika anda sedang membuat kode program dan kode program tersebut menggunakan teridentifikasi file Java ubahlah ke file JSP jangan sampai ada file Java di dalam project ini. Perhatikan dalam pembuatan kode program JSP, jika file dan directory belum tersedia di project ini anda sebagai programmer boleh menambahkan file dan directory yang belum tersedia, akan tetapi tetap berdasarkan permintaan developer kepada anda. Berikut ini adalah Spesifikasi, Database, File JSP, Directory, Halaman Login, Halaman Register, Halaman Lupa Kata Sandi, Halaman Utama. Navigation Bar, Dashboard Admin, Dashboard User, Slider Humburger Menu (<PERSON><PERSON> Utama, Halaman Dashboard Admin, dan <PERSON> Dashboard User), Fitur, Warna, Icon, Hover, Animasi, Parallax, Font, Notifikasi, Security, Config Firebase untuk fitur Login with Google, dan <PERSON>ja situs web yang diminta oleh developer kepada anda:



Spesifikasi situs web:
- Pastikan web.xml berfungsi dengan baik dan memenuhi semua kebutuhan dalam project ini
- Pastikan <%@page contentType="text/html" pageEncoding="UTF-8"%> berfungsi dengan baik agar bisa menampilkan konten HTML yang sesuai pada situs web. Jika perlu diubah sesuaikan kembali dan jika terdapat error segera perbaiki secara komprehensif
- Pastikan <%@page contentType="application/json" pageEncoding="UTF-8"%> berfungsi dengan baik agar bisa menampilka konten JSON yang sesuai pada situs web. Jika perlu diubah sesuaikan kembali dan jika terdapat error segera perbaiki secara komprehensif
- Nama situs web bernama Arqeta mengenai Jasa Pembuatan UI Web dan Mobile
- Pastikan semua desain dan tata letak sejajar agar presisi
- Perhatikan tampilan di setiap halaman agar pengguna tidak bingung dalam menggunakannya
- Pastikan tidak menggunakan icon yang berbentuk emoticon
- Pastikan ketika terdapat icon. Icon tersebut terstrukurnya tidak berantakan
- Pastikan send direct pada semua Halaman situs web terkirim pada alamatnya dan berfungsi dengan baik. Jangan sampai ia bertabrakan agar tidak terjadi looping
- Terapkan agar ketika berganti halaman dikasih loading tetapi tetap mempunyai respon time yang baik dan tangani agar ketika berganti halaman harus sesuai alur pada sistem situs web 
- Terapkan konsep Estetika Desain
- Terapkan konsep Perfeksionis Desain
- Terapkan konsep Minimalis Desain
- Terapkan konsep parallax
- Terapkan konsep Firebase
- Terapkan konsep cloudflare
- Terapkan konsep CRUD
- Pastikan semua library yang dibutuhkan terpenuhi
- Pastikan situs web support di region manapun itu
- Pastikan situs web support dengan bahasa manapun itu
- Pastikan HTML, CSS, Javascript, SQL, dan XML berjalan dengan baik sesuai semestinya
- Pastikan semua fungsi yang menggunakan database berjalan dengan baik sesuai semestinya 
- Situs web ini menggunakan Bahasa Indonesia yang mudah dipahami oleh pembaca agar tidak terjadi kesalahpahaman dalam menjelaskan
- Menggunakan model AI dari Anthropic yaitu Claude Opus 4 (Thinking) dalam pembuatan project situs web ini
- Pastikan mempunyai respon time yang baik
- Terapkan konsep smooth scrolling
- Pastikan menggunakan HTTP bukan HTTPS (karena masih tahap pengembangan)
- Pastikan situs web kompatibel di spesifikasi device manapun (maupun itu di spesifikasi rendah sekalipun)
- Jika halaman pada situs web ini tidak sesuai dengan apa sudah diminta oleh developer kepada anda, anda sebagai programmer boleh untuk mengubahnya agar lebih sesuai dengan apa yang diminta oleh developer kepada anda
- Jika file sudah ada tidak perlu dibuatkan kembali anda hanya menambahkan source kode nya saja, akan tetapi ketika file belum ada dibuatkan dan tambahkan source kode nya
- Sesuaikan database yang sudah disertakan di project ini di directory “web/database”
- Pastikan menggunakan Jakarta Servlet bukan Javax Servlet
- Sesuaikan directory yang sudah disertakan di project ini bernama “Directory.png”
- Menggunakan teknologi dari bahasa pemrograman Java yaitu (JavaServerPage)
- Menggunakan teknologi HTML, CSS, Javascript dan SQL
- Menggunakan Vanilla CSS
- Database bernama arqeta yang terdiri dari table admin, user, services, portfolio, blog, dan contact
- Pengelolaan database menggunakan PHPMyAdmin dengan Database MySQL
- Menggunakan MySQL Connector J versi 9.3.0
- Menggunakan Glassfish server versi 7.0.0
- Menggunakan server dari Laragon
- Terapkan konsep Basic HTML
- Terapkan konsep Semantic elements
- Terapkan konsep Form validation
- Terapkan konsep SVG & canvas
- Terapkan konsep Information architecture
- Terapkan konsep Accessibility
- Terapkan konsep SEO yang baik
- Terapkan konsep Basic CSS
- Terapkan konsep CSS layouting
- Terapkan konsep Filter, transition & animation
- Terapkan konsep Responsive design
- Terapkan konsep Modern CSS
- Terapkan konsep CSS architecture
- Terapkan konsep Basic concepts
- Terapkan konsep DOM (Document Object Model)
- Terapkan konsep ES6 + modular JavaScript
- Terapkan konsep Asynchronous JavaScript
- Terapkan konsep Fetch API
- Terapkan konsep Basic Programming
- Terapkan konsep Fundamental UI/UX Concepts
- Terapkan konsep Paradigma Procedural Programming
- Terapkan konsep Paradigma OOP (Object Oriented Programming)
- Terapkan konsep Paradigma Functional Programming
- Terapkan konsep Database Normalization Design
- Terapkan konsep Database ERD Design
- Terapkan konsep Data Definition Language
- Terapkan konsep Data Manipulation Language
- Terapkan konsep Data Control Language
- Terapkan konsep ORM (Object Relational Mapping)
- Terapkan konsep Index & Transaction
- Terapkan konsep ACID (Atomicity, Consistency, Isolation, Durability)
- Terapkan konsep REST API
- Terapkan JSON (JavaScript Object Notation)
- Terapkan konsep Basic Auth
- Terapkan konsep Token Auth
- Terapkan konsep Oauth
- Terapkan konsep JWT (JSON Web Token)
- Terapkan konsep Algorithm & Data Structure
- Terapkan konsep Software Development Methodologies
- Terapkan konsep Design Patterns & Principles
- Terapkan konsep Architectural Pattern
- Terapkan konsep Hashing & Encryption
- HTTPS
- SSL (Select Sockets Layer)
- Terapkan konsep CORS (Cross-Origin Resources Sharing)
- Terapkan konsep Content Security Policy (CSP)
- Terapkan konsep Integration Testing
- Terapkan konsep Unit Testing
- Terapkan konsep Functional Testing

Database situs web:
- Jika ada table database yang belum tersedia silahkan tambahkan
- Username: root
- Password: tidak ada
- Url: ******************************************************************************************************
- Table: admin, user, admin_google, user_google, services, transaction, portfolio, blog, contact, dan otp_log
- Table admin: id, name, username, email, password, created_at, updated_at
- Table user: id, name, username, email, password, created_at, updated_at
- Table admin_google: id, name, email, admin_id, photo_url, created_at, last_login, updated_at
- Table user_google: id, name, email, user_id, photo_url, created_at, last_login, updated_at
- Table services: id, name, images, quantity, price, created_at, updated_at
- Table transaction: id, name, amount, unit, total, user_id, service_id, status, price, created_at, updated_at
- Table portfolio: id, name, image, description, created_at, updated_at
- Table blog: id, title, image, content, created_at, updated_at
- Table contact: id, name, email, subject, message, status, created_at, updated_at
- Table otp_log: id, email, otp, created_at, used

File JSP situs web:
- connection.jsp  Koneksi pada database
- home.jsp  Halaman Utama
- dashboardadmin.jsp  Halaman Dashboard untuk Admin
- dashboarduser.jsp  Halaman Dashboard untuk User
- signin.jsp  Halaman form Login
- signup.jsp  Halaman form Register
- forgotpassword.jsp  Halaman Lupa kata sandi
- signupdata  Halaman Data Register Admin dan User di bagian Dashboard Admin
- servicesdata.jsp  Halaman Data Layanan di bagian Dashboard Admin
- transactionadata  Halaman Data Transaksi di bagian Dashboard Admin
- portfoliodata.jsp  Halaman Data Portfolio di bagian Dashboard Admin
- blogdata.jsp  Halaman Data Blog di bagian Dashboard Admin
- contactdata.jsp  Halaman Data Kontak di bagian Dashboard Admin
- loginwithgoogle.jsp  Halaman Login with Google
- googleaccountdata.jsp  Halaman Data Akun Google Admin dan User di bagian Dashboard Admin
- otplogdata.jsp  Halaman Data Riwayat OTP user dan admin di bagian Dashboard Admin
- transaction.jsp  Halaman membeli layanan untuk User
- basket.jsp  Halaman Keranjang di bagian Dashboard User
- account.jsp  Halaman Akun di bagian Dashboard User
- transactionhistory.jsp  Halaman Riwayat Trasanksi di bagian Dashboard User


Directory pada situs web:
- web/config  Semua koneksi database pada situs web
- web/form  Semua halaman pada situs web
- web/form/admin  Semua halaman admin pada situs web
- web/form/user  Semua halaman user pada situs web
- web/dist/img  Semua IMG pada situs web
- web/dist/svg  Semua SVG pada situs web
- web/dist/js  Semua file Javascript pada situs web
- web/dist/css  Semua file CSS pada situs web
- web/database  Semua table Database pada situs web
- web/error  Semua Halaman Error pada situs web
- web/process  Semua Halaman yang membutuhkan fitur pemprosesan pada situs web
- web/process/admin  Semua Halaman Admin yang membutuhkan fitur pemprosesan pada situs web
- web/process/user  Semua Halaman User yang membutuhkan fitur pemprosesan pada situs web

Halaman Login situs web:
- Nama dan Username (pada kolom bisa mengisikan Nama atau Username)
- Email
- Kata sandi (include icon mata)
- Login with Google

Halaman Register situs web:
- Nama
- Username
- Email
- Kata sandi (include icon mata dan indikator kekuatan kata sandi)
- Konfirmasi kata sandi (include icon mata dan indikator kekuatan kata sandi)

Halaman Lupa Kata Sandi situs web:
- Email

Halaman Utama situs web:
- Hero Section (Kalimat penyambut untuk calon pelanggan dan button Login)
- Tentang Kami (Sub nama ”Siapa Kami?” dan Ingin ”Mengenal Kami Lebih Jauh?”)
- Layanan (Semua layanan dan paket dari arqeta)
- Portfolio (Semua Portfolio yang sudah dikerjakan oleh Arqeta)
- Blog (Seputar Layanan arqeta dan edukasi UI Web dan Mobile)
- Partnership (Anthropic, Figma, Google)
- Kontak
- Footer (Nama situs web, Layanan, Cari apa?, Icon media sosial, dan Copyright)  

Navigation Bar situs web:
- Nama situs web ”Arqeta”
- Tentang Kami
- Layanan ( dropdown ”Layanan Pembuatan UI Web” dan ”Layanan Pembuatan UI Mobile”)
- Portfolio
- Blog
- Partnership
- Kontak
- Button Login
- Icon fitur Light Mode dan Dark Mode (icon Matahari dan icon Bulan Sabit)

Dashboard Admin situs web:
- Beranda (Menampilkan output dari Data Register, Data Register Google, Data Layanan, Data Portfolio, Data Blog, Data Kontak, dan Data Riwayat OTP)
- Data Register (Menampilkan Output dari database table admin dan user)
- Data Akun Google (Menampilkan Output dari database table admin_google dan user_google)
- Data Layanan (Menampilkan Output dari database table services)
- Data Transaksi (Menampilkan Output dari database table transaction)
- Data Portfolio (Menampilkan Output dari database table portfolio)
- Data Blog (Menampilkan Output dari database table blog)
- Data Kontak (Menampilkan Output dari database table contact)
- Data Riwayat OTP (Menampilkan Output dari database otp_log)
- Button Logout
- Icon toggle switch fitur Light Mode dan Dark Mode

Dashboard User situs web:
- Beranda
- Akun
- Keranjang
- Riwayat Transaksi
- Button Logout
- Icon toggle switch fitur Light Mode dan Dark Mode

Slider Humburger Menu pada Halaman Utama situs web:
- Tentang Kami
- Layanan Pembuatan UI Web
- Layanan Pembuatan UI Mobile
- Portfolio
- Blog
- Partnership
- Kontak
- Login
- Icon toggle switch fitur Light Mode dan Dark Mode

Slider Humburger Menu pada Halaman Dashboard Admin situs web:
- Beranda
- Data Register Admin
- Data Register User
- Data Akun Google Admin
- Data Akun Google User
- Data Layanan
- Data Transaksi
- Data Portfolio
- Data Blog
- Data Kontak
- Data Riwayat OTP
- Logout
- Icon toggle switch fitur Light Mode dan Dark Mode

Slider Humburger Menu pada Halaman Dashboard User situs web:
- Beranda
- Akun
- Keranjang
- Riwayat Transaksi
- Logout

Fitur situs web:
- Responsif terhadap device Laptop, Monitor, Tablet, dan Handphone. Untuk ratio atau ukuran pastikan sesuai dengan standarisasi media query.
- Humburger Menu hanya tersedia ketika berbentuk ratio atau ukuran Handphone. Terapkan di semua Halaman situs web
- Pastikan di semua ukuran isi konten Navigation Bar, Dashboard User, Dashboard Admin, Slider Humburger Menu pada Halaman Utama, Slider Humburger Menu pada Halaman Dashboard Admin, dan Slider Humburger Menu pada Halaman Dashboard User tidak berdekatan agar tidak terlihat sempit dan sejajarkan agar presisi. Jika kalimat terlalu besar dan menjadi paragraf atau ”...” kecilkan ukuran kalimat
- Pada Halaman Utama ketika di scroll ke bawah Navigation Bar akan menjadi transparan atau efek kaca, akan tetapi ketika Navigation kembali ke posisi awalnya ia menjadi normal
- Pada Halaman Utama ketika di scroll ke bawah memunculkan button icon anak panah untuk kembali ke halaman paling atas, akan tetapi ketika sudah mencapai halaman paling atas button icon anak panah tersebut menghilang
- Fitur pada Light Mode – Dark Mode untuk yang berbentuk icon Matahari dan Bulan Sabit. Ketika icon Bulan Sabit di klik ia akan berubah menjadi Dark Mode dan icon berubah menjadi Matahari. Ketika icon Matahari di klik ia akan berubah menjadi Light Mode dan icon berubah menjadi Bulan Sabit. Akan tetapi defaultnya adalah Light Mode
- Icon Humburger Menu ketika di klik ia akan menjadi Slider Humburger Menu yang berbentuk box dan di letakkan dibawah icon Humburger Menu
- Layanan pada Navigation Bar ketika di klik akan memunculkan dropdown yang isinya ”Layanan Pembuatan UI Web” dan ”Layanan Pembuatan UI Mobile”. Buatkan icon anak panah ke bawah untuk membuka dropdown yang icon anak panah tersebut di letakkan di samping kalimat Layanan. Pastikan perlu di klik anak panahnya untuk membukanya bukan di hover
- Buatkan fitur Light Mode – Dark Mode pada Navigation Bar, Slider Humburger Menu di semua halaman yang mempunyainya (Halaman Utama, Halaman Dashboard Admin, dan Halaman Dashboard User), Dashboard Admin, dan Dashboard User. Untuk Navigation Bar berbentuk icon matahari dan icon bulan sabit sedangkan untuk Slider Humburger Menu (Halaman Utama, Halaman Dashboard Admin, dan Halaman Dashboard User), Dashboard Admin dan Dashboard User berbentuk icon toggle switch
- Sediakan background gambar pada Hero Section, akan tetapi gambar tersebut diletakkan oleh developer saja, tugas anda hanya menyediakannya saja
- Ketika ingin Login dan mengisikannya sebagai user sesuai pada database table ”user”  ia akan memasuki halaman ”home.jsp”, akan tetapi ketika Login dan mengisikannya sebagai admin sesuai pada database table ”admin” ia akan memasuki halaman ”dashboardadmin.jsp”. Pastikan tetap dalam satu form yang sama
- Buatkan fitur Edit dan Hapus pada Halaman Data Register, Halaman Data Layanan, Halaman Data Portfolio, dan Halaman Data Blog di bagian opsi pada Dashboard Admin dan berbentuk pop-up ketika ingin mengedit dan menghapus. Pastikan pada Halaman Data Register, Halaman Data Layanan, Halaman Data Portfolio, dan Halaman Data Blog OTP ketika diedit dan dihapus tercatat di dalam database
- Buatkan fitur Hapus pada Halaman Data Akun Google, Halaman Data Transaksi, Halaman Data Kontak, dan Halaman Data Riwayat OTP di bagian opsi pada Dashboard Admin dan berbentuk pop-up ketika ingin menghapus. Pastikan pada Halaman Data Akun Google, Halaman Data Riwayat Transaksi, Halaman Data Kontak, dan Data Halaman Riwayat OTP ketika dihapus tercatat di dalam database
- Buatkan fitur Tambah yang berbentuk button pada Halaman Data Register, Halaman Data Akun Google, Halaman Data Layanan, Halaman Data Portfolio, dan Halaman Data Blog. Pastikan pada Halaman Data Register, Halaman Data Akun Google, Halaman Data Layanan, Halaman Data Portfolio, dan Halaman Data Blog ketika ditambah tercatat di dalam database
- Sediakan fitur yang berbentuk button ketika memilih Data Admin ia akan menampilkan database table admin dan ketika memilih Data User ia akan menampilkan database table user pada opsi Halaman Dashboard Admin bernama Data Register 
- Sediakan fitur yang berbentuk button ketika memilih Data Admin ia akan menampilkan database table admin_google dan ketika memilih Data User ia akan menampilkan database table user_google pada opsi Halaman Dashboard Admin bernama Data Akun Google 
- Data Admin di bagian opsi Dashboard Admin bernama Data Register berisi ID (id), Nama (name), Username (username), Email (email), Kata Sandi (password), Waktu Pembuatan (created_at), dan Waktu Perubahan (uptaded_at) yang terkoneksi oleh database dengan table admin, jadi untuk isinya otomatis akan diisikan sesuai database table admin
- Data User di bagian opsi Dashboard Admin bernama Data Register berisi ID (id), Nama (name), Username (username), Email (email), Kata Sandi (password), Waktu Pembuatan (created_at), dan Waktu Perubahan (uptaded_at) yang terkoneksi oleh database dengan table user, jadi untuk isinya otomatis akan diisikan sesuai database table user
- Data Admin di bagian opsi Dashboard Admin bernama Data Akun Google berisi  ID (id), Nama (name), Email (email), Admin ID (admin_id), Gambar (photo_url), Waktu Pembuatan (created_at) Terakhir Login (last_login), dan Waktu Perubahan (uptaded_at) yang terkoneksi oleh database dengan table admin_google, jadi untuk isinya otomatis akan diisikan sesuai database table admin_google
- Data User di bagian opsi Dashboard Admin bernama Data Akun Google berisi ID (id), Nama (name), Email (email), User ID (user_id), Gambar (photo_url), Waktu Pembuatan (created_at) Terakhir Login (last_login), dan Waktu Perubahan (uptaded_at) yang terkoneksi oleh database dengan table user_google, jadi untuk isinya otomatis akan diisikan sesuai database table user_google
- Halaman Data Layanan di bagian opsi Dashboard berisi ID (id), Nama (name), Gambar (images), Jumlah (quantity) , Harga (price), Waktu Pembuatan (created_at), dan Waktu Perubahan (uptaded_at) yang terkoneksi oleh database dengan table services, jadi untuk isinya otomatis akan diisikan sesuai database table services
- Halaman Data Portfolio di bagian opsi Dashboard berisi ID (id), Nama (name), Gambar (image), Deskripsi (description), Waktu Pembuatan (created_at), dan Waktu Perubahan (uptaded_at) yang terkoneksi oleh database dengan table portfolio, jadi untuk isinya otomatis akan diisikan sesuai database table portfolio
- Halaman Data Blog di bagian opsi Dashboard berisi ID (id), Judul (title), Gambar (image), Konten (content), Waktu Pembuatan (created_at), dan Waktu Perubahan (uptaded_at) yang terkoneksi oleh database dengan table blog, jadi untuk isinya otomatis akan diisikan sesuai database table blog
- Halaman Data Kontak di bagian opsi Dashboard berisi ID (id), Nama (name), Email (email), Subjek (subject), Pesan (message), Status (status), Waktu Pembuatan (created_at), dan Waktu Perubahan (uptaded_at) yang terkoneksi oleh database dengan table contact, jadi untuk isinya otomatis akan diisikan sesuai database table contact
- Halaman Riwayat OTP di bagian opsi Dashboard berisi ID (id), Email (email), OTP (otp), Waktu Pembuatan (created_at), Terpakai (used) yang terkoneksi oleh database dengan table otp_log, jadi untuk isinya otomatis akan diisikan sesuai database table otp_log
- Pastikan ketika opsi Beranda, Data Register, Data Layanan, Data Akun Google, Data Transaksi, Data Portfolio, Data Blog, Data Kontak, dan Data Riwayat OTP ketika di klik pada Dashboard Admin tetap berada di halaman yang sama dengan Dashboard Admin akan tetapi ketika di klik pastikan memunculkan Halaman Beranda, Halaman Data Register, Halaman Data Akun Google, Halaman Data Layanan, Halaman Data Riwayat Transaksi, Halaman Data Portfolio, Halaman Data Blog, Halaman Data Kontak, Halaman Data Riwayat OTP
- Buatkan fitur upload gambar pada opsi Dashboard ”Data Layanan”, ”Data Portfolio”, dan ”Data Blog” dan bisa menampilkan gambar pada outputnya (Membutuhkan konfigurasi pada web.xml pada directory WEB-INF)
- Ketika sedang tambah pada Halaman Data Register, Halaman Akun Google, Halaman Data Layanan, Halaman Data Portfolio, dan Halaman Data Blog pada Dashboard Admin berbentuk pop up
- Pastikan ketika menginput kata sandi di Halaman Data Register dan Halaman Data Akun Google pada Dashboard Admin dan mengklik button tambah kata sandi sudah Hashing & Encryption
- Ketika login with Google di klik pada Halaman Login dan berhasil ia akan mengarahkan kepada halaman utama, akan tetapi login with google ini bisa untuk admin hanya saja harus input manual lewat Dashboard Admin
- Fitur register hanya tersedia pada user saja
- Fitur lupa password tersedia untuk admin dan user
- Buatkan halaman error ketika situs web sedang error.
- Sediakan fitur icon mata pada kolom password dan forgot password. Pastikan ketika mata terbuka password bisa dilihat akan tetapi ketika mata tertutup password tidak bisa dilihat. Default nya adalah icon mata tertutup
- Ketika mengisi kolom form apapun, ketika di refresh ia akan menghilang dan harus mengisi kolom dari ulang
- Layanan berbentuk card yang isinya ada penjelasan layanan, gambar, harga. Untuk penjelasan layanan, gambar, dan harga nanti otomatis diisikan sesuai dengan database table services
- Portfolio berbentuk card dan buatkan fitur preview. Untuk portfolionya nanti otomatis diisikan sesuai dengan database table portfolio
- Blog berbentuk card. Untuk blognya nanti otomatis diisikan sesuai dengan database table blog
- Sediakan button dengan kalimat ”Beli” dan icon keranjang pada Layanan
- Sediakan kalimat ” Baca Selengkapnya” pada Blog
- Sediakan fitur ”Lihat Lebih Banyak” pada Layanan, Portfolio, dan Blog jika card terlalu banyak. Pastikan tetap berada di Halaman yang sama
- Pastikan ketika kalimat sudah mencapai batas yang sudah ditentukan di bagian card dan isi konten lainnya ia akan membuat paragraf ke bawah, jadi kalimat tidak akan melewati ukuran card dan isi konten lainnya yang sudah ditentukan
- Pastikan ketika sedang mengisikan Halaman Login, Halaman Register, Halaman Lupa Kata Sandi, dan Halaman Kontak isi halaman tersebut di dalam kolom tidak bertabrakan oleh ukuran kolom yang sudah ditentukan
-  Partnership hanya berbentuk icon dari brand atau merk saja. Icon tersebut berbentuk format SVG
- Ketika nama ”arqeta” di klik ia akan kembali ke halaman awal dengan cara scroll keatas bukan menjadi refresh atau membuka ulang Halaman ”home.jsp”. Terapkan di semua ukuran media query.
- Buatkan fitur Login with Google pada Form Login saja. Pastikan fitur Login with Google memakai Firebase
- Fitur Login with Google tersedia untuk user dan admin
- Untuk opsi Data Akun Google pada Dashboard Admin, untuk button tambahnya ketika di klik ia akan muncul pop up Login with Google untuk menambah akun dan terdapat opsi untuk hapus
- Pastikan semua textarea hanya bisa vertikal. Terapkan di semua halaman jika terdapat fitur textarea
- Ketika user berhasil login ke Halaman Utama sediakan icon profile yang ketika di klik akan memunculkan dropdown (include anak panah untuk memunculkannya) yang isinya adalah Dashboard (dashboarduser.jsp) dan Logout
- Pastikan ketika opsi Beranda, Akun, Keranjang, dan Riwayat Transaksi, ketika di klik pada Dashboard User tetap berada di halaman yang sama dengan Dashboard User akan tetapi ketika di klik pastikan memunculkan Halaman Beranda, Halaman Akun, Halaman Keranjang dan Halaman Riwayat Transaksi
- Buttton ”Beli” dan icon keranjang pada card Layanan pada Halaman Utama ketika di klik dan user belum login ia akan diarahkan ke Halaman Login akan tetapi ketika user sudah login ketika di klik button ”Beli” akan muncul pop up (transaction.jsp) untuk menginput layanan yang ingin dibeli. Akan tetapi ketika user mengklik icon keranjang akan memunculkan notifikasi ”Layanan sudah di tambahkan pada keranjang Dashboard)
- Sediakan fitur unread dan read pada Halaman Data Kontak di bagian Dashboard Admin 

Warna situs web:
- Hitam --> #181818
- Putih --> #FFFFFF

Icon situs web:
- Media sosial  web/dist/svg
- Partnership  web/dist/svg
- Icon Login with Google  web/dist/svg
- Icon lainnya  https://feathericons.com

Hover situs web:
- Terapkan ketika menghover card ia menjadi timbul
- Ketika menghover semua button pada situs web, akan tetapi tetap memakai warna yang sudah ditentukan tetapi dibuat bayangan
- Pastikan ketika di hover di bagian opsi yang ada di Navigation Bar, Slider Humburger Menu pada Halaman Utama, Slider Humburger Menu pada Dashboard Admin, dan Dashboard User ia membuat garis dibawah kalimatnya, akan tetapi garis tersebut dibuat setengah saja. Pastikan juga Ketika di klik ia mempertahakan garis dibawahnya dan menjadi bold
- Terapkan ketika menghover icon sosial media. Ketika di hover ia timbul
- Ketika menghover icon pada Partnership ia akan berwarna. Akan tetapi default icon tersebut tidak berwarna

Animasi situs web:
- Pastikan animasi tidak membuat situs web menjadi berat agar tidak lag
- Animasikan ketika mengklik icon matahari dan bulan pada fitur Light Mode - Dark Mode di bagian Navigation Bar dan Dashboard. Ketika Light Mode icon menjadi bulan sabit dan ketika Dark Mode icon menjadi matahari. Default situs web adalah Light Mode
- Animasikan toggle switch Light Mode - Dark Mode pada slider Humburger Menu pada Halaman Utama, Slider Humburger Menu pada Halaman Dashboard Admin dan Slider Humburger Menu pada Halaman Dashboard User. Akan tetapi toggle switch untuk tuasnya berbentuk bulat hitam saja tidak perlu memakai icon matahari dan bulan sabit
- Animasikan icon Humburger Menu ketika di klik

Parallax situs web:
- Terapkan di semua halaman situs web

Font situs web:
- Quicksand (SemiBold)
- Quicksand (Medium)
- Quicksand (Regular)

Notifikasi situs web:
- Pastikan semua notifikasi berjalan dengan semestinya dan tidak membuat spam agar menghindari lag pada situs web. Pastikan juga semua notifikasi tidak menutupi isi konten
- Pastikan semua notifikasi berjalan sebagaimana fungsinya
- Tidak perlu diberi notifikasi ketika ingin login sebagai user dan admin
- Ketika login sesuai dan tidak sesuai dengan database
- Ketika edit dan hapus pada Halaman Data Register, Halaman Data Layanan, Halaman Data Akun Google Admin, Halaman Data Akun Google User, Halaman Data Portfolio, Halaman Data Blog, dan Halaman Data Kontak di bagian Dashboard
- Ketika menginput pada Halaman Data Register, Halaman Data Layanan Halaman Data Akun Google Admin, Halaman Data Akun Google User, Halaman Data Portfolio, dan Halaman Data Blog di bagian Dashboard,
- Ketika Logout pada Halaman Dashboard Admin dan User
- Ketika seseorang mencoba klik kanan dan f12 pada situs web
- Ketika seseorang mencoba copypaste pada situs web
- Ketika mengirim pesan pada isi konten Kontak
- Ketika memasuki halaman OTP ia muncul notifikasi yang isinya kode OTP
- Ketika nama, username, kata sandi di Halaman Login tidak sesuai pada database notifikasinya hanya memberikan penjelasan bahwa akun tidak ditemukan untuk menghindari SQL Injection

Security situs web:
- Terapkan konsep Human organization dan regulatory
- Terapkan konsep Attack dan Defense
- Terapkan konsep Security System
- Terapkan konsep Software dan Platform Security
- Terapkan konsep Security Infrastructure
- Pastikan tidak bisa klik kanan
- Pastikan situs web tidak bisa dilacak oleh fitur "Inspect"
- Pastikan kode program tidak bisa dideteksi dan tidak bisa akses oleh orang lain
- Pastikan isi konten tidak bisa copypaste

Config Firebase untuk fitur Login with Google situs web:
- apiKey: "AIzaSyDF1xa0UEu0wTCYzDPdC0AFJKY5Y5PjBhc",
- authDomain: "thearqeta.firebaseapp.com",
- projectId: "thearqeta",
- storageBucket: "thearqeta.firebasestorage.app",
- messagingSenderId: "560559961893",
- appId: "1:560559961893:web:6d51f4b364c763bfcc4ccc",
- measurementId: "G-BPLQBHFFX1"

Alur Kerja situs web:
- Pada Halaman Dashboard Admin terdapat Halaman Beranda yang isinya adalah jumlah dari konten Halaman Data Register, Halaman Data Akun Google, Halaman Data Layanan, Halaman Transaksi, Halaman Data Portfolio, Halaman Data Blog, Halaman Data Kontak, dan Halaman Data Riwayat OTP
- Pada Halaman Dashboard Admin terdapat Halaman Data Register yang isinya adalah semua database table user dan database table admin. Jadi di dalam Halaman Data Register terdapat fitur untuk memilih ingin menampilkan database table admin dan database table user yang nantinya fitur tersebut berbentuk button yang jika di klik akan memunculkan data tersebut. Pastikan Halaman Data Register terdapat fitur ”Tambah Akun” yang berbentuk button dan sediakan fitur Edit dan Hapus
- Pada Halaman Dashboard Admin terdapat Halaman Data Akun Google yang isinya adalah semua database table admin_google dan database table user_google. Jadi di dalam Halaman Data Akun Google terdapat fitur untuk memilih ingin menampilkan database table admin_google dan database table user_google yang nantinya fitur tersebut berbentuk button yang jika di klik akan memunculkan data tersebut. Pastikan Halaman Data Akun Google terdapat fitur ”Tambah Akun Google” yang berbentuk button dan sediakan fitur Hapus saja
- Pada Halaman Dashboard Admin terdapat Halaman Data Layanan yang isinya adalah semua database table services. Pastikan Halaman Data Akun Google terdapat fitur ”Tambah Layanan” yang berbentuk button, upload gambar dan sediakan fitur Edit dan Hapus
- Pada Halaman Dashboard Admin terdapat Halaman Data Transaksi yang isinya adalah semua database table transaction. Jadi di dalam Halaman Data Transaksi ketika user membeli barang ia akan berstatus ”menunggu” dan tugas admin adalah untuk menerima pesanan tersebut di dalam Dashboard Admin pada Halaman Data Transaksi. Pastikan Halaman Data Transaksi terdapat fitur untuk menolak pesanan dan menghapus riwayat pesanan yang tercatat
- Pada Halaman Dashboard Admin terdapat Halaman Data Portfolio yang isinya adalah semua database table portfolio. Pastikan Halaman Data Portfolio terdapat fitur ”Tambah Portfolio” yang berbentuk button, upload gambar dan sediakan fitur Edit dan Hapus
- Pada Halaman Dashboard Admin terdapat Halaman Data Blog yang isinya adalah semua database table blog. Pastikan Halaman Data Blog terdapat fitur ”Tambah Blog” yang berbentuk button, upload gambar dan sediakan fitur Edit dan Hapus
- Pada Halaman Dashboard Admin terdapat Halaman Data Kontak yang isinya adalah semua database table contact. Pastikan Halaman Data Kontak terdapat fitur untuk membaca pesan dan menghapus pesan
- Pada Halaman Dashboard Admin terdapat Halaman Data Riwayat OTP yang isinya adalah semua database table otp_log. Pastikan Halaman Data Riwayat OTP terdapat fitur untuk hapus saja
- Pastikan semua file SVG, CSS, dan Javascript di pisah yang nantinya file tersebut berada pada directory web/dist 
- Halaman Akun pada Dashboard User adalah akun yang dipakai user
- Halaman Keranjang pada Dashboard adalah halaman untuk jadi atau tidaknya membeli layanan
- Halaman Riwayat Trasanksi pada Dashboard User adalah riwayat trasanksi ketika selesai membeli layanan
- Ketika menginput pada halaman transaksi (transaction.jsp) ia akan tersimpan pada database table transaction dan tersimpan juga di Halaman Data Transaksi pada opsi Dashboard Admin
- Pastikan icon Partnership dan icon media sosial jangan diubah untuk iconnya
- Pastikan background pada Halaman Utama berwarna putih kecuali pada Hero Section. Akan tetapi tiap Halaman pada Halaman Utama dikasih layer buat menandakan Halaman tersebut adalah Halaman yang berbeda. Default dari Halaman Utama adalah berwarna putih
- Pastikan semua table database yang di input dan di output tercatat pada database
- Pastikan pada Halaman Login, Regsiter, dan Lupa Kata Sandi tidak ada menampilkan kalimat ”Arqeta” ataupun logo ”Arqeta”
- Pastikan background Halaman Login, Halaman Register, Halaman Lupa Kata Sandi berwarna putih saja
- Untuk menangani HTTPS dan SLL menggunakan Cloudflare
- Untuk menangani Login with Google menggunakan Firebase
- Semua icon pada situs web tersimpan pada directory web/dist/svg. Jadi tinggal dipanggil saja untuk iconnya di setiap Halaman situs web
- Icon partnership terdiri dari Anthropic, Figma, Google
- Icon media sosial terdiri dari Instagram, LinkedIn, Medium, dan Twitter
- Siapa saya? pada Tentang Kami adalah deskripsi dari situs web
- Ingin Mengenal Kami Lebih Jauh? adalah deskripsi ”Kunjungi sosial media kami” dan dibawahnya terdapat icon media sosial
- Pastikan ”Siapa Saya?” dan ”Ingin Mengenal Kami Lebih Jauh?” menjadi 2 kolom
- Cari apa? pada Footer adalah semua isi konten dari Navigation Bar
- Layanan pada Footer adalah ”Layanan Pembuatan UI Web” dan ”Layanan Pembuatan UI Mobile”
- Desain Halaman Dashboard Admin dan User semua opsi berada di sebelah kiri sedangkan untuk sebalah kanan adalah isi konten dari opsi tersebut
- Ketika button Login di klik ia akan memasuki halaman ”signin.jsp” yang di dalamnya ada Halaman Login
- Pastikan semua style (file css) pada Halaman situs web tersimpan pada ”web/dist/css”. Jadi nantinya tinggal dipanggil saja ke dalam Halaman situs web yang ingin membutuhkan style (file css)
- Pastikan semua script (file js) pada Halaman situs web tersimpan pada ”web/dist/js”. Jadi nantinya tinggal dipanggil saja ke dalam Halaman situs web yang ingin membutuhkan script (file js)
- Pastikan semua SVG pada Halaman situs web tersimpan pada ”web/dist/svg”. Jadi nantinya tinggal dipanggil saja ke dalam Halaman situs web yang ingin membutuhkan SVG
- Pastikan semua IMG pada Halaman situs web tersimpan pada ”web/dist/img”. Jadi nantinya tinggal dipanggil saja ke dalam Halaman situs web yang ingin membutuhkan IMG
- Pada Halaman Lupa kata sandi ketika memasukkan email ia akan diarahkan ke Halaman OTP dan memunculkan notifikasi kode OTP yang nantinya kode OTP tersebut untuk mengganti kata sandi. Halaman Lupa kata sandi dan Halaman memasukkan kode OTP berbeda akan tetapi Halaman kode OTP tetap berada pada Halaman Lupa kata sandi. Ketika sudah memasukkan kode OTP dan sudah benar sesuai pada notifikasi ia akan memasuki Halaman untuk mengganti kata sandi setelah itu baru ia kembali ke Halaman Login. Pastikan juga notifikasi OTP jangan terlalu cepat hilangnya dan notifikasi tersebut di letakkan di kanan 


Pastikan kode program sesuai aturan dalam penulisan sintaksis agar mudah untuk dimodifikasi dan dipelihara. Pastikan juga semua Spesifikasi, Database, File JSP, Directory, Halaman Login, Halaman Register, Halaman Lupa Kata Sandi, Halaman Utama. Navigation Bar, Dashboard Admin, Dashboard User, Slider Humburger Menu (Halaman Utama, Halaman Dashboard Admin, dan Halaman Dashboard User), Fitur, Warna, Icon, Hover, Animasi, Parallax, Font, Notifikasi, Security, Config Firebase untuk fitur Login with Google, dan Alur Kerja sesuai seperti apa yang sudah diminta oleh developer kemudian sekali lagi untuk isi konten biarkan developer yang mengisikannya tugas anda hanya menyediakan saja, akan tetapi jika developer mengizinkan anda diperintahnya dan boleh untuk mengisikannya anda boleh mengisikan isi konten tersebut. Sertakan juga Tag Comments dengan menggunakan bahasa Indonesia yang mudah dipahami oleh pembaca agar tidak terjadi kesalahpahaman dalam menjelaskan. Perhatikan dan Implemtasikan secara lebih dan lebih komprehensif sesuai seperti apa yang sudah diminta developer kepada anda
