/* 
 * Authentication Stylesheet for Arqeta Website
 * Created on : June 1, 2025
 * Author     : Arqeta
 * Description: Styles for authentication pages (login, register, forgot password)
 */

/* ===== Reset & Base Styles ===== */
:root {
  --color-black: #181818;
  --color-white: #ffffff;
  --color-gray-100: #f8f8f8;
  --color-gray-200: #e0e0e0;
  --color-gray-300: #c0c0c0;
  --color-gray-400: #a0a0a0;
  --color-gray-500: #808080;
  --color-gray-600: #717171;
  --color-gray-700: #515151;
  --color-gray-800: #313131;
  --color-gray-900: #212121;
  --color-success: #4caf50;
  --color-warning: #ff9800;
  --color-error: #ff5252;
  --color-info: #2196f3;

  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.1), 0 6px 6px rgba(0, 0, 0, 0.1);

  --transition-fast: all 0.2s ease;
  --transition-normal: all 0.3s ease;

  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;

  /* Color variables */
  --primary-color: #181818;
  --secondary-color: #ffffff;
  --background-color: #ffffff;
  --text-color: #181818;
  --card-bg: #f8f8f8;
  --shadow-color: rgba(0, 0, 0, 0.1);

  /* Transition variables */
  --transition-speed: 0.3s;

  /* Font variables */
  --font-family: "Quicksand", sans-serif;

  /* Light mode default colors */
  --primary-color: #181818;
  --secondary-color: #333333;
  --text-color: #181818;
  --bg-color: #ffffff;
  --card-bg: #ffffff;
  --input-bg: #f9f9f9;
  --input-text: #181818;
  --input-border: #eeeeee;
  --input-focus-border: #181818;
  --btn-bg: #181818;
  --btn-text: #ffffff;
  --btn-google-bg: #ffffff;
  --btn-google-text: #181818;
  --btn-google-border: #eeeeee;
  --link-color: #181818;
  --link-hover: #000000;
  --divider-color: #eeeeee;
  --shadow-color: rgba(0, 0, 0, 0.05);
  --notification-info-bg: #e5f5ff;
  --notification-info-color: #0077cc;
  --notification-success-bg: #e5fff2;
  --notification-success-color: #00aa55;
  --notification-warning-bg: #fffbe5;
  --notification-warning-color: #cc8800;
  --notification-error-bg: #ffebee;
  --notification-error-color: #cc0033;

  /* Animation durations */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;

  --primary-color: #181818;
  --background-color: #ffffff;
  --text-color: #181818;
  --input-bg-color: #f5f5f5;
  --border-color: #e5e5e5;
  --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --border-radius: 8px;
}

/* Dark mode colors */
[data-theme="dark"] {
  --primary-color: #ffffff;
  --secondary-color: #cccccc;
  --text-color: #f8f8f8;
  --bg-color: #121212;
  --card-bg: #1e1e1e;
  --input-bg: #2a2a2a;
  --input-text: #f8f8f8;
  --input-border: #444444;
  --input-focus-border: #ffffff;
  --btn-bg: #ffffff;
  --btn-text: #121212;
  --btn-google-bg: #333333;
  --btn-google-text: #ffffff;
  --btn-google-border: #444444;
  --link-color: #a0c9ff;
  --link-hover: #ffffff;
  --divider-color: #444444;
  --shadow-color: rgba(0, 0, 0, 0.2);
  --notification-info-bg: #001f33;
  --notification-info-color: #66b3ff;
  --notification-success-bg: #003319;
  --notification-success-color: #66ffaa;
  --notification-warning-bg: #332600;
  --notification-warning-color: #ffcc66;
  --notification-error-bg: #330011;
  --notification-error-color: #ff6680;

  --input-bg-color: #2d2d2d;
  --border-color: #3d3d3d;
  --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  transition: background-color var(--transition-normal) ease-in-out,
    color var(--transition-normal) ease-in-out;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-weight: 400;
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--bg-color);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color var(--transition-speed),
    color var(--transition-speed);
  padding: 20px;
}

h1,
h2,
h3 {
  font-family: "Quicksand", sans-serif;
  font-weight: 600;
  line-height: 1.2;
}

a {
  color: var(--link-color);
  text-decoration: none;
  transition: all var(--transition-fast) ease;
}

a:hover {
  color: var(--link-hover);
}

button,
input,
textarea {
  font-family: var(--font-family);
}

/* ===== Authentication Container ===== */
.auth-container {
  width: 100%;
  max-width: 450px;
}

.auth-card {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  padding: 40px;
  width: 100%;
}

.auth-header {
  text-align: center;
  margin-bottom: 30px;
}

.auth-header h1 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.auth-header p {
  color: var(--text-color);
  opacity: 0.8;
}

/* ===== Form Elements ===== */
.auth-form {
  width: 100%;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background-color: var(--input-bg-color);
  color: var(--text-color);
  font-family: "Quicksand", sans-serif;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.password-input {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: var(--text-color);
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.password-toggle:hover {
  opacity: 1;
}

.form-action {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.forgot-link {
  color: var(--primary-color);
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.forgot-link:hover {
  opacity: 0.8;
  text-decoration: underline;
}

/* ===== Buttons ===== */
.btn-auth {
  width: 100%;
  padding: 12px;
  background-color: var(--primary-color);
  color: var(--background-color);
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: "Quicksand", sans-serif;
  font-size: 1rem;
}

.btn-auth:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

.btn-auth:active {
  transform: translateY(0);
}

.btn-google {
  width: 100%;
  padding: 12px;
  background-color: #fff;
  color: #4285f4;
  border: 1px solid #4285f4;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  margin-bottom: 20px;
}

.btn-google img {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.btn-google:hover {
  background-color: #f5f5f5;
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

/* ===== Divider ===== */
.auth-divider {
  display: flex;
  align-items: center;
  text-align: center;
  margin: 20px 0;
}

.auth-divider:before,
.auth-divider:after {
  content: "";
  flex: 1;
  border-bottom: 1px solid var(--border-color);
}

.auth-divider span {
  padding: 0 10px;
  color: var(--text-color);
  opacity: 0.7;
  font-size: 0.9rem;
}

/* ===== Footer ===== */
.auth-footer {
  text-align: center;
  margin-top: 20px;
  font-size: 0.9rem;
}

.auth-footer a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.auth-footer a:hover {
  text-decoration: underline;
}

/* ===== Notifications ===== */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  max-width: 400px;
  background-color: var(--background-color);
  color: var(--text-color);
  border-radius: var(--border-radius);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  padding: 0;
  z-index: 1000;
  transform: translateX(110%);
  transition: transform 0.3s ease;
}

.notification.show {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  align-items: center;
  padding: 15px 20px;
}

.notification-message {
  flex: 1;
}

.close-notification {
  cursor: pointer;
  font-size: 20px;
  margin-left: 15px;
}

.notification-info {
  border-left: 4px solid var(--info-color);
}

.notification-success {
  border-left: 4px solid var(--success-color);
}

.notification-error {
  border-left: 4px solid var(--danger-color);
}

.notification-warning {
  border-left: 4px solid var(--warning-color);
}

/* ===== Forgot Password Steps ===== */
.auth-step {
  display: none;
}

.auth-step.active {
  display: block;
}

/* ===== Input Group ===== */
.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-group i {
  position: absolute;
  left: 15px;
  color: var(--text-color);
  opacity: 0.6;
  z-index: 1;
}

.input-group input {
  padding-left: 45px;
  padding-right: 45px;
}

.toggle-password {
  position: absolute;
  right: 15px;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-color);
  opacity: 0.6;
  transition: opacity var(--transition-fast);
  z-index: 1;
}

.toggle-password:hover {
  opacity: 1;
}

.toggle-password i {
  position: static;
}

/* ===== Form Help Text ===== */
.form-help {
  display: block;
  margin-top: 5px;
  font-size: 0.85rem;
  color: var(--text-color);
  opacity: 0.7;
}

/* ===== Password Strength Indicator ===== */
.password-strength {
  margin-top: 8px;
}

.strength-bar {
  width: 100%;
  height: 4px;
  background-color: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 5px;
}

.strength-fill {
  height: 100%;
  width: 0%;
  background-color: #ff4757;
  transition: width var(--transition-normal),
    background-color var(--transition-normal);
}

.strength-text {
  font-size: 0.8rem;
  font-weight: 500;
  color: #ff4757;
  transition: color var(--transition-normal);
}

/* ===== Utilities ===== */
.hidden {
  display: none !important;
}

/* ===== Media Queries ===== */
@media screen and (max-width: 576px) {
  .auth-card {
    padding: 30px 20px;
  }

  .auth-header h1 {
    font-size: 1.8rem;
  }

  .btn-auth,
  .btn-google {
    padding: 10px;
  }
}
