<%--
    Document   : error
    Created on : Jun 1, 2025, 10:45:35 PM
    Author     : Arqeta
    Description: Generic error page
--%>

<%@page contentType="text/html" pageEncoding="UTF-8" isErrorPage="true"%>
<%@page import="java.io.*" %>

<!DOCTYPE html>
<html lang="id">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title><PERSON><PERSON><PERSON><PERSON> | Arqeta</title>
        <style>
            /* Error Page Styles */
            :root {
                --color-black: #181818;
                --color-white: #ffffff;
            }

            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: "Quicksand", sans-serif;
                background-color: var(--color-white);
                color: var(--color-black);
                height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                text-align: center;
            }

            .error-container {
                max-width: 500px;
            }

            h1 {
                font-size: 80px;
                margin-bottom: 10px;
                line-height: 1;
                font-weight: 700;
            }

            h2 {
                font-size: 24px;
                margin-bottom: 20px;
            }

            p {
                margin-bottom: 30px;
                color: #808080;
            }

            .btn-home {
                display: inline-block;
                padding: 12px 30px;
                background-color: var(--color-black);
                color: var(--color-white);
                text-decoration: none;
                border-radius: 6px;
                font-weight: 600;
                transition: all 0.3s ease;
            }

            .btn-home:hover {
                transform: translateY(-3px);
                box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
            }

            .error-details {
                margin-top: 30px;
                padding: 15px;
                background-color: #f8f8f8;
                border-radius: 6px;
                text-align: left;
                font-size: 14px;
                display: none;
            }

            .show-details {
                background: none;
                border: none;
                color: #808080;
                text-decoration: underline;
                cursor: pointer;
                margin-top: 20px;
                font-family: inherit;
            }

            /* Dark Mode Support */
            @media (prefers-color-scheme: dark) {
                :root {
                    --color-black: #ffffff;
                    --color-white: #181818;
                }

                p {
                    color: #a0a0a0;
                }

                .error-details {
                    background-color: #333333;
                    color: #e0e0e0;
                }
            }
        </style>
        <link
            href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap"
            rel="stylesheet"
        />
    </head>
    <body>
        <div class="error-container">
            <h1>Oops!</h1>
            <h2>Terjadi Kesalahan</h2>
            <p>
                Maaf, terjadi kesalahan saat memproses permintaan Anda. Tim kami telah
                diberi tahu dan sedang menangani masalah ini.
            </p>
            <a href="${pageContext.request.contextPath}/home.jsp" class="btn-home"
                >Kembali ke Beranda</a
            >

            <% if (exception != null) { %>
            <button class="show-details" onclick="toggleErrorDetails()">
                Lihat detail kesalahan
            </button>
            <div id="errorDetails" class="error-details">
                <strong>Error Type:</strong> <%= exception.getClass().getName() %><br />
                <strong>Message:</strong> <%= exception.getMessage() %><br />
                <strong>Stack Trace:</strong><br />
                <pre>
                    <% StringWriter sw = new StringWriter();
                        PrintWriter pw = new PrintWriter(sw);
                        exception.printStackTrace(pw);
                        out.println(sw.toString().substring(0, Math.min(sw.toString().length(), 1000))); %>
                </pre>
                <% if (sw.toString().length() > 1000) { %>
                <em>... (truncated)</em>
                <% } %>
            </div>
            <% } %>
        </div>

        <script>
            function toggleErrorDetails() {
                const errorDetails = document.getElementById("errorDetails");
                if (errorDetails.style.display === "block") {
                    errorDetails.style.display = "none";
                } else {
                    errorDetails.style.display = "block";
                }
            }
        </script>
    </body>
</html>
