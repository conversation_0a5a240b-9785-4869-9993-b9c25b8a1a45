# Fitur Ekspor Data Layanan

## Deskripsi
Fitur ini memungkinkan admin untuk mengekspor data layanan dalam format Word (DOC) dan JSON dari halaman Dashboard Admin.

## Lokasi Fitur
- **Halaman**: Dashboard Admin > Data Layanan
- **Posisi**: Button ekspor berada di sebelah kiri button "Tambah Layanan"

## Fitur yang Ditambahkan

### 1. Button Ekspor
- **Word Button**: Mengekspor data dalam format DOC yang dapat dibuka di Microsoft Word
- **JSON Button**: Mengekspor data dalam format JSON untuk integrasi API atau analisis data

### 2. File yang Dimodifikasi/Ditambahkan

#### File yang Dimodifikasi:
1. `web/form/admin/servicesdata.jsp`
   - Menambahkan button ekspor Word dan JSON
   - Menambahkan fungsi JavaScript untuk ekspor

2. `web/dist/css/dashboard.css`
   - Menambahkan styling untuk button Word (biru) dan JSON (ungu)
   - Menggunakan responsive design yang sudah ada

3. `web/WEB-INF/web.xml`
   - Menambahkan konfigurasi servlet untuk export services processor

#### File yang Ditambahkan:
1. `web/process/admin/exportServices.jsp`
   - Processor untuk menghandle ekspor data layanan
   - Mendukung format Word (DOC) dan JSON

### 3. Data yang Diekspor
Data layanan yang diekspor meliputi:
- ID Layanan
- Nama Layanan
- Nama File Gambar
- Jumlah/Stok
- Harga (dalam format Rupiah)
- Waktu Pembuatan
- Waktu Perubahan Terakhir

### 4. Format Ekspor

#### Word (DOC):
- File dengan ekstensi `.doc`
- Dapat dibuka di Microsoft Word, LibreOffice Writer, dll.
- Format tabel yang rapi dengan styling profesional
- Header dan footer dengan informasi ekspor
- Nama file: `data_layanan_YYYYMMDD_HHMMSS.doc`

#### JSON:
- File dengan ekstensi `.json`
- Format standar untuk integrasi API
- Struktur data yang terorganisir dengan metadata
- Informasi ekspor dan total records
- Nama file: `data_layanan_YYYYMMDD_HHMMSS.json`

### 5. Struktur JSON
```json
{
  "export_info": {
    "title": "Data Layanan - Arqeta",
    "exported_at": "2025-07-30 14:30:00",
    "format": "JSON",
    "total_records": 10
  },
  "services": [
    {
      "id": 1,
      "name": "Nama Layanan",
      "images": "gambar.jpg",
      "quantity": 100,
      "price": 50000.0,
      "price_formatted": "Rp 50,000",
      "created_at": "2025-01-01 10:00:00",
      "updated_at": "2025-01-15 15:30:00"
    }
  ]
}
```

### 6. Keamanan
- Hanya admin yang login yang dapat mengakses fitur ekspor
- Validasi session admin sebelum proses ekspor
- Error handling untuk database connection
- Escape karakter untuk mencegah injection

### 7. Styling Button
- **Word Button**: Warna biru (#2b579a) dengan icon file-text
- **JSON Button**: Warna ungu (#6f42c1) dengan icon code
- Hover effects dengan animasi smooth
- Responsive design untuk semua ukuran layar

## Cara Penggunaan
1. Login sebagai admin
2. Masuk ke halaman "Data Layanan"
3. Klik button "Word" untuk ekspor ke format DOC
4. Klik button "JSON" untuk ekspor ke format JSON
5. File akan otomatis didownload

## Dependensi
- MySQL JDBC Driver (sudah ada)
- JSP/Servlet support (sudah ada)
- Tidak memerlukan library tambahan

## Catatan Penting
- Fitur ini mengekspor SEMUA data layanan yang ada di database
- File Word menggunakan format HTML yang kompatibel dengan Microsoft Word
- File JSON menggunakan encoding UTF-8 untuk mendukung karakter Indonesia
- Error handling sudah diimplementasi untuk menangani masalah database
- Data gambar hanya mengekspor nama file, bukan file gambar itu sendiri

## Troubleshooting
Jika terjadi masalah:
1. Pastikan admin sudah login
2. Periksa koneksi database
3. Pastikan folder temp dapat diakses untuk file download
4. Periksa log server untuk error detail
5. Pastikan browser mendukung download file

## Integrasi dengan Sistem Lain
File JSON yang dihasilkan dapat digunakan untuk:
- Import ke sistem lain
- Analisis data dengan tools seperti Excel, Python, R
- Backup data dalam format yang mudah dibaca
- Integrasi dengan API eksternal
