<%--
    Document   : loginwithgoogle
    Created on : Jun 1, 2025, 4:30:15 PM
    Author     : Arqeta
    Description: Login with Google page for Arqeta website
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../config/connection.jsp" %>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Login dengan Google ke Arqeta" />
    <title>Login dengan Google | Arqeta</title>

    <link rel="stylesheet" href="../dist/css/auth.css" />
    <link
        href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600&display=swap"
        rel="stylesheet"
    />
    <script src="https://unpkg.com/feather-icons"></script>

    <script src="https://www.gstatic.com/firebasejs/9.19.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.19.1/firebase-auth-compat.js"></script>

    <script>
        document.addEventListener("contextmenu", function (e) {
            e.preventDefault();
            showNotification("Klik kanan dinonaktifkan pada situs ini!", "error");
        });

        // Prevent inspect element
        document.addEventListener("keydown", function (e) {
            if (
                e.keyCode === 123 ||
                (e.ctrlKey && e.shiftKey && e.keyCode === 73)
            ) {
                e.preventDefault();
                showNotification(
                    "Inspeksi elemen dinonaktifkan pada situs ini!",
                    "error"
                );
            }
        });
    </script>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>Login dengan Google</h1>
                <p>Masuk menggunakan akun Google Anda</p>
            </div>

            <div class="auth-form">
                <div id="googleLogin" class="btn-google">
                    <img src="../dist/svg/googleicon.svg" alt="Google" />
                    <span>Login dengan Google</span>
                </div>

                <div class="auth-footer">
                    <p>Kembali ke <a href="signin.jsp">halaman login</a></p>
                </div>
            </div>
        </div>
    </div>

    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p>Menghubungkan dengan Google...</p>
    </div>

    <div class="notification" id="notification">
        <div class="notification-content">
            <span class="notification-message" id="notificationMessage"></span>
            <span class="close-notification" onclick="closeNotification()"
                >&times;</span
            >
        </div>
    </div>

    <script>
        // Initialize feather icons
        feather.replace();

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDF1xa0UEu0wTCYzDPdC0AFJKY5Y5PjBhc",
            authDomain: "thearqeta.firebaseapp.com",
            projectId: "thearqeta",
            storageBucket: "thearqeta.firebasestorage.app",
            messagingSenderId: "560559961893",
            appId: "1:560559961893:web:6d51f4b364c763bfcc4ccc",
            measurementId: "G-BPLQBHFFX1"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);

        // Google sign-in provider
        const googleProvider = new firebase.auth.GoogleAuthProvider();
        googleProvider.addScope('profile');
        googleProvider.addScope('email');

        // Get loading overlay
        const loadingOverlay = document.getElementById('loadingOverlay');

        // Google sign-in function
        document.getElementById('googleLogin').addEventListener('click', function() {
            showLoading(true);
            firebase.auth()
                .signInWithPopup(googleProvider)
                .then(function(result) {
                    // Google Sign In was successful
                    const user = result.user;

                    // Get the user information from the Google account
                    const name = user.displayName;
                    const email = user.email;
                    const photoURL = user.photoURL;
                    const uid = user.uid;

                    // Send the data to the server to handle user creation/login
                    window.location.href = '../process/googleLoginProcess.jsp' +
                        '?name=' + encodeURIComponent(name) +
                        '&email=' + encodeURIComponent(email) +
                        '&photoURL=' + encodeURIComponent(photoURL) +
                        '&uid=' + encodeURIComponent(uid);
                })
                .catch(function(error) {
                    // Handle Errors here
                    showLoading(false);
                    showNotification('Login gagal: ' + error.message, 'error');
                    console.error("Google Sign-In Error", error);
                });
        });

        // Show/hide loading overlay
        function showLoading(show) {
            if (show) {
                loadingOverlay.style.display = 'flex';
            } else {
                loadingOverlay.style.display = 'none';
            }
        }

        // Check for session message
        <% if (session.getAttribute("message") != null) { %>
            showNotification("<%= session.getAttribute("message") %>", "<%= session.getAttribute("messageType") %>");
            <%
            // Clear the message after displaying
            session.removeAttribute("message");
            session.removeAttribute("messageType");
            %>
        <% } %>

        // Notification functions
        function showNotification(message, type) {
            if (!type) type = "info";
            const notification = document.getElementById("notification");
            const notificationMessage = document.getElementById("notificationMessage");

            notificationMessage.textContent = message;
            notification.className = "notification";
            notification.classList.add("notification-" + type);
            notification.classList.add("show");

            // Auto hide after 10 seconds
            setTimeout(function() {
                closeNotification();
            }, 10000);
        }

        function closeNotification() {
            const notification = document.getElementById("notification");
            notification.classList.remove("show");
        }

        // Prevent copy paste
        document.addEventListener("copy", function (e) {
            e.preventDefault();
            showNotification("Maaf, tindakan salin telah dinonaktifkan di situs ini.", "error");
        });

        document.addEventListener("paste", function (e) {
            e.preventDefault();
            showNotification("Maaf, tindakan tempel telah dinonaktifkan di situs ini.", "error");
        });
    </script>

    <style>
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            z-index: 9999;
        }

        .loading-spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #ffffff;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        .loading-overlay p {
            color: white;
            font-family: "Quicksand", sans-serif;
            font-weight: 500;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</body>
</html>
