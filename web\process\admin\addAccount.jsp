<%-- 
    Document    : addAccount
    Created on  : Jun 14, 2025 
    Author      : Arqeta
    Description : Process untuk menambah akun admin/user dengan hashing password
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.security.MessageDigest"%>
<%@page import="java.nio.charset.StandardCharsets"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Periksa apakah user sudah login sebagai admin
    String adminId = (String) session.getAttribute("adminId");
    if (adminId == null) {
        response.sendRedirect("../../form/signin.jsp");
        return;
    }

    // Ambil parameter dari form
    String type = request.getParameter("type");
    String name = request.getParameter("name");
    String username = request.getParameter("username");
    String email = request.getParameter("email");
    String password = request.getParameter("password");

    // Validasi input: semua field harus diisi
    if (type == null || name == null || username == null || email == null || password == null || 
        type.trim().isEmpty() || name.trim().isEmpty() || username.trim().isEmpty() || 
        email.trim().isEmpty() || password.trim().isEmpty()) {
        
        session.setAttribute("message", "Semua field harus diisi!");
        session.setAttribute("messageType", "error");
        
        String redirectType = (type != null ? type : "admin");
        response.sendRedirect("../../dashboardadmin.jsp?page=register&type=" + redirectType);
        return;
    }

    // Validasi tipe akun
    if (!type.equals("admin") && !type.equals("user")) {
        session.setAttribute("message", "Tipe akun tidak valid!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=register&type=admin");
        return;
    }

    try {
        // Periksa apakah username atau email sudah ada di database
        String checkQuery = "SELECT COUNT(*) FROM " + type + " WHERE username = ? OR email = ?";
        PreparedStatement checkPs = conn.prepareStatement(checkQuery);
        checkPs.setString(1, username.trim());
        checkPs.setString(2, email.trim());
        
        ResultSet checkRs = checkPs.executeQuery();
        checkRs.next();
        
        if (checkRs.getInt(1) > 0) {
            session.setAttribute("message", "Username atau email sudah digunakan!");
            session.setAttribute("messageType", "error");
            
            checkRs.close();
            checkPs.close();
            
            response.sendRedirect("../../dashboardadmin.jsp?page=register&type=" + type);
            return;
        }
        
        checkRs.close();
        checkPs.close();

        // Hash password menggunakan algoritma SHA-256
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(password.getBytes(StandardCharsets.UTF_8));
        
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        String hashedPassword = hexString.toString();

        // Insert data baru ke dalam database
        String insertQuery = "INSERT INTO " + type + " (name, username, email, password) VALUES (?, ?, ?, ?)";
        PreparedStatement insertPs = conn.prepareStatement(insertQuery);
        insertPs.setString(1, name.trim());
        insertPs.setString(2, username.trim());
        insertPs.setString(3, email.trim());
        insertPs.setString(4, hashedPassword);
        
        int result = insertPs.executeUpdate();
        insertPs.close();
        
        if (result > 0) {
            session.setAttribute("message", "Akun " + type + " berhasil ditambahkan!");
            session.setAttribute("messageType", "success");
        } else {
            session.setAttribute("message", "Gagal menambahkan akun!");
            session.setAttribute("messageType", "error");
        }
        
    } catch (Exception e) {
        session.setAttribute("message", "Terjadi kesalahan pada server: " + e.getMessage());
        session.setAttribute("messageType", "error");
        // Untuk debugging, bisa ditambahkan:
        // e.printStackTrace(); 
    }

    // Redirect kembali ke halaman register dengan tipe yang sesuai
    response.sendRedirect("../../dashboardadmin.jsp?page=register&type=" + type);
%>
