<%-- 
    Document    : editAccount
    Created on  : Jun 14, 2025 
    Author      : Arqeta
    Description : Process untuk mengedit akun admin/user dengan validasi
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.security.MessageDigest"%>
<%@page import="java.nio.charset.StandardCharsets"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Periksa apakah user sudah login sebagai admin
    String adminId = (String) session.getAttribute("adminId");
    if (adminId == null) {
        response.sendRedirect("../../form/signin.jsp");
        return;
    }

    // Ambil parameter dari form
    String id = request.getParameter("id");
    String type = request.getParameter("type");
    String name = request.getParameter("name");
    String username = request.getParameter("username");
    String email = request.getParameter("email");
    String password = request.getParameter("password");

    // Validasi input: semua field (kecuali password) harus diisi
    if (id == null || type == null || name == null || username == null || email == null ||
        id.trim().isEmpty() || type.trim().isEmpty() || name.trim().isEmpty() || 
        username.trim().isEmpty() || email.trim().isEmpty()) {
        
        session.setAttribute("message", "Semua field kecuali password harus diisi!");
        session.setAttribute("messageType", "error");
        
        String redirectType = (type != null ? type : "admin");
        response.sendRedirect("../../dashboardadmin.jsp?page=register&type=" + redirectType);
        return;
    }

    // Validasi tipe akun
    if (!type.equals("admin") && !type.equals("user")) {
        session.setAttribute("message", "Tipe akun tidak valid!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=register&type=admin");
        return;
    }

    try {
        int accountId = Integer.parseInt(id);

        // Periksa apakah username atau email sudah digunakan oleh akun lain
        String checkQuery = "SELECT COUNT(*) FROM " + type + " WHERE (username = ? OR email = ?) AND id != ?";
        PreparedStatement checkPs = conn.prepareStatement(checkQuery);
        checkPs.setString(1, username.trim());
        checkPs.setString(2, email.trim());
        checkPs.setInt(3, accountId);
        
        ResultSet checkRs = checkPs.executeQuery();
        checkRs.next();
        
        if (checkRs.getInt(1) > 0) {
            session.setAttribute("message", "Username atau email sudah digunakan oleh akun lain!");
            session.setAttribute("messageType", "error");
            
            checkRs.close();
            checkPs.close();
            
            response.sendRedirect("../../dashboardadmin.jsp?page=register&type=" + type);
            return;
        }
        
        checkRs.close();
        checkPs.close();

        // Siapkan query update
        String updateQuery;
        PreparedStatement updatePs;

        // Jika password diisi, maka update password
        if (password != null && !password.trim().isEmpty()) {
            // Hash password baru menggunakan SHA-256
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(password.getBytes(StandardCharsets.UTF_8));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            String hashedPassword = hexString.toString();

            updateQuery = "UPDATE " + type + " SET name = ?, username = ?, email = ?, password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            updatePs = conn.prepareStatement(updateQuery);
            updatePs.setString(1, name.trim());
            updatePs.setString(2, username.trim());
            updatePs.setString(3, email.trim());
            updatePs.setString(4, hashedPassword);
            updatePs.setInt(5, accountId);
        } else {
            // Jika password kosong, update tanpa mengubah password
            updateQuery = "UPDATE " + type + " SET name = ?, username = ?, email = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            updatePs = conn.prepareStatement(updateQuery);
            updatePs.setString(1, name.trim());
            updatePs.setString(2, username.trim());
            updatePs.setString(3, email.trim());
            updatePs.setInt(4, accountId);
        }
        
        int result = updatePs.executeUpdate();
        updatePs.close();

        if (result > 0) {
            session.setAttribute("message", "Akun " + type + " berhasil diperbarui!");
            session.setAttribute("messageType", "success");
        } else {
            session.setAttribute("message", "Gagal memperbarui akun!");
            session.setAttribute("messageType", "error");
        }

    } catch (Exception e) {
        session.setAttribute("message", "Terjadi kesalahan pada server: " + e.getMessage());
        session.setAttribute("messageType", "error");
    }

    // Redirect kembali ke halaman register
    response.sendRedirect("../../dashboardadmin.jsp?page=register&type=" + type);
%>
