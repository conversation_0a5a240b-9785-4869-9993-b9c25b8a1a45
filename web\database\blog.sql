-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jun 11, 2025 at 12:07 PM
-- Server version: 8.0.30
-- PHP Version: 8.1.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `arqeta`
--

-- --------------------------------------------------------

--
-- Table structure for table `blog`
--

CREATE TABLE `blog` (
  `id` int NOT NULL,
  `title` varchar(255) NOT NULL,
  `image` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `blog`
--

INSERT INTO `blog` (`id`, `title`, `image`, `content`, `created_at`, `updated_at`) VALUES
(1, 'Tren Desain UI Web 2025', 'blog-1.jpg', 'Pada tahun 2025, tren desain UI web semakin berkembang dengan fokus pada pengalaman pengguna yang lebih personal dan imersif. Beberapa tren yang menonjol antara lain: penggunaan AI untuk personalisasi konten, desain 3D yang lebih realistis, dark mode sebagai pilihan default, dan animasi mikro yang lebih halus. Selain itu, penggunaan warna gradien dan tipografi yang berani juga semakin populer. Desainer UI perlu terus mengikuti perkembangan ini untuk menciptakan antarmuka yang modern dan menarik bagi pengguna.', '2025-05-26 08:00:00', '2025-05-26 08:00:00'),
(2, 'Tips Meningkatkan UX pada Mobile App', 'blog-2.jpg', 'Meningkatkan UX pada aplikasi mobile adalah kunci untuk mendapatkan pengguna loyal. Beberapa tips yang bisa diterapkan: pastikan onboarding yang simpel dan jelas, gunakan gestur yang intuitif, prioritaskan kecepatan loading, terapkan desain yang konsisten, dan berikan feedback yang jelas pada setiap interaksi. Jangan lupa untuk melakukan testing dengan pengguna nyata untuk mendapatkan masukan yang berharga. Dengan memperhatikan aspek-aspek ini, aplikasi mobile Anda akan memberikan pengalaman yang lebih baik bagi pengguna.', '2025-05-26 09:30:00', '2025-05-26 09:30:00'),
(3, 'Pentingnya Aksesibilitas dalam Desain UI', 'blog-3.jpg', 'Aksesibilitas adalah aspek penting dalam desain UI yang sering terabaikan. Desain yang aksesibel memastikan semua pengguna, termasuk mereka dengan keterbatasan, dapat menggunakan produk Anda dengan baik. Beberapa praktik terbaik meliputi: penggunaan kontras warna yang cukup, teks alternatif untuk gambar, navigasi keyboard yang baik, dan struktur konten yang jelas. Dengan menerapkan prinsip aksesibilitas, tidak hanya memperluas jangkauan pengguna tetapi juga meningkatkan kualitas UI secara keseluruhan untuk semua orang.', '2025-05-26 10:45:00', '2025-05-26 10:45:00');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `blog`
--
ALTER TABLE `blog`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `blog`
--
ALTER TABLE `blog`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
