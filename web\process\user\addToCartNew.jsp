<%--
    Document   : addToCart
    Created on : Jun 16, 2025
    Author     : Arqeta
    Description: Add items to cart
--%>

<%@page contentType="application/json" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.math.BigDecimal"%>
<%@include file="../../config/connection.jsp" %>

<%
// Set response headers
response.setContentType("application/json");
response.setCharacterEncoding("UTF-8");
response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
response.setHeader("Pragma", "no-cache");
response.setDateHeader("Expires", 0);

String jsonResponse = "";
try {
    String userId = (String) session.getAttribute("userId");
    Boolean isLoggedIn = (Boolean) session.getAttribute("isLoggedIn");
    
    // Debug logging
    System.out.println("DEBUG - userId: " + userId);
    System.out.println("DEBUG - isLoggedIn: " + isLoggedIn);
    
    if (userId == null || isLoggedIn == null || !isLoggedIn) {
        jsonResponse = "{\"success\": false, \"message\": \"Anda harus login terlebih dahulu\"}";
        out.print(jsonResponse);
        return;
    }
    
    String serviceIdParam = request.getParameter("serviceId");
    String quantityParam = request.getParameter("quantity");
    
    // Debug logging
    System.out.println("DEBUG - serviceIdParam: " + serviceIdParam);
    System.out.println("DEBUG - quantityParam: " + quantityParam);
    
    if (serviceIdParam == null || quantityParam == null || serviceIdParam.trim().isEmpty() || quantityParam.trim().isEmpty()) {
        jsonResponse = "{\"success\": false, \"message\": \"Parameter tidak lengkap - serviceId: " + serviceIdParam + ", quantity: " + quantityParam + "\"}";
        out.print(jsonResponse);
        return;
    }
    
    int serviceId = Integer.parseInt(serviceIdParam);
    int quantity = Integer.parseInt(quantityParam);
    
    if (quantity <= 0) {
        jsonResponse = "{\"success\": false, \"message\": \"Kuantitas harus lebih dari 0\"}";
        out.print(jsonResponse);
        return;
    }
    
    // Get service details
    PreparedStatement psService = conn.prepareStatement("SELECT id, name, price, quantity FROM services WHERE id = ?");
    psService.setInt(1, serviceId);
    ResultSet rsService = psService.executeQuery();
    
    if (!rsService.next()) {
        jsonResponse = "{\"success\": false, \"message\": \"Layanan tidak ditemukan\"}";
        out.print(jsonResponse);
        rsService.close();
        psService.close();
        return;
    }
    
    String serviceName = rsService.getString("name");
    BigDecimal servicePrice = rsService.getBigDecimal("price");
    int availableQuantity = rsService.getInt("quantity");
    rsService.close();
    psService.close();
    
    if (availableQuantity < quantity) {
        jsonResponse = "{\"success\": false, \"message\": \"Stok layanan tidak mencukupi\"}";
        out.print(jsonResponse);
        return;
    }
    
    // Check if item already exists in cart
    PreparedStatement psCheck = conn.prepareStatement("SELECT id, quantity FROM cart WHERE user_id = ? AND service_id = ?");
    psCheck.setInt(1, Integer.parseInt(userId));
    psCheck.setInt(2, serviceId);
    ResultSet rsCheck = psCheck.executeQuery();
    
    if (rsCheck.next()) {
        // Update existing cart item
        int existingQuantity = rsCheck.getInt("quantity");
        int newQuantity = existingQuantity + quantity;
        int cartId = rsCheck.getInt("id");
        rsCheck.close();
        psCheck.close();
        
        if (newQuantity > availableQuantity) {
            jsonResponse = "{\"success\": false, \"message\": \"Total kuantitas melebihi stok yang tersedia\"}";
            out.print(jsonResponse);
            return;
        }
        
        PreparedStatement psUpdate = conn.prepareStatement("UPDATE cart SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        psUpdate.setInt(1, newQuantity);
        psUpdate.setInt(2, cartId);
        int updateResult = psUpdate.executeUpdate();
        psUpdate.close();
        
        if (updateResult > 0) {
            jsonResponse = "{\"success\": true, \"message\": \"Layanan berhasil ditambahkan ke keranjang\"}";
        } else {
            jsonResponse = "{\"success\": false, \"message\": \"Gagal memperbarui keranjang\"}";
        }
    } else {
        // Insert new cart item
        rsCheck.close();
        psCheck.close();
        
        PreparedStatement psInsert = conn.prepareStatement("INSERT INTO cart (user_id, service_id, service_name, price, quantity) VALUES (?, ?, ?, ?, ?)");
        psInsert.setInt(1, Integer.parseInt(userId));
        psInsert.setInt(2, serviceId);
        psInsert.setString(3, serviceName);
        psInsert.setBigDecimal(4, servicePrice);
        psInsert.setInt(5, quantity);
        int insertResult = psInsert.executeUpdate();
        psInsert.close();
        
        if (insertResult > 0) {
            jsonResponse = "{\"success\": true, \"message\": \"Layanan berhasil ditambahkan ke keranjang\"}";
        } else {
            jsonResponse = "{\"success\": false, \"message\": \"Gagal menambahkan layanan ke keranjang\"}";
        }
    }
} catch (NumberFormatException e) {
    jsonResponse = "{\"success\": false, \"message\": \"Format parameter tidak valid: " + e.getMessage() + "\"}";
    e.printStackTrace();
} catch (SQLException e) {
    jsonResponse = "{\"success\": false, \"message\": \"Kesalahan database: " + e.getMessage() + "\"}";
    e.printStackTrace();
} catch (Exception e) {
    jsonResponse = "{\"success\": false, \"message\": \"Terjadi kesalahan sistem: " + e.getMessage() + "\"}";
    e.printStackTrace();
} finally {
    try {
        if (conn != null && !conn.isClosed()) {
            conn.close();
        }
    } catch (SQLException se) {
        se.printStackTrace();
    }
}

// Ensure we always output a response
if (jsonResponse == null || jsonResponse.trim().isEmpty()) {
    jsonResponse = "{\"success\": false, \"message\": \"Tidak ada respons dari server\"}";
}

out.print(jsonResponse);
out.flush();
%>
