<%--
    Document   : connection-json
    Created on : June 17, 2025
    Author     : Arqeta
    Description: Database connection 
--%>

<%@page pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>

<%
    // Database connection parameters
    String jdbcURL = "******************************************************************************************************";
    String dbUser = "root";
    String dbPassword = "";
    Connection conn = null;

    try {
        // Load MySQL JDBC driver
        Class.forName("com.mysql.cj.jdbc.Driver");

        // Create database connection
        conn = DriverManager.getConnection(jdbcURL, dbUser, dbPassword);
    } catch (ClassNotFoundException e) {
        throw new RuntimeException("MySQL JDBC Driver not found: " + e.getMessage());
    } catch (SQLException e) {
        throw new RuntimeException("Database connection failed: " + e.getMessage());
    }
%>
