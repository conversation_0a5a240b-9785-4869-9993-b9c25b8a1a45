<%--
    Document   : updateCart
    Created on : Jun 16, 2025
    Author     : Arqeta
    Description: Handle cart updates (quantity changes, item removal, cart count)
--%>

<%@page contentType="application/json" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.math.BigDecimal"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Set response type to JSON
    response.setContentType("application/json");
    response.setCharacterEncoding("UTF-8");

    String jsonResponse = "";

    try {
        // Check if user is logged in
        String userId = (String) session.getAttribute("userId");
        Boolean isLoggedIn = (Boolean) session.getAttribute("isLoggedIn");

        if (userId == null || isLoggedIn == null || !isLoggedIn) {
            jsonResponse = "{\"success\": false, \"message\": \"Anda harus login terlebih dahulu\"}";
            out.print(jsonResponse);
            return;
        }

        // Get action parameter
        String action = request.getParameter("action");

        if (action == null || action.trim().isEmpty()) {
            jsonResponse = "{\"success\": false, \"message\": \"Action tidak ditemukan\"}";
            out.print(jsonResponse);
            return;
        }

        if ("count".equals(action)) {
            // Get cart count for user
            PreparedStatement psCount = conn.prepareStatement(
                "SELECT COUNT(*) as total_items, COALESCE(SUM(quantity), 0) as total_quantity FROM cart WHERE user_id = ?"
            );
            psCount.setInt(1, Integer.parseInt(userId));
            ResultSet rsCount = psCount.executeQuery();

            if (rsCount.next()) {
                int totalItems = rsCount.getInt("total_items");
                int totalQuantity = rsCount.getInt("total_quantity");
                jsonResponse = "{\"success\": true, \"totalItems\": " + totalItems + ", \"totalQuantity\": " + totalQuantity + "}";
            } else {
                jsonResponse = "{\"success\": true, \"totalItems\": 0, \"totalQuantity\": 0}";
            }
            rsCount.close();
            psCount.close();

        } else if ("update".equals(action)) {
            // Update cart item quantity
            String cartIdParam = request.getParameter("cartId");
            String quantityParam = request.getParameter("quantity");

            if (cartIdParam == null || quantityParam == null || cartIdParam.trim().isEmpty() || quantityParam.trim().isEmpty()) {
                jsonResponse = "{\"success\": false, \"message\": \"Parameter tidak lengkap\"}";
                out.print(jsonResponse);
                return;
            }

            int cartId;
            int quantity;
            try {
                cartId = Integer.parseInt(cartIdParam);
                quantity = Integer.parseInt(quantityParam);
            } catch (NumberFormatException e) {
                jsonResponse = "{\"success\": false, \"message\": \"Format parameter tidak valid\"}";
                out.print(jsonResponse);
                return;
            }

            if (quantity <= 0) {
                jsonResponse = "{\"success\": false, \"message\": \"Kuantitas harus lebih dari 0\"}";
                out.print(jsonResponse);
                return;
            }

            // Verify cart item belongs to user and get service details
            PreparedStatement psVerify = conn.prepareStatement(
                "SELECT c.service_id, s.quantity as available_quantity FROM cart c " +
                "JOIN services s ON c.service_id = s.id " +
                "WHERE c.id = ? AND c.user_id = ?"
            );
            psVerify.setInt(1, cartId);
            psVerify.setInt(2, Integer.parseInt(userId));
            ResultSet rsVerify = psVerify.executeQuery();

            if (!rsVerify.next()) {
                jsonResponse = "{\"success\": false, \"message\": \"Item keranjang tidak ditemukan\"}";
                out.print(jsonResponse);
                return;
            }

            int availableQuantity = rsVerify.getInt("available_quantity");
            rsVerify.close();
            psVerify.close();

            // Check if requested quantity exceeds available stock
            if (quantity > availableQuantity) {
                jsonResponse = "{\"success\": false, \"message\": \"Kuantitas melebihi stok yang tersedia. Stok tersedia: " + availableQuantity + "\"}";
                out.print(jsonResponse);
                return;
            }

            // Update cart item
            PreparedStatement psUpdate = conn.prepareStatement(
                "UPDATE cart SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?"
            );
            psUpdate.setInt(1, quantity);
            psUpdate.setInt(2, cartId);
            psUpdate.setInt(3, Integer.parseInt(userId));

            int updateResult = psUpdate.executeUpdate();
            psUpdate.close();

            if (updateResult > 0) {
                jsonResponse = "{\"success\": true, \"message\": \"Kuantitas berhasil diperbarui\"}";
            } else {
                jsonResponse = "{\"success\": false, \"message\": \"Gagal memperbarui kuantitas\"}";
            }

        } else if ("remove".equals(action)) {
            // Remove cart item
            String cartIdParam = request.getParameter("cartId");

            if (cartIdParam == null || cartIdParam.trim().isEmpty()) {
                jsonResponse = "{\"success\": false, \"message\": \"Cart ID tidak ditemukan\"}";
                out.print(jsonResponse);
                return;
            }

            int cartId;
            try {
                cartId = Integer.parseInt(cartIdParam);
            } catch (NumberFormatException e) {
                jsonResponse = "{\"success\": false, \"message\": \"Format Cart ID tidak valid\"}";
                out.print(jsonResponse);
                return;
            }

            // Remove cart item
            PreparedStatement psRemove = conn.prepareStatement(
                "DELETE FROM cart WHERE id = ? AND user_id = ?"
            );
            psRemove.setInt(1, cartId);
            psRemove.setInt(2, Integer.parseInt(userId));

            int removeResult = psRemove.executeUpdate();
            psRemove.close();

            if (removeResult > 0) {
                jsonResponse = "{\"success\": true, \"message\": \"Item berhasil dihapus dari keranjang\"}";
            } else {
                jsonResponse = "{\"success\": false, \"message\": \"Gagal menghapus item dari keranjang\"}";
            }

        } else if ("clear".equals(action)) {
            // Clear all cart items for user
            PreparedStatement psClear = conn.prepareStatement(
                "DELETE FROM cart WHERE user_id = ?"
            );
            psClear.setInt(1, Integer.parseInt(userId));

            int clearResult = psClear.executeUpdate();
            psClear.close();

            if (clearResult >= 0) { // >= 0 because even 0 rows affected is success (empty cart)
                jsonResponse = "{\"success\": true, \"message\": \"Keranjang berhasil dikosongkan\", \"itemsRemoved\": " + clearResult + "}";
            } else {
                jsonResponse = "{\"success\": false, \"message\": \"Gagal mengosongkan keranjang\"}";
            }

        } else {
            jsonResponse = "{\"success\": false, \"message\": \"Action tidak valid\"}";
        }

    } catch (SQLException e) {
        jsonResponse = "{\"success\": false, \"message\": \"Terjadi kesalahan database: " + e.getMessage().replace("\"", "\\\"") + "\"}";
        e.printStackTrace();
    } catch (Exception e) {
        jsonResponse = "{\"success\": false, \"message\": \"Terjadi kesalahan sistem: " + e.getMessage().replace("\"", "\\\"") + "\"}";
        e.printStackTrace();
    } finally {
        // Close database connection
        try {
            if (conn != null && !conn.isClosed()) {
                conn.close();
            }
        } catch (SQLException se) {
            se.printStackTrace();
        }
    }

    out.print(jsonResponse);
%>
