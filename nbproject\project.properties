annotation.processing.enabled=true
annotation.processing.enabled.in.editor=true
annotation.processing.processors.list=
annotation.processing.run.all.processors=true
annotation.processing.source.output=${build.generated.sources.dir}/ap-source-output
build.classes.dir=${build.web.dir}/WEB-INF/classes
build.classes.excludes=**/*.java,**/*.form
build.dir=build
build.generated.dir=${build.dir}/generated
build.generated.sources.dir=${build.dir}/generated-sources
build.test.classes.dir=${build.dir}/test/classes
build.test.results.dir=${build.dir}/test/results
build.web.dir=${build.dir}/web
build.web.excludes=${build.classes.excludes}
client.urlPart=
compile.jsps=false
conf.dir=${source.root}/conf
debug.classpath=${build.classes.dir}:${javac.classpath}
debug.test.classpath=\
    ${run.test.classpath}
display.browser=true
# Files to be excluded from distribution war
dist.archive.excludes=
dist.dir=dist
dist.ear.war=${dist.dir}/${war.ear.name}
dist.javadoc.dir=${dist.dir}/javadoc
dist.war=${dist.dir}/${war.name}
excludes=
file.reference.mysql-connector-j-9.3.0.jar=C:\\Library Java Component\\mysql-connector-j-9.3.0\\mysql-connector-j-9.3.0.jar
includes=**
j2ee.compile.on.save=true
j2ee.copy.static.files.on.save=true
j2ee.deploy.on.save=true
j2ee.platform=10-web
j2ee.platform.classpath=${j2ee.server.home}/modules/jakarta.activation-api.jar:${j2ee.server.home}/modules/jakarta.annotation-api.jar:${j2ee.server.home}/modules/jakarta.authentication-api.jar:${j2ee.server.home}/modules/jakarta.authorization-api.jar:${j2ee.server.home}/modules/jakarta.batch-api.jar:${j2ee.server.home}/modules/jakarta.ejb-api.jar:${j2ee.server.home}/modules/jakarta.el-api.jar:${j2ee.server.home}/modules/jakarta.enterprise.cdi-api.jar:${j2ee.server.home}/modules/jakarta.enterprise.concurrent-api.jar:${j2ee.server.home}/modules/jakarta.enterprise.concurrent.jar:${j2ee.server.home}/modules/jakarta.enterprise.lang-model.jar:${j2ee.server.home}/modules/jakarta.faces.jar:${j2ee.server.home}/modules/jakarta.inject-api.jar:${j2ee.server.home}/modules/jakarta.interceptor-api.jar:${j2ee.server.home}/modules/jakarta.jms-api.jar:${j2ee.server.home}/modules/jakarta.json-api.jar:${j2ee.server.home}/modules/jakarta.json.bind-api.jar:${j2ee.server.home}/modules/jakarta.mail-api.jar:${j2ee.server.home}/modules/jakarta.mvc-api.jar:${j2ee.server.home}/modules/jakarta.persistence-api.jar:${j2ee.server.home}/modules/jakarta.resource-api.jar:${j2ee.server.home}/modules/jakarta.security.enterprise-api.jar:${j2ee.server.home}/modules/jakarta.security.enterprise.jar:${j2ee.server.home}/modules/jakarta.servlet-api.jar:${j2ee.server.home}/modules/jakarta.servlet.jsp-api.jar:${j2ee.server.home}/modules/jakarta.servlet.jsp.jstl-api.jar:${j2ee.server.home}/modules/jakarta.servlet.jsp.jstl.jar:${j2ee.server.home}/modules/jakarta.transaction-api.jar:${j2ee.server.home}/modules/jakarta.validation-api.jar:${j2ee.server.home}/modules/jakarta.websocket-api.jar:${j2ee.server.home}/modules/jakarta.websocket-client-api.jar:${j2ee.server.home}/modules/jakarta.ws.rs-api.jar:${j2ee.server.home}/modules/jakarta.xml.bind-api.jar:${j2ee.server.home}/modules/jaxb-osgi.jar:${j2ee.server.home}/modules/webservices-osgi.jar:${j2ee.server.home}/modules/weld-osgi-bundle.jar
j2ee.platform.embeddableejb.classpath=${j2ee.server.home}/lib/embedded/glassfish-embedded-static-shell.jar
j2ee.platform.wscompile.classpath=${j2ee.server.home}/modules/webservices-osgi.jar
j2ee.platform.wsgen.classpath=${j2ee.server.home}/modules/webservices-osgi.jar:${j2ee.server.home}/modules/webservices-api-osgi.jar:${j2ee.server.home}/modules/jaxb-osgi.jar
j2ee.platform.wsimport.classpath=${j2ee.server.home}/modules/webservices-osgi.jar:${j2ee.server.home}/modules/webservices-api-osgi.jar:${j2ee.server.home}/modules/jaxb-osgi.jar
j2ee.platform.wsit.classpath=
j2ee.server.type=gfv700ee10
jar.compress=false
javac.classpath=\
    ${file.reference.mysql-connector-j-9.3.0.jar}
# Space-separated list of extra javac options
javac.compilerargs=
javac.debug=true
javac.deprecation=false
javac.processorpath=\
    ${javac.classpath}
javac.source=16
javac.target=16
javac.test.classpath=\
    ${javac.classpath}:\
    ${build.classes.dir}
javac.test.processorpath=\
    ${javac.test.classpath}
javadoc.additionalparam=
javadoc.author=false
javadoc.encoding=${source.encoding}
javadoc.noindex=false
javadoc.nonavbar=false
javadoc.notree=false
javadoc.preview=true
javadoc.private=false
javadoc.splitindex=true
javadoc.use=true
javadoc.version=false
javadoc.windowtitle=
lib.dir=${web.docbase.dir}/WEB-INF/lib
no.dependencies=false
persistence.xml.dir=${conf.dir}
platform.active=default_platform
resource.dir=setup
run.test.classpath=\
    ${javac.test.classpath}:\
    ${build.test.classes.dir}
# Space-separated list of JVM arguments used when running a class with a main method or a unit test
# (you may also define separate properties like run-sys-prop.name=value instead of -Dname=value):
runmain.jvmargs=
source.encoding=UTF-8
source.root=src
src.dir=${source.root}/java
test.src.dir=test
war.content.additional=
war.ear.name=${war.name}
war.name=arqeta.war
web.docbase.dir=web
webinf.dir=web/WEB-INF
