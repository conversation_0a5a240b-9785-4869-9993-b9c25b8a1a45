<%--
    Document   : servicesdata
    Created on : Jun 1, 2025, 7:11:55 PM
    Author     : Arqeta
    Description: Halaman data layanan di dashboard admin
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<div class="page-content">
    <div class="page-header">
        <h2>Data Layanan</h2>
        <div class="action-bar">
            <div class="export-buttons">
                <button class="btn-export btn-word" onclick="exportToWord()" title="Ekspor ke Word">
                    <i data-feather="file-text"></i>
                    <span>Word</span>
                </button>
                <button class="btn-export btn-json" onclick="exportToJSON()" title="Ekspor ke JSON">
                    <i data-feather="code"></i>
                    <span>JSON</span>
                </button>
            </div>
            <button class="btn-primary" onclick="openAddServiceModal()">
                <i data-feather="plus"></i> <PERSON><PERSON> Layanan
            </button>
        </div>
    </div>

    <div class="table-container">
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nama Layanan</th>
                        <th>Gambar</th>
                        <th>Jumlah</th>
                        <th>Harga</th>
                        <th>Waktu Pembuatan</th>
                        <th>Waktu Perubahan</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <%
                        try {
                            // Query untuk mengambil data layanan berurutan berdasarkan ID
                            PreparedStatement ps = conn.prepareStatement("SELECT * FROM services ORDER BY id ASC");
                            ResultSet rs = ps.executeQuery();

                            while (rs.next()) {
                                int id = rs.getInt("id");
                                String name = rs.getString("name");
                                String images = rs.getString("images");
                                int quantity = rs.getInt("quantity");
                                double price = rs.getDouble("price");
                                String createdAt = rs.getString("created_at");
                                String updatedAt = rs.getString("updated_at");
                    %>
                    <tr>
                        <td><%= id %></td>
                        <td><%= name %></td>
                        <td>
                            <% if (images != null && !images.isEmpty()) { %>
                                <img src="dist/img/<%= images %>" alt="<%= name %>" class="table-image"
                                     onerror="this.src='dist/img/default-service.png';">
                            <% } else { %>
                                <span class="no-image">Tidak ada gambar</span>
                            <% } %>
                        </td>
                        <td><%= quantity %></td>
                        <td>Rp <%= String.format("%,.0f", price) %></td>
                        <td><%= createdAt %></td>
                        <td><%= updatedAt %></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-edit" onclick="openEditServiceModal(<%= id %>, '<%= name.replace("'", "\\'") %>', '<%= images %>', <%= quantity %>, <%= price %>)" title="Edit">
                                    <i data-feather="edit"></i>
                                </button>
                                <button class="btn-delete" onclick="openDeleteServiceModal(<%= id %>, '<%= name.replace("'", "\\'") %>')" title="Hapus">
                                    <i data-feather="trash-2"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <%
                            }
                            rs.close();
                            ps.close();
                        } catch (SQLException e) {
                            out.println("<tr><td colspan='8'>Error: " + e.getMessage() + "</td></tr>");
                        }
                    %>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Tambah Layanan -->
<div id="addServiceModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Tambah Layanan</h3>
            <span class="close-modal" onclick="closeModal('addServiceModal')">&times;</span>
        </div>
        <form id="addServiceForm" method="POST" action="process/admin/addService.jsp" enctype="multipart/form-data">
            <div class="form-group">
                <label for="addServiceName">Nama Layanan</label>
                <input type="text" id="addServiceName" name="name" required>
            </div>
            <div class="form-group">
                <label for="addServiceImage">Gambar Layanan</label>
                <input type="file" id="addServiceImage" name="image" accept="image/*" required>
                <small class="form-text">Format yang didukung: JPG, PNG, GIF (Maksimal 10MB)</small>
            </div>
            <div class="form-group">
                <label for="addServiceQuantity">Jumlah</label>
                <input type="number" id="addServiceQuantity" name="quantity" min="1" required>
            </div>
            <div class="form-group">
                <label for="addServicePrice">Harga</label>
                <input type="number" id="addServicePrice" name="price" min="0" step="0.01" required>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn-secondary" onclick="closeModal('addServiceModal')">Batal</button>
                <button type="submit" class="btn-primary">Tambah</button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Edit Layanan -->
<div id="editServiceModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Edit Layanan</h3>
            <span class="close-modal" onclick="closeModal('editServiceModal')">&times;</span>
        </div>
        <form id="editServiceForm" method="POST" action="process/admin/editService.jsp" enctype="multipart/form-data">
            <input type="hidden" id="editServiceId" name="id">
            <div class="form-group">
                <label for="editServiceName">Nama Layanan</label>
                <input type="text" id="editServiceName" name="name" required>
            </div>
            <div class="form-group">
                <label for="editServiceImage">Gambar Layanan (kosongkan jika tidak ingin mengubah)</label>
                <input type="file" id="editServiceImage" name="image" accept="image/*">
                <small class="form-text">Format yang didukung: JPG, PNG, GIF (Maksimal 10MB)</small>
            </div>
            <div class="form-group">
                <label for="editServiceQuantity">Jumlah</label>
                <input type="number" id="editServiceQuantity" name="quantity" min="1" required>
            </div>
            <div class="form-group">
                <label for="editServicePrice">Harga</label>
                <input type="number" id="editServicePrice" name="price" min="0" step="0.01" required>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn-secondary" onclick="closeModal('editServiceModal')">Batal</button>
                <button type="submit" class="btn-primary">Update</button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Konfirmasi Hapus -->
<div id="deleteServiceModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Konfirmasi Hapus</h3>
            <span class="close-modal" onclick="closeModal('deleteServiceModal')">&times;</span>
        </div>
        <div class="modal-body">
            <p>Apakah Anda yakin ingin menghapus layanan "<span id="deleteServiceName"></span>"?</p>
            <p class="warning-text">Tindakan ini tidak dapat dibatalkan.</p>
        </div>
        <div class="modal-actions">
            <button type="button" class="btn-secondary" onclick="closeModal('deleteServiceModal')">Batal</button>
            <button type="button" class="btn-danger" onclick="confirmDeleteService()">Hapus</button>
        </div>
    </div>
</div>

<script>
    let deleteServiceId = null;

    function openAddServiceModal() {
        document.getElementById('addServiceModal').style.display = 'block';
    }

    function openEditServiceModal(id, name, image, quantity, price) {
        document.getElementById('editServiceId').value = id;
        document.getElementById('editServiceName').value = name;
        document.getElementById('editServiceQuantity').value = quantity;
        document.getElementById('editServicePrice').value = price;
        document.getElementById('editServiceModal').style.display = 'block';
    }

    function openDeleteServiceModal(id, name) {
        deleteServiceId = id;
        document.getElementById('deleteServiceName').textContent = name;
        document.getElementById('deleteServiceModal').style.display = 'block';
    }

    function confirmDeleteService() {
        if (deleteServiceId) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'process/admin/deleteService.jsp';

            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'id';
            idInput.value = deleteServiceId;

            form.appendChild(idInput);
            document.body.appendChild(form);
            form.submit();
        }
    }

    function closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
        // Reset form jika ada
        const form = document.querySelector('#' + modalId + ' form');
        if (form) {
            form.reset();
        }
    }

    /**
     * Ekspor data layanan ke format Word.
     */
    function exportToWord() {
        // Tampilkan loading indicator
        if (typeof showNotification === "function") {
            showNotification("Sedang memproses ekspor Word...", "info");
        }

        // Buat form untuk submit ke processor
        const form = document.createElement("form");
        form.method = "POST";
        form.action =
            "<%= request.getContextPath() %>/process/admin/exportServices.jsp";

        const formatInput = document.createElement("input");
        formatInput.type = "hidden";
        formatInput.name = "format";
        formatInput.value = "word";

        form.appendChild(formatInput);
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }

    /**
     * Ekspor data layanan ke format JSON.
     */
    function exportToJSON() {
        // Tampilkan loading indicator
        if (typeof showNotification === "function") {
            showNotification("Sedang memproses ekspor JSON...", "info");
        }

        // Buat form untuk submit ke processor
        const form = document.createElement("form");
        form.method = "POST";
        form.action =
            "<%= request.getContextPath() %>/process/admin/exportServices.jsp";

        const formatInput = document.createElement("input");
        formatInput.type = "hidden";
        formatInput.name = "format";
        formatInput.value = "json";

        form.appendChild(formatInput);
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }

    // Tutup modal ketika klik di luar modal
    window.onclick = function(event) {
        const modals = ['addServiceModal', 'editServiceModal', 'deleteServiceModal'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (event.target === modal) {
                closeModal(modalId);
            }
        });
    }
</script>
