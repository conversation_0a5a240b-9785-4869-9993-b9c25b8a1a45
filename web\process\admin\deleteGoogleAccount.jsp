<%-- 
    Document    : deleteGoogleAccount
    Created on  : Jun 14, 2025 
    Author      : Arqeta
    Description : Process untuk menghapus akun Google admin/user dengan validasi
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Periksa apakah user sudah login sebagai admin
    String adminId = (String) session.getAttribute("adminId");
    if (adminId == null) {
        response.sendRedirect("../../form/signin.jsp");
        return;
    }

    // Ambil parameter dari form
    String id = request.getParameter("id");
    String type = request.getParameter("type");

    // Validasi input
    if (id == null || type == null || id.trim().isEmpty() || type.trim().isEmpty()) {
        session.setAttribute("message", "Parameter tidak valid!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=google_accounts&type=admin");
        return;
    }

    // Validasi tipe akun
    if (!type.equals("admin") && !type.equals("user")) {
        session.setAttribute("message", "Tipe akun tidak valid!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=google_accounts&type=admin");
        return;
    }

    try {
        int googleAccountId = Integer.parseInt(id);
        String tableName = type + "_google";

        // Periksa apakah akun Google ada di database
        String checkQuery = "SELECT COUNT(*) FROM " + tableName + " WHERE id = ?";
        PreparedStatement checkPs = conn.prepareStatement(checkQuery);
        checkPs.setInt(1, googleAccountId);
        
        ResultSet checkRs = checkPs.executeQuery();
        checkRs.next();

        if (checkRs.getInt(1) == 0) {
            session.setAttribute("message", "Akun Google tidak ditemukan!");
            session.setAttribute("messageType", "error");
            
            checkRs.close();
            checkPs.close();
            
            response.sendRedirect("../../dashboardadmin.jsp?page=google_accounts&type=" + type);
            return;
        }

        checkRs.close();
        checkPs.close();

        // Hapus akun Google dari database
        String deleteQuery = "DELETE FROM " + tableName + " WHERE id = ?";
        PreparedStatement deletePs = conn.prepareStatement(deleteQuery);
        deletePs.setInt(1, googleAccountId);
        
        int result = deletePs.executeUpdate();
        deletePs.close();

        if (result > 0) {
            session.setAttribute("message", "Akun Google " + type + " berhasil dihapus!");
            session.setAttribute("messageType", "success");
        } else {
            session.setAttribute("message", "Gagal menghapus akun Google!");
            session.setAttribute("messageType", "error");
        }

    } catch (NumberFormatException e) {
        session.setAttribute("message", "ID akun Google tidak valid!");
        session.setAttribute("messageType", "error");
    } catch (Exception e) {
        session.setAttribute("message", "Terjadi kesalahan pada server: " + e.getMessage());
        session.setAttribute("messageType", "error");
    }

    // Redirect kembali ke halaman Google accounts
    response.sendRedirect("../../dashboardadmin.jsp?page=google_accounts&type=" + type);
%>
