<%--
    Document   : editService
    Created on : Jun 15, 2025
    Author     : Arqeta
    Description: Process untuk mengedit layanan
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>
<%@page import="java.io.*"%>
<%@page import="java.nio.file.*"%>
<%@page import="jakarta.servlet.http.Part"%>

<%
    // Pastikan request adalah POST
    if (!"POST".equalsIgnoreCase(request.getMethod())) {
        response.sendRedirect("../../dashboardadmin.jsp?page=services");
        return;
    }

    try {
        // Ambil parameter dari form
        String idStr = request.getParameter("id");
        String name = request.getParameter("name");
        String quantityStr = request.getParameter("quantity");
        String priceStr = request.getParameter("price");
        
        // Validasi input
        if (idStr == null || idStr.trim().isEmpty() ||
            name == null || name.trim().isEmpty() || 
            quantityStr == null || quantityStr.trim().isEmpty() ||
            priceStr == null || priceStr.trim().isEmpty()) {
            session.setAttribute("message", "Semua field harus diisi!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../dashboardadmin.jsp?page=services");
            return;
        }

        int id = Integer.parseInt(idStr);
        int quantity = Integer.parseInt(quantityStr);
        double price = Double.parseDouble(priceStr);

        // Ambil data layanan yang ada
        String currentImage = null;
        PreparedStatement psSelect = conn.prepareStatement("SELECT images FROM services WHERE id = ?");
        psSelect.setInt(1, id);
        ResultSet rs = psSelect.executeQuery();
        if (rs.next()) {
            currentImage = rs.getString("images");
        }
        rs.close();
        psSelect.close();

        // Handle file upload jika ada
        String fileName = currentImage; // Default menggunakan gambar yang sudah ada
        Part filePart = request.getPart("image");
        
        if (filePart != null && filePart.getSize() > 0) {
            // Validasi tipe file
            String contentType = filePart.getContentType();
            if (!contentType.startsWith("image/")) {
                session.setAttribute("message", "File harus berupa gambar!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboardadmin.jsp?page=services");
                return;
            }

            // Validasi ukuran file (maksimal 10MB)
            if (filePart.getSize() > 10 * 1024 * 1024) {
                session.setAttribute("message", "Ukuran file maksimal 10MB!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboardadmin.jsp?page=services");
                return;
            }

            // Generate nama file unik
            String originalFileName = filePart.getSubmittedFileName();
            String fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
            fileName = "service_" + System.currentTimeMillis() + fileExtension;

            // Path untuk menyimpan file
            String uploadPath = application.getRealPath("/dist/img/");
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            // Hapus file lama jika ada
            if (currentImage != null && !currentImage.isEmpty()) {
                File oldFile = new File(uploadPath + File.separator + currentImage);
                if (oldFile.exists()) {
                    oldFile.delete();
                }
            }

            // Simpan file baru
            String filePath = uploadPath + File.separator + fileName;
            try (InputStream input = filePart.getInputStream()) {
                Files.copy(input, Paths.get(filePath), StandardCopyOption.REPLACE_EXISTING);
            }
        }

        // Update database
        PreparedStatement ps = conn.prepareStatement(
            "UPDATE services SET name = ?, images = ?, quantity = ?, price = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        );
        ps.setString(1, name.trim());
        ps.setString(2, fileName);
        ps.setInt(3, quantity);
        ps.setDouble(4, price);
        ps.setInt(5, id);

        int result = ps.executeUpdate();
        ps.close();

        if (result > 0) {
            session.setAttribute("message", "Layanan berhasil diperbarui!");
            session.setAttribute("messageType", "success");
        } else {
            session.setAttribute("message", "Gagal memperbarui layanan!");
            session.setAttribute("messageType", "error");
        }

    } catch (NumberFormatException e) {
        session.setAttribute("message", "Format angka tidak valid!");
        session.setAttribute("messageType", "error");
    } catch (Exception e) {
        session.setAttribute("message", "Error: " + e.getMessage());
        session.setAttribute("messageType", "error");
        e.printStackTrace();
    }

    response.sendRedirect("../../dashboardadmin.jsp?page=services");
%>
