<%--
    Document   : deleteOtpProcess
    Created on : Jun 17, 2025
    Author     : Arqeta
    Description: Processing file untuk menghapus data riwayat OTP dari database
    Features: Menghapus record OTP berdasarkan ID yang diberikan
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Debug: Log request method dan parameters
    System.out.println("Delete OTP Process - Method: " + request.getMethod());
    System.out.println("Delete OTP Process - ID Parameter: " + request.getParameter("id"));
    
    // Ambil parameter ID dari request
    String idParam = request.getParameter("id");
    
    // Validasi parameter
    if (idParam == null || idParam.trim().isEmpty()) {
        System.out.println("Delete OTP Process - ID parameter is null or empty");
        session.setAttribute("message", "ID OTP tidak valid!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=otp");
        return;
    }
    
    int otpId = 0;
    try {
        otpId = Integer.parseInt(idParam);
        System.out.println("Delete OTP Process - Parsed OTP ID: " + otpId);
    } catch (NumberFormatException e) {
        System.out.println("Delete OTP Process - Invalid ID format: " + idParam);
        session.setAttribute("message", "Format ID OTP tidak valid!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=otp");
        return;
    }
    
    // Database operation variables
    PreparedStatement ps = null;
    ResultSet rs = null;
    
    try {
        // Pastikan koneksi database tersedia
        if (conn == null || conn.isClosed()) {
            System.out.println("Delete OTP Process - Database connection is null or closed");
            session.setAttribute("message", "Koneksi database tidak tersedia!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../dashboardadmin.jsp?page=otp");
            return;
        }
        
        System.out.println("Delete OTP Process - Using existing database connection");
        
        // Cek apakah OTP dengan ID tersebut ada
        ps = conn.prepareStatement("SELECT email, otp, used FROM otp_log WHERE id = ?");
        ps.setInt(1, otpId);
        rs = ps.executeQuery();
        
        if (rs.next()) {
            String email = rs.getString("email");
            String otp = rs.getString("otp");
            boolean used = rs.getBoolean("used");
            
            System.out.println("Delete OTP Process - Found OTP record: ID=" + otpId + ", Email=" + email + ", Used=" + used);
            
            // Tutup ResultSet dan PreparedStatement sebelum query berikutnya
            rs.close();
            ps.close();
            
            // Hapus data OTP dari database
            ps = conn.prepareStatement("DELETE FROM otp_log WHERE id = ?");
            ps.setInt(1, otpId);
            
            int rowsAffected = ps.executeUpdate();
            System.out.println("Delete OTP Process - Rows affected: " + rowsAffected);
            
            if (rowsAffected > 0) {
                // Berhasil menghapus
                String statusText = used ? "sudah digunakan" : "belum digunakan";
                System.out.println("Delete OTP Process - Successfully deleted OTP ID: " + otpId + " for email: " + email);
                session.setAttribute("message", "Riwayat OTP untuk email " + email + " (" + statusText + ") berhasil dihapus!");
                session.setAttribute("messageType", "success");
            } else {
                // Gagal menghapus
                System.out.println("Delete OTP Process - Failed to delete OTP ID: " + otpId + ", no rows affected");
                session.setAttribute("message", "Gagal menghapus riwayat OTP. Silakan coba lagi.");
                session.setAttribute("messageType", "error");
            }
        } else {
            // OTP dengan ID tersebut tidak ditemukan
            System.out.println("Delete OTP Process - OTP record not found for ID: " + otpId);
            session.setAttribute("message", "Data riwayat OTP tidak ditemukan!");
            session.setAttribute("messageType", "error");
        }
        
    } catch (SQLException e) {
        // Error database connection atau query
        System.out.println("Delete OTP Process - SQL error: " + e.getMessage());
        session.setAttribute("message", "Error database: " + e.getMessage());
        session.setAttribute("messageType", "error");
        e.printStackTrace();
    } catch (Exception e) {
        // Error umum lainnya
        System.out.println("Delete OTP Process - General error: " + e.getMessage());
        session.setAttribute("message", "Terjadi kesalahan: " + e.getMessage());
        session.setAttribute("messageType", "error");
        e.printStackTrace();
    } finally {
        // Tutup PreparedStatement dan ResultSet (koneksi akan ditutup oleh connection.jsp)
        try {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
            System.out.println("Delete OTP Process - Database resources closed");
        } catch (SQLException e) {
            System.out.println("Delete OTP Process - Error closing resources: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    // Redirect kembali ke halaman OTP log data
    System.out.println("Delete OTP Process - Redirecting to dashboard");
    response.sendRedirect("../../dashboardadmin.jsp?page=otp");
%>
