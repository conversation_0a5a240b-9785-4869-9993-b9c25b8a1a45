<%--
    Document   : deleteBlog
    Created on : Jun 15, 2025
    Author     : Arqeta
    Description: Process untuk menghapus blog
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>
<%@page import="java.io.*"%>

<%
    // Pastikan request adalah POST
    if (!"POST".equalsIgnoreCase(request.getMethod())) {
        response.sendRedirect("../../dashboardadmin.jsp?page=blog");
        return;
    }

    try {
        // Ambil parameter ID
        String idStr = request.getParameter("id");
        
        if (idStr == null || idStr.trim().isEmpty()) {
            session.setAttribute("message", "ID blog tidak valid!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../dashboardadmin.jsp?page=blog");
            return;
        }

        int id = Integer.parseInt(idStr);

        // Ambil nama file gambar sebelum menghapus
        String imageName = null;
        PreparedStatement psSelect = conn.prepareStatement("SELECT image FROM blog WHERE id = ?");
        psSelect.setInt(1, id);
        ResultSet rs = psSelect.executeQuery();
        if (rs.next()) {
            imageName = rs.getString("image");
        }
        rs.close();
        psSelect.close();

        // Hapus dari database
        PreparedStatement ps = conn.prepareStatement("DELETE FROM blog WHERE id = ?");
        ps.setInt(1, id);
        int result = ps.executeUpdate();
        ps.close();

        if (result > 0) {
            // Hapus file gambar jika ada
            if (imageName != null && !imageName.isEmpty()) {
                String uploadPath = application.getRealPath("/dist/img/");
                File imageFile = new File(uploadPath + File.separator + imageName);
                if (imageFile.exists()) {
                    imageFile.delete();
                }
            }

            session.setAttribute("message", "Blog berhasil dihapus!");
            session.setAttribute("messageType", "success");
        } else {
            session.setAttribute("message", "Blog tidak ditemukan atau gagal dihapus!");
            session.setAttribute("messageType", "error");
        }

    } catch (NumberFormatException e) {
        session.setAttribute("message", "ID blog tidak valid!");
        session.setAttribute("messageType", "error");
    } catch (Exception e) {
        session.setAttribute("message", "Error: " + e.getMessage());
        session.setAttribute("messageType", "error");
        e.printStackTrace();
    }

    response.sendRedirect("../../dashboardadmin.jsp?page=blog");
%>
