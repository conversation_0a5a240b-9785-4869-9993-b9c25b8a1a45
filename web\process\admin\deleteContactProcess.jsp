<%--
    Document   : deleteContactProcess
    Created on : Jun 16, 2025
    Author     : Arqeta
    Description: Process untuk menghapus pesan kontak
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Cek apakah admin sudah login
    String adminId = (String) session.getAttribute("adminId");
    if (adminId == null) {
        response.sendRedirect("../../form/signin.jsp");
        return;
    }

    String contactId = request.getParameter("id");
    if (contactId == null) {
        session.setAttribute("notification", "ID kontak tidak ditemukan!");
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=contact");
        return;
    }

    try {
        // Hapus pesan kontak dari database
        PreparedStatement ps = conn.prepareStatement("DELETE FROM contact WHERE id = ?");
        ps.setInt(1, Integer.parseInt(contactId));
        
        int result = ps.executeUpdate();
        ps.close();
        
        if (result > 0) {
            session.setAttribute("notification", "Pesan kontak berhasil dihapus!");
            session.setAttribute("notificationType", "success");
        } else {
            session.setAttribute("notification", "Gagal menghapus pesan kontak!");
            session.setAttribute("notificationType", "error");
        }
        
    } catch (NumberFormatException e) {
        session.setAttribute("notification", "Format ID tidak valid!");
        session.setAttribute("notificationType", "error");
    } catch (SQLException e) {
        session.setAttribute("notification", "Terjadi kesalahan database: " + e.getMessage());
        session.setAttribute("notificationType", "error");
    } catch (Exception e) {
        session.setAttribute("notification", "Terjadi kesalahan: " + e.getMessage());
        session.setAttribute("notificationType", "error");
    }

    response.sendRedirect("../../dashboardadmin.jsp?page=contact");
%>
