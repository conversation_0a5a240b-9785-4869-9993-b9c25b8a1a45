<%-- 
    Document    : addGoogleAccount
    Created on  : Jun 17, 2025 
    Author      : Arqeta
    Description : Process untuk menambah akun Google admin/user dari dashboard admin
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.time.LocalDateTime"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Periksa apakah user sudah login sebagai admin
    String adminId = (String) session.getAttribute("adminId");
    if (adminId == null) {
        response.sendRedirect("../../form/signin.jsp");
        return;
    }

    // Ambil data Google user dari parameter URL
    String name = request.getParameter("name");
    String email = request.getParameter("email");
    String photoURL = request.getParameter("photoURL");
    String uid = request.getParameter("uid");
    String type = request.getParameter("type");

    // Validasi input
    if (name == null || email == null || type == null) {
        session.setAttribute("notification", "Data tidak lengkap!");
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=google_accounts&type=" + (type != null ? type : "admin"));
        return;
    }

    // Validasi tipe akun
    if (!type.equals("admin") && !type.equals("user")) {
        session.setAttribute("notification", "Tipe akun tidak valid!");
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=google_accounts&type=admin");
        return;
    }

    try {
        String tableName = type + "_google";
        String parentTable = type;
        String idColumn = type + "_id";

        // Periksa apakah email sudah ada di tabel Google
        String checkGoogleQuery = "SELECT COUNT(*) FROM " + tableName + " WHERE email = ?";
        PreparedStatement checkGooglePs = conn.prepareStatement(checkGoogleQuery);
        checkGooglePs.setString(1, email);
        ResultSet checkGoogleRs = checkGooglePs.executeQuery();
        checkGoogleRs.next();

        if (checkGoogleRs.getInt(1) > 0) {
            session.setAttribute("notification", "Email Google sudah terdaftar untuk " + type + "!");
            session.setAttribute("notificationType", "error");
            checkGoogleRs.close();
            checkGooglePs.close();
            response.sendRedirect("../../dashboardadmin.jsp?page=google_accounts&type=" + type);
            return;
        }
        checkGoogleRs.close();
        checkGooglePs.close();

        // Periksa apakah email sudah ada di tabel utama (admin/user)
        String checkMainQuery = "SELECT id FROM " + parentTable + " WHERE email = ?";
        PreparedStatement checkMainPs = conn.prepareStatement(checkMainQuery);
        checkMainPs.setString(1, email);
        ResultSet checkMainRs = checkMainPs.executeQuery();

        int parentId = 0;
        if (checkMainRs.next()) {
            // Email sudah ada di tabel utama, gunakan ID yang ada
            parentId = checkMainRs.getInt("id");
        } else {
            // Email belum ada, buat akun baru di tabel utama
            String insertMainQuery = "INSERT INTO " + parentTable + " (name, username, email, password, created_at, updated_at) VALUES (?, ?, ?, '', NOW(), NOW())";
            PreparedStatement insertMainPs = conn.prepareStatement(insertMainQuery, Statement.RETURN_GENERATED_KEYS);
            insertMainPs.setString(1, name);
            insertMainPs.setString(2, email.split("@")[0]); // Gunakan bagian pertama email sebagai username
            insertMainPs.setString(3, email);
            
            int mainResult = insertMainPs.executeUpdate();
            if (mainResult > 0) {
                ResultSet generatedKeys = insertMainPs.getGeneratedKeys();
                if (generatedKeys.next()) {
                    parentId = generatedKeys.getInt(1);
                }
                generatedKeys.close();
            }
            insertMainPs.close();
        }
        checkMainRs.close();
        checkMainPs.close();

        if (parentId > 0) {
            // Tambahkan akun Google - Convert parentId to String for varchar columns
            String insertGoogleQuery = "INSERT INTO " + tableName + " (name, email, " + idColumn + ", photo_url, created_at, last_login, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW(), NOW())";
            PreparedStatement insertGooglePs = conn.prepareStatement(insertGoogleQuery);
            insertGooglePs.setString(1, name);
            insertGooglePs.setString(2, email);
            insertGooglePs.setString(3, String.valueOf(parentId)); // Convert int to String for varchar column
            insertGooglePs.setString(4, photoURL);
            
            int googleResult = insertGooglePs.executeUpdate();
            insertGooglePs.close();

            if (googleResult > 0) {
                session.setAttribute("notification", "Akun Google " + type + " berhasil ditambahkan!");
                session.setAttribute("notificationType", "success");
            } else {
                session.setAttribute("notification", "Gagal menambahkan akun Google!");
                session.setAttribute("notificationType", "error");
            }
        } else {
            session.setAttribute("notification", "Gagal membuat akun " + type + "!");
            session.setAttribute("notificationType", "error");
        }

    } catch (SQLException e) {
        session.setAttribute("notification", "Terjadi kesalahan database: " + e.getMessage());
        session.setAttribute("notificationType", "error");
    } catch (Exception e) {
        session.setAttribute("notification", "Terjadi kesalahan: " + e.getMessage());
        session.setAttribute("notificationType", "error");
    }

    // Redirect kembali ke halaman Google accounts
    response.sendRedirect("../../dashboardadmin.jsp?page=google_accounts&type=" + type);
%>
