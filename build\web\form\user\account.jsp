<%--
  Document    : account
  Created on  : Jun 16, 2025
  Author      : Arqeta
  Description : User account management page
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Mendapatkan informasi user yang sedang login
    String userId = (String) session.getAttribute("userId");
    if (userId == null) {
        response.sendRedirect("../signin.jsp");
        return;
    }

    // Inisialisasi variabel data user
    String userName = "";
    String userUsername = "";
    String userEmail = "";
    boolean isGoogleUser = false;
    String googlePhotoUrl = "";
    String googleName = "";
    String googleEmail = "";

    try {
        // Cek apakah user menggunakan akun Google
        PreparedStatement psGoogle = conn.prepareStatement("SELECT * FROM user_google WHERE user_id = ?");
        psGoogle.setString(1, userId);
        ResultSet rsGoogle = psGoogle.executeQuery();

        if (rsGoogle.next()) {
            isGoogleUser = true;
            googleName = rsGoogle.getString("name");
            googleEmail = rsGoogle.getString("email");
            googlePhotoUrl = rsGoogle.getString("photo_url");
        }
        rsGoogle.close();
        psGoogle.close();

        // Jika bukan Google user, ambil data dari tabel user biasa
        if (!isGoogleUser) {
            PreparedStatement psUser = conn.prepareStatement("SELECT * FROM user WHERE id = ?");
            psUser.setString(1, userId);
            ResultSet rsUser = psUser.executeQuery();

            if (rsUser.next()) {
                userName = rsUser.getString("name");
                userUsername = rsUser.getString("username");
                userEmail = rsUser.getString("email");
            }
            rsUser.close();
            psUser.close();
        }
    } catch (SQLException e) {
        out.println("Error: " + e.getMessage());
    }
%>

<div class="page-content">
    <div class="page-header">
        <h2>Pengaturan Akun</h2>
        <p>Kelola informasi akun Anda</p>
    </div>

    <div class="account-container">
        <% if (isGoogleUser) { %>
        <!-- Google Account Display -->
        <div class="account-card google-account">
            <div class="account-header">
                <div class="account-avatar">
                    <img
                        src="<%= googlePhotoUrl %>"
                        alt="<%= googleName %>"
                        onerror="this.src='../../dist/img/default-avatar.png';"
                    />
                </div>
                <div class="account-info">
                    <h3><%= googleName %></h3>
                    <p class="account-type">Akun Google</p>
                    <p class="account-email"><%= googleEmail %></p>
                </div>
            </div>
            <div class="account-body">
                <div class="info-section">
                    <h4>Informasi Akun Google</h4>
                    <p>
                        Akun Anda terhubung dengan Google. Untuk mengubah informasi akun,
                        silakan kunjungi pengaturan akun Google Anda.
                    </p>
                    <a href="https://myaccount.google.com/" target="_blank" class="btn-primary">
                        <i data-feather="external-link"></i>
                        Kelola Akun Google
                    </a>
                </div>
            </div>
        </div>
        <% } else { %>
        <!-- Regular Account Form -->
        <div class="account-card">
            <div class="account-header">
                <div class="account-avatar">
                    <i data-feather="user"></i>
                </div>
                <div class="account-info">
                    <h3><%= userName %></h3>
                    <p class="account-type">Akun Reguler</p>
                    <p class="account-email"><%= userEmail %></p>
                </div>
            </div>
            <div class="account-body">
                <form id="updateAccountForm" action="process/user/updateAccount.jsp" method="POST">
                    <div class="form-section">
                        <h4><i data-feather="user"></i> Informasi Pribadi</h4>
                        <div class="form-group">
                            <label for="name">Nama Lengkap</label>
                            <input type="text" id="name" name="name" value="<%= userName %>" required />
                        </div>
                        <div class="form-group">
                            <label for="username">Username</label>
                            <input type="text" id="username" name="username" value="<%= userUsername %>" required />
                        </div>
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" value="<%= userEmail %>" required />
                        </div>
                    </div>

                    <div class="form-section">
                        <h4><i data-feather="lock"></i> Ubah Kata Sandi</h4>
                        <div class="form-group">
                            <label for="currentPassword">Kata Sandi Saat Ini</label>
                            <div class="password-input">
                                <input type="password" id="currentPassword" name="currentPassword" />
                                <button type="button" class="toggle-password" onclick="togglePassword('currentPassword')">
                                    <i data-feather="eye-off"></i>
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="newPassword">Kata Sandi Baru</label>
                            <div class="password-input">
                                <input type="password" id="newPassword" name="newPassword" />
                                <button type="button" class="toggle-password" onclick="togglePassword('newPassword')">
                                    <i data-feather="eye-off"></i>
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">Konfirmasi Kata Sandi Baru</label>
                            <div class="password-input">
                                <input type="password" id="confirmPassword" name="confirmPassword" />
                                <button type="button" class="toggle-password" onclick="togglePassword('confirmPassword')">
                                    <i data-feather="eye-off"></i>
                                </button>
                            </div>
                        </div>
                        <p class="form-note">
                            Kosongkan jika tidak ingin mengubah kata sandi
                        </p>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn-primary">
                            <i data-feather="save"></i>
                            Simpan Perubahan
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <% } %>
    </div>
</div>

<script>
    // Toggle password visibility
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const button = field.nextElementSibling;
        const icon = button.querySelector("i");

        if (field.type === "password") {
            field.type = "text";
            icon.setAttribute("data-feather", "eye");
        } else {
            field.type = "password";
            icon.setAttribute("data-feather", "eye-off");
        }
        // Re-render feather icons
        feather.replace();
    }

    // Form validation
    document.addEventListener("DOMContentLoaded", function () {
        const updateForm = document.getElementById("updateAccountForm");
        if (updateForm) {
            updateForm.addEventListener("submit", function (e) {
                const newPassword = document.getElementById("newPassword").value;
                const confirmPassword = document.getElementById("confirmPassword").value;
                const currentPassword = document.getElementById("currentPassword").value;

                // Jika ingin mengubah password
                if (newPassword || confirmPassword) {
                    if (!currentPassword) {
                        e.preventDefault();
                        const message = "Masukkan kata sandi saat ini untuk mengubah kata sandi";
                        if (typeof showNotification === "function") {
                            showNotification(message, "error");
                        } else {
                            alert(message);
                        }
                        return;
                    }

                    if (newPassword !== confirmPassword) {
                        e.preventDefault();
                        const message = "Konfirmasi kata sandi tidak sesuai";
                        if (typeof showNotification === "function") {
                            showNotification(message, "error");
                        } else {
                            alert(message);
                        }
                        return;
                    }

                    if (newPassword.length < 6) {
                        e.preventDefault();
                        const message = "Kata sandi baru minimal 6 karakter";
                        if (typeof showNotification === "function") {
                            showNotification(message, "error");
                        } else {
                            alert(message);
                        }
                        return;
                    }
                }
            });
        }
    });
</script>
