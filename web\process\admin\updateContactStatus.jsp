<%--
    Document   : updateContactStatus
    Created on : June 17, 2025
    Author     : Arqeta
    Description: Proses untuk menandai pesan kontak sebagai sudah dibaca
--%>

<%@page contentType="application/json" pageEncoding="UTF-8"%>
<%@include file="../../config/connection-json.jsp" %>
<%@page import="java.sql.*" %>
<%@page import="jakarta.servlet.http.HttpServletResponse" %>

<%
    // Set response headers untuk JSON
    response.setContentType("application/json");
    response.setCharacterEncoding("UTF-8");
    
    // Debug logging
    System.out.println("updateContactStatus.jsp called with method: " + request.getMethod());
    System.out.println("Request parameters: " + request.getParameterMap().keySet());
    
    // Cek apakah user adalah admin
    String adminId = (String) session.getAttribute("adminId");
    System.out.println("Admin ID from session: " + adminId);
    
    if (adminId == null) {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        out.print("{\"success\": false, \"message\": \"Akses ditolak. Hanya admin yang dapat mengakses fitur ini.\"}");
        return;
    }
    
    String contactId = request.getParameter("id");
    System.out.println("Contact ID parameter: " + contactId);
    
    if (contactId == null || contactId.trim().isEmpty()) {
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        out.print("{\"success\": false, \"message\": \"ID kontak tidak valid.\"}");
        return;
    }
    
    try {
        // Validasi koneksi database
        if (conn == null || conn.isClosed()) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            out.print("{\"success\": false, \"message\": \"Koneksi database tidak tersedia.\"}");
            return;
        }
        
        // Update status kontak menjadi 'read'
        PreparedStatement ps = conn.prepareStatement(
            "UPDATE contact SET status = 'read', updated_at = NOW() WHERE id = ?"
        );
        ps.setInt(1, Integer.parseInt(contactId));
        
        int rowsAffected = ps.executeUpdate();
        ps.close();
        
        System.out.println("Rows affected: " + rowsAffected);
        
        if (rowsAffected > 0) {
            out.print("{\"success\": true, \"message\": \"Pesan berhasil ditandai sebagai sudah dibaca.\"}");
        } else {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            out.print("{\"success\": false, \"message\": \"Pesan kontak tidak ditemukan.\"}");
        }
        
    } catch (NumberFormatException e) {
        System.out.println("NumberFormatException: " + e.getMessage());
        response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        out.print("{\"success\": false, \"message\": \"Format ID kontak tidak valid.\"}");
    } catch (SQLException e) {
        System.out.println("SQLException: " + e.getMessage());
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.print("{\"success\": false, \"message\": \"Terjadi kesalahan database: " + e.getMessage() + "\"}");
    } catch (Exception e) {
        System.out.println("Exception: " + e.getMessage());
        response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        out.print("{\"success\": false, \"message\": \"Terjadi kesalahan sistem: " + e.getMessage() + "\"}");
    }
%>
