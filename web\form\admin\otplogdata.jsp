<%--
    Document   : otplogdata
    Created on : Jun 14, 2025
    Author     : Arqeta
    Description: Admin page untuk mengelola data riwayat OTP
    Features: Menampilkan riwayat OTP, filter berdasarkan status, dan hapus riwayat OTP
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<div class="page-content">
    <div class="page-header">
        <h2>Data Riwayat OTP</h2>
        <div class="action-bar">
            <div class="status-filter">
                <select id="usedFilter" onchange="filterOtpLogs()">
                    <option value="">Semua Status</option>
                    <option value="0">Belum Digunakan</option>
                    <option value="1">Sudah Digunakan</option>
                </select>
            </div>
        </div>
    </div>

    <div class="table-container">
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Email</th>
                        <th>Kode OTP</th>
                        <th>Status</th>
                        <th>Waktu Pembuatan</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody id="otpTableBody">
                    <%
                        try {
                            // Query untuk mengambil data OTP log berurutan berdasarkan ID
                            PreparedStatement ps = conn.prepareStatement("SELECT * FROM otp_log ORDER BY id ASC");
                            ResultSet rs = ps.executeQuery();

                            while (rs.next()) {
                                int id = rs.getInt("id");
                                String email = rs.getString("email");
                                String otp = rs.getString("otp");
                                boolean used = rs.getBoolean("used");
                                String createdAt = rs.getString("created_at");

                                String statusClass = used ? "status-approved" : "status-pending";
                                String statusText = used ? "Sudah Digunakan" : "Belum Digunakan";
                    %>
                    <tr data-used="<%= used ? "1" : "0" %>">
                        <td><%= id %></td>
                        <td><%= email %></td>
                        <td>
                            <span class="otp-code" data-otp="<%= otp %>">
                                ******
                            </span>
                            <button class="btn-show-otp" onclick="toggleOtp(this, '<%= otp %>')" title="Tampilkan OTP">
                                <i data-feather="eye"></i>
                            </button>
                        </td>
                        <td>
                            <span class="status-badge <%= statusClass %>">
                                <%= statusText %>
                            </span>
                        </td>
                        <td><%= createdAt %></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-delete" onclick="openDeleteOtpModal(<%= id %>, '<%= email %>')" title="Hapus">
                                    <i data-feather="trash-2"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <%
                            }
                            rs.close();
                            ps.close();
                        } catch (SQLException e) {
                            out.println("<tr><td colspan='6'>Error: " + e.getMessage() + "</td></tr>");
                        }
                    %>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Konfirmasi Hapus -->
<div id="deleteOtpModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Konfirmasi Hapus</h3>
            <span class="close-modal" onclick="closeModal('deleteOtpModal')">&times;</span>
        </div>
        <div class="modal-body">
            <p>Apakah Anda yakin ingin menghapus riwayat OTP untuk email "<span id="deleteOtpEmail"></span>"?</p>
            <p class="warning-text">Tindakan ini tidak dapat dibatalkan.</p>
        </div>
        <div class="modal-actions">
            <button type="button" class="btn-secondary" onclick="closeModal('deleteOtpModal')">Batal</button>
            <button type="button" class="btn-danger" onclick="confirmDeleteOtp()">Hapus</button>
        </div>
    </div>
</div>

<script>
    let deleteOtpId = null;

    function toggleOtp(button, otp) {
        const otpSpan = button.previousElementSibling;
        const icon = button.querySelector('i');

        if (otpSpan.textContent.trim() === '******') {
            // Tampilkan OTP
            otpSpan.textContent = otp;
            icon.setAttribute('data-feather', 'eye-off');
            button.title = 'Sembunyikan OTP';
        } else {
            // Sembunyikan OTP
            otpSpan.textContent = '******';
            icon.setAttribute('data-feather', 'eye');
            button.title = 'Tampilkan OTP';
        }

        // Refresh feather icons
        feather.replace();
    }

    function openDeleteOtpModal(id, email) {
        console.log('Opening delete modal for OTP ID:', id, 'Email:', email);
        deleteOtpId = id;
        document.getElementById('deleteOtpEmail').textContent = email;
        document.getElementById('deleteOtpModal').style.display = 'block';
        console.log('Modal should be visible now');
    }

    function confirmDeleteOtp() {
        console.log('Confirm delete called with ID:', deleteOtpId);
        if (deleteOtpId) {
            console.log('Creating form for deletion...');
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'process/admin/deleteOtpProcess.jsp';

            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'id';
            idInput.value = deleteOtpId;

            form.appendChild(idInput);
            document.body.appendChild(form);

            console.log('Form created, submitting to:', form.action);
            console.log('Form data - ID:', idInput.value);

            // Close modal before submitting
            closeModal('deleteOtpModal');

            form.submit();
        } else {
            console.error('No OTP ID set for deletion');
        }
    }

    function filterOtpLogs() {
        const filter = document.getElementById('usedFilter').value;
        const rows = document.querySelectorAll('#otpTableBody tr');

        rows.forEach(row => {
            const used = row.getAttribute('data-used');
            if (filter === '' || used === filter) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    function closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }

    // Tutup modal ketika klik di luar modal
    window.onclick = function(event) {
        const modal = document.getElementById('deleteOtpModal');
        if (event.target === modal) {
            closeModal('deleteOtpModal');
        }
    }
</script>

<style>
.otp-code {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    background-color: var(--bg-color);
    padding: 4px 8px;
    border-radius: 4px;
    margin-right: 8px;
}

.btn-show-otp {
    background: none;
    border: none;
    color: var(--accent-color);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.btn-show-otp:hover {
    background-color: rgba(63, 81, 181, 0.1);
}

/* Ensure modal is properly styled and visible */
#deleteOtpModal {
    z-index: 9999 !important;
}

#deleteOtpModal .modal-content {
    position: relative;
    z-index: 10000;
}

/* Ensure buttons are clickable */
.btn-delete {
    cursor: pointer;
    border: none;
    background: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
    padding: 6px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.btn-delete:hover {
    background-color: rgba(244, 67, 54, 0.2);
}

.warning-text {
    color: var(--warning-color);
    font-size: 0.9rem;
    margin-top: 10px;
}
</style>


