<%--
  Document    : manageTransaction
  Created on  : Jun 17, 2025
  Author      : Arqeta
  Description : Processor untuk mengelola transaksi (menerima/menolak pesanan).
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Cek apakah admin sudah login
    String adminId = (String) session.getAttribute("adminId");
    if (adminId == null) {
        session.setAttribute("message", "Akses ditolak! Silakan login sebagai admin.");
        session.setAttribute("messageType", "error");
        response.sendRedirect(request.getContextPath() + "/form/signin.jsp");
        return;
    }

    // Ambil parameter dari form
    String transactionIdStr = request.getParameter("id");
    String action = request.getParameter("action");

    // Validasi parameter
    if (transactionIdStr == null || transactionIdStr.trim().isEmpty() || action == null || action.trim().isEmpty()) {
        session.setAttribute("message", "Parameter tidak valid!");
        session.setAttribute("messageType", "error");
        response.sendRedirect(request.getContextPath() + "/dashboardadmin.jsp?page=transaction");
        return;
    }

    try {
        int transactionId = Integer.parseInt(transactionIdStr);

        // Ambil data transaksi untuk validasi
        String checkSql = "SELECT t.*, s.quantity as service_quantity "
                        + "FROM transaction t "
                        + "LEFT JOIN services s ON t.service_id = s.id "
                        + "WHERE t.id = ?";
        PreparedStatement psCheck = conn.prepareStatement(checkSql);
        psCheck.setInt(1, transactionId);
        ResultSet rsCheck = psCheck.executeQuery();

        if (!rsCheck.next()) {
            session.setAttribute("message", "Transaksi tidak ditemukan!");
            session.setAttribute("messageType", "error");
            response.sendRedirect(request.getContextPath() + "/dashboardadmin.jsp?page=transaction");
            return;
        }

        // Ambil detail dari transaksi yang ada
        String currentStatus = rsCheck.getString("status");
        int serviceId = rsCheck.getInt("service_id");
        int transactionUnit = rsCheck.getInt("unit");
        rsCheck.close();
        psCheck.close();

        // Validasi status transaksi - hanya bisa diproses jika masih 'pending' atau 'menunggu'
        if (!"pending".equals(currentStatus) && !"menunggu".equals(currentStatus)) {
            session.setAttribute("message", "Transaksi ini sudah diproses sebelumnya!");
            session.setAttribute("messageType", "error");
            response.sendRedirect(request.getContextPath() + "/dashboardadmin.jsp?page=transaction");
            return;
        }

        String newStatus = "";
        String successMessage = "";

        // Tentukan status baru dan logika berdasarkan 'action'
        switch (action) {
            case "accept":
                newStatus = "completed";
                successMessage = "Transaksi berhasil diterima!";
                break;

            case "reject":
                newStatus = "cancelled";
                successMessage = "Transaksi berhasil ditolak!";
                
                // Jika transaksi ditolak, kembalikan quantity layanan
                PreparedStatement psRestore = conn.prepareStatement("UPDATE services SET quantity = quantity + ? WHERE id = ?");
                psRestore.setInt(1, transactionUnit);
                psRestore.setInt(2, serviceId);
                psRestore.executeUpdate();
                psRestore.close();
                break;

            default:
                session.setAttribute("message", "Aksi tidak valid!");
                session.setAttribute("messageType", "error");
                response.sendRedirect(request.getContextPath() + "/dashboardadmin.jsp?page=transaction");
                return;
        }

        // Update status transaksi di database
        PreparedStatement psUpdate = conn.prepareStatement("UPDATE transaction SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        psUpdate.setString(1, newStatus);
        psUpdate.setInt(2, transactionId);

        int result = psUpdate.executeUpdate();
        psUpdate.close();

        if (result > 0) {
            session.setAttribute("message", successMessage);
            session.setAttribute("messageType", "success");
        } else {
            session.setAttribute("message", "Gagal memproses transaksi!");
            session.setAttribute("messageType", "error");
        }

    } catch (NumberFormatException e) {
        session.setAttribute("message", "ID transaksi tidak valid!");
        session.setAttribute("messageType", "error");
    } catch (SQLException e) {
        session.setAttribute("message", "Error database: " + e.getMessage());
        session.setAttribute("messageType", "error");
    } catch (Exception e) {
        session.setAttribute("message", "Terjadi kesalahan: " + e.getMessage());
        session.setAttribute("messageType", "error");
    }

    // Redirect kembali ke dashboard admin
    response.sendRedirect(request.getContextPath() + "/dashboardadmin.jsp?page=transaction");
%>
