<%--
  Document   : addService
  Created on : Jun 15, 2025
  Author     : Arqeta
  Description: Process untuk menambah layanan baru
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.io.*"%>
<%@page import="java.nio.file.*"%>
<%@page import="jakarta.servlet.http.Part"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Pastikan request adalah POST, jika bukan, redirect kembali.
    if (!"POST".equalsIgnoreCase(request.getMethod())) {
        response.sendRedirect("../../dashboardadmin.jsp?page=services");
        return;
    }

    try {
        // 1. Ambil parameter dari form
        String name = request.getParameter("name");
        String quantityStr = request.getParameter("quantity");
        String priceStr = request.getParameter("price");

        // 2. Validasi input tidak boleh kosong
        if (name == null || name.trim().isEmpty() || quantityStr == null || quantityStr.trim().isEmpty() || priceStr == null || priceStr.trim().isEmpty()) {
            session.setAttribute("notification", "Semua field harus diisi!");
            session.setAttribute("notificationType", "error");
            response.sendRedirect("../../dashboardadmin.jsp?page=services");
            return;
        }

        // Konversi tipe data dengan error handling
        int quantity = Integer.parseInt(quantityStr);
        double price = Double.parseDouble(priceStr);

        // 3. Handle proses upload file gambar
        String fileName = null;
        Part filePart = request.getPart("image");

        if (filePart != null && filePart.getSize() > 0) {
            // Validasi tipe file (harus gambar)
            String contentType = filePart.getContentType();
            if (!contentType.startsWith("image/")) {
                session.setAttribute("notification", "File harus berupa gambar!");
                session.setAttribute("notificationType", "error");
                response.sendRedirect("../../dashboardadmin.jsp?page=services");
                return;
            }

            // Validasi ukuran file (maksimal 10MB)
            if (filePart.getSize() > 10 * 1024 * 1024) {
                session.setAttribute("notification", "Ukuran file maksimal 10MB!");
                session.setAttribute("notificationType", "error");
                response.sendRedirect("../../dashboardadmin.jsp?page=services");
                return;
            }

            // Generate nama file unik untuk mencegah duplikasi
            String originalFileName = filePart.getSubmittedFileName();
            String fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
            fileName = "service_" + System.currentTimeMillis() + fileExtension;

            // Path untuk menyimpan file - simpan ke direktori build dan source
            String buildUploadPath = application.getRealPath("/dist/img/");
            String sourceUploadPath = application.getRealPath("/").replace("build" + File.separator + "web", "web") + "dist" + File.separator + "img";

            // Buat direktori jika belum ada
            File buildUploadDir = new File(buildUploadPath);
            File sourceUploadDir = new File(sourceUploadPath);
            if (!buildUploadDir.exists()) buildUploadDir.mkdirs();
            if (!sourceUploadDir.exists()) sourceUploadDir.mkdirs();

            // Simpan file ke kedua lokasi
            String buildFilePath = buildUploadPath + File.separator + fileName;
            String sourceFilePath = sourceUploadPath + File.separator + fileName;

            try (InputStream input = filePart.getInputStream()) {
                Files.copy(input, Paths.get(buildFilePath), StandardCopyOption.REPLACE_EXISTING);
            }
            try (InputStream input = filePart.getInputStream()) {
                Files.copy(input, Paths.get(sourceFilePath), StandardCopyOption.REPLACE_EXISTING);
            }

        } else {
            // Jika tidak ada file yang diupload
            session.setAttribute("notification", "Gambar layanan harus diupload!");
            session.setAttribute("notificationType", "error");
            response.sendRedirect("../../dashboardadmin.jsp?page=services");
            return;
        }

        // 4. Simpan data ke database
        PreparedStatement ps = conn.prepareStatement("INSERT INTO services (name, images, quantity, price) VALUES (?, ?, ?, ?)");
        ps.setString(1, name.trim());
        ps.setString(2, fileName);
        ps.setInt(3, quantity);
        ps.setDouble(4, price);
        
        int result = ps.executeUpdate();
        ps.close();

        if (result > 0) {
            session.setAttribute("notification", "Layanan berhasil ditambahkan!");
            session.setAttribute("notificationType", "success");
        } else {
            session.setAttribute("notification", "Gagal menambahkan layanan!");
            session.setAttribute("notificationType", "error");
        }

    } catch (NumberFormatException e) {
        session.setAttribute("notification", "Format angka untuk jumlah atau harga tidak valid!");
        session.setAttribute("notificationType", "error");
        e.printStackTrace();
    } catch (Exception e) {
        session.setAttribute("notification", "Terjadi kesalahan sistem: " + e.getMessage());
        session.setAttribute("notificationType", "error");
        e.printStackTrace(); // Penting untuk debugging di console server
    }

    // 5. Redirect kembali ke halaman layanan setelah semua proses selesai
    response.sendRedirect("../../dashboardadmin.jsp?page=services");
%>
