<%--
    Document   : blogdata
    Created on : May 30, 2025, 3:41:13 PM
    Author     : Arqeta
    Description: Halaman data blog di dashboard admin
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<div class="page-content">
    <div class="page-header">
        <h2>Data Blog</h2>
        <div class="action-bar">
            <button class="btn-primary" onclick="openAddBlogModal()">
                <i data-feather="plus"></i> Tambah Blog
            </button>
        </div>
    </div>

    <div class="table-container">
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Judul</th>
                        <th>Gambar</th>
                        <th>Konten</th>
                        <th>Waktu Pembuatan</th>
                        <th><PERSON><PERSON><PERSON></th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <%
                        try {
                            // Query untuk mengambil data blog berurutan berdasarkan ID
                            PreparedStatement ps = conn.prepareStatement("SELECT * FROM blog ORDER BY id ASC");
                            ResultSet rs = ps.executeQuery();

                            while (rs.next()) {
                                int id = rs.getInt("id");
                                String title = rs.getString("title");
                                String image = rs.getString("image");
                                String content = rs.getString("content");
                                String createdAt = rs.getString("created_at");
                                String updatedAt = rs.getString("updated_at");
                    %>
                    <tr>
                        <td><%= id %></td>
                        <td><%= title %></td>
                        <td>
                            <% if (image != null && !image.isEmpty()) { %>
                                <img src="dist/img/<%= image %>" alt="<%= title %>" class="table-image"
                                     onerror="this.src='dist/img/default-blog.png';">
                            <% } else { %>
                                <span class="no-image">Tidak ada gambar</span>
                            <% } %>
                        </td>
                        <td class="description-cell">
                            <%= content.length() > 100 ? content.substring(0, 100) + "..." : content %>
                        </td>
                        <td><%= createdAt %></td>
                        <td><%= updatedAt %></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-view" onclick="viewBlogDetails(<%= id %>)" title="Lihat Detail">
                                    <i data-feather="eye"></i>
                                </button>
                                <button class="btn-edit" onclick="openEditBlogModal(<%= id %>, '<%= title.replace("'", "\\'") %>', '<%= image %>', '<%= content.replace("'", "\\'").replace("\n", "\\n") %>')" title="Edit">
                                    <i data-feather="edit"></i>
                                </button>
                                <button class="btn-delete" onclick="openDeleteBlogModal(<%= id %>, '<%= title.replace("'", "\\'") %>')" title="Hapus">
                                    <i data-feather="trash-2"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <%
                            }
                            rs.close();
                            ps.close();
                        } catch (SQLException e) {
                            out.println("<tr><td colspan='7'>Error: " + e.getMessage() + "</td></tr>");
                        }
                    %>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Tambah Blog -->
<div id="addBlogModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Tambah Blog</h3>
            <span class="close-modal" onclick="closeModal('addBlogModal')">&times;</span>
        </div>
        <form id="addBlogForm" method="POST" action="process/admin/addBlog.jsp" enctype="multipart/form-data">
            <div class="form-group">
                <label for="addBlogTitle">Judul Blog</label>
                <input type="text" id="addBlogTitle" name="title" required>
            </div>
            <div class="form-group">
                <label for="addBlogImage">Gambar Blog</label>
                <input type="file" id="addBlogImage" name="image" accept="image/*" required>
                <small class="form-text">Format yang didukung: JPG, PNG, GIF (Maksimal 10MB)</small>
            </div>
            <div class="form-group">
                <label for="addBlogContent">Konten</label>
                <textarea id="addBlogContent" name="content" rows="6" required style="resize: vertical;"></textarea>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn-secondary" onclick="closeModal('addBlogModal')">Batal</button>
                <button type="submit" class="btn-primary">Tambah</button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Edit Blog -->
<div id="editBlogModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Edit Blog</h3>
            <span class="close-modal" onclick="closeModal('editBlogModal')">&times;</span>
        </div>
        <form id="editBlogForm" method="POST" action="process/admin/editBlog.jsp" enctype="multipart/form-data">
            <input type="hidden" id="editBlogId" name="id">
            <div class="form-group">
                <label for="editBlogTitle">Judul Blog</label>
                <input type="text" id="editBlogTitle" name="title" required>
            </div>
            <div class="form-group">
                <label for="editBlogImage">Gambar Blog (kosongkan jika tidak ingin mengubah)</label>
                <input type="file" id="editBlogImage" name="image" accept="image/*">
                <small class="form-text">Format yang didukung: JPG, PNG, GIF (Maksimal 10MB)</small>
            </div>
            <div class="form-group">
                <label for="editBlogContent">Konten</label>
                <textarea id="editBlogContent" name="content" rows="6" required style="resize: vertical;"></textarea>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn-secondary" onclick="closeModal('editBlogModal')">Batal</button>
                <button type="submit" class="btn-primary">Update</button>
            </div>
        </form>
    </div>
</div>

<!-- Modal Konfirmasi Hapus -->
<div id="deleteBlogModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Konfirmasi Hapus</h3>
            <span class="close-modal" onclick="closeModal('deleteBlogModal')">&times;</span>
        </div>
        <div class="modal-body">
            <p>Apakah Anda yakin ingin menghapus blog "<span id="deleteBlogTitle"></span>"?</p>
            <p class="warning-text">Tindakan ini tidak dapat dibatalkan.</p>
        </div>
        <div class="modal-actions">
            <button type="button" class="btn-secondary" onclick="closeModal('deleteBlogModal')">Batal</button>
            <button type="button" class="btn-danger" onclick="confirmDeleteBlog()">Hapus</button>
        </div>
    </div>
</div>

<!-- Modal Detail Blog -->
<div id="blogDetailModal" class="modal">
    <div class="modal-content modal-large">
        <div class="modal-header">
            <h3>Detail Blog</h3>
            <span class="close-modal" onclick="closeModal('blogDetailModal')">&times;</span>
        </div>
        <div class="modal-body">
            <div id="blogDetailContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
        <div class="modal-actions">
            <button type="button" class="btn-secondary" onclick="closeModal('blogDetailModal')">Tutup</button>
        </div>
    </div>
</div>

<script>
    let deleteBlogId = null;

    function openAddBlogModal() {
        document.getElementById('addBlogModal').style.display = 'block';
    }

    function openEditBlogModal(id, title, image, content) {
        document.getElementById('editBlogId').value = id;
        document.getElementById('editBlogTitle').value = title;
        document.getElementById('editBlogContent').value = content;
        document.getElementById('editBlogModal').style.display = 'block';
    }

    function openDeleteBlogModal(id, title) {
        deleteBlogId = id;
        document.getElementById('deleteBlogTitle').textContent = title;
        document.getElementById('deleteBlogModal').style.display = 'block';
    }

    function confirmDeleteBlog() {
        if (deleteBlogId) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'process/admin/deleteBlog.jsp';

            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'id';
            idInput.value = deleteBlogId;

            form.appendChild(idInput);
            document.body.appendChild(form);
            form.submit();
        }
    }

    function viewBlogDetails(id) {
        // Load blog details via AJAX
        fetch('process/admin/getBlogDetails.jsp?id=' + id)
            .then(response => response.text())
            .then(data => {
                document.getElementById('blogDetailContent').innerHTML = data;
                document.getElementById('blogDetailModal').style.display = 'block';
            })
            .catch(error => {
                console.error('Error loading blog details:', error);
                showNotification('Error loading blog details', 'error');
            });
    }

    function closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
        // Reset form jika ada
        const form = document.querySelector('#' + modalId + ' form');
        if (form) {
            form.reset();
        }
    }

    // Tutup modal ketika klik di luar modal
    window.onclick = function(event) {
        const modals = ['addBlogModal', 'editBlogModal', 'deleteBlogModal', 'blogDetailModal'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (event.target === modal) {
                closeModal(modalId);
            }
        });
    }
</script>

