<%--
    Document   : getItemDetails
    Created on : Jun 2, 2025, 10:15:30 AM
    Author     : Arqeta
    Description: Get and display item details for modal
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../config/connection.jsp" %>

<%
    // Get the parameters
    String type = request.getParameter("type");
    String id = request.getParameter("id");

    if (id != null && !id.trim().isEmpty() && type != null && !type.trim().isEmpty()) {
        try {
            PreparedStatement ps = null;
            ResultSet rs = null;

            // Get data based on type
            switch (type) {
                case "service":
                    ps = conn.prepareStatement("SELECT * FROM services WHERE id = ?");
                    ps.setString(1, id);
                    rs = ps.executeQuery();
                    if (rs.next()) {
                        String name = rs.getString("name");
                        String image = rs.getString("images");
                        int quantity = rs.getInt("quantity");
                        int price = rs.getInt("price");
                        String createdAt = rs.getString("created_at");
%>
<div class="item-modal-header">
    <h3><%= name %></h3>
    <p class="item-date">Dibuat: <%= createdAt %></p>
</div>
<div class="item-modal-img">
    <img src="../dist/img/<%= image %>" alt="<%= name %>" />
</div>
<div class="item-modal-details">
    <div class="item-detail">
        <span class="detail-label">Kuota:</span>
        <span class="detail-value"><%= quantity %></span>
    </div>
    <div class="item-detail">
        <span class="detail-label">Harga:</span>
        <span class="detail-value">Rp <%= price %></span>
    </div>
</div>
<%
                    }
                    break;
                case "blog":
                    ps = conn.prepareStatement("SELECT * FROM blog WHERE id = ?");
                    ps.setString(1, id);
                    rs = ps.executeQuery();
                    if (rs.next()) {
                        String title = rs.getString("title");
                        String image = rs.getString("image");
                        String content = rs.getString("content");
                        String createdAt = rs.getString("created_at");
%>
<div class="item-modal-header">
    <h3><%= title %></h3>
    <p class="item-date">Dipublikasikan: <%= createdAt %></p>
</div>
<div class="item-modal-img">
    <img src="../dist/img/<%= image %>" alt="<%= title %>" />
</div>
<div class="item-modal-content">
    <p><%= content %></p>
</div>
<%
                    }
                    break;
                case "contact":
                    ps = conn.prepareStatement("SELECT * FROM contact WHERE id = ?");
                    ps.setString(1, id);
                    rs = ps.executeQuery();
                    if (rs.next()) {
                        String name = rs.getString("name");
                        String email = rs.getString("email");
                        String subject = rs.getString("subject");
                        String message = rs.getString("message");
                        String status = rs.getString("status");
                        String createdAt = rs.getString("created_at");
%>
<div class="item-modal-header">
    <h3><%= subject %></h3>
    <p class="item-date">Diterima: <%= createdAt %></p>
</div>
<div class="item-modal-details">
    <div class="item-detail">
        <span class="detail-label">Nama:</span>
        <span class="detail-value"><%= name %></span>
    </div>
    <div class="item-detail">
        <span class="detail-label">Email:</span>
        <span class="detail-value"><%= email %></span>
    </div>
    <div class="item-detail">
        <span class="detail-label">Status:</span>
        <span class="detail-value status-badge status-<%= status.toLowerCase() %>"
            ><%= status %></span
        >
    </div>
</div>
<div class="item-modal-message">
    <h4>Pesan:</h4>
    <p><%= message %></p>
</div>
<%
                    }
                    break;
                default:
                    out.println("<p>Tipe data tidak valid</p>");
                    break;
            }

            if (rs != null) rs.close();
            if (ps != null) ps.close();

        } catch (Exception e) {
            out.println("<p>Terjadi kesalahan: " + e.getMessage() + "</p>");
        }
    } else {
        out.println("<p>Parameter tidak lengkap</p>");
    }
%>
