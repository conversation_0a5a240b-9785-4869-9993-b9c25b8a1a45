<%--
    Document   : contactdata
    Created on : May 30, 2025, 3:49:02 PM
    Author     : Arqeta
    Description: Halaman data kontak di dashboard admin
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<div class="page-content">
    <div class="page-header">
        <h2>Data Kontak</h2>
        <div class="action-bar">
            <div class="status-filter">
                <select id="statusFilter" onchange="filterContacts()">
                    <option value="">Semua Status</option>
                    <option value="unread">Belum Dibaca</option>
                    <option value="read">Sudah Dibaca</option>
                    <option value="replied">Sudah Dibalas</option>
                </select>
            </div>
        </div>
    </div>

    <div class="table-container">
        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nama</th>
                        <th>Email</th>
                        <th>Subjek</th>
                        <th>Status</th>
                        <th>Tanggal</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody id="contactTableBody">
                    <%
                        try {
                            // Query untuk mengambil data kontak berurutan berdasarkan ID
                            PreparedStatement ps = conn.prepareStatement("SELECT * FROM contact ORDER BY id ASC");
                            ResultSet rs = ps.executeQuery();

                            while (rs.next()) {
                                int id = rs.getInt("id");
                                String name = rs.getString("name");
                                String email = rs.getString("email");
                                String subject = rs.getString("subject");
                                String message = rs.getString("message");
                                String status = rs.getString("status");
                                String createdAt = rs.getString("created_at");

                                String statusClass = "";
                                String statusText = "";
                                switch(status) {
                                    case "unread":
                                        statusClass = "status-unread";
                                        statusText = "Belum Dibaca";
                                        break;
                                    case "read":
                                        statusClass = "status-read";
                                        statusText = "Sudah Dibaca";
                                        break;
                                    case "replied":
                                        statusClass = "status-approved";
                                        statusText = "Sudah Dibalas";
                                        break;
                                    default:
                                        statusClass = "status-unread";
                                        statusText = status;
                                }
                    %>
                    <tr data-status="<%= status %>" data-contact-id="<%= id %>" class="<%= status.equals("unread") ? "unread-row" : "" %>">
                        <td><%= id %></td>
                        <td><%= name %></td>
                        <td><%= email %></td>
                        <td><%= subject %></td>
                        <td>
                            <span class="status-badge <%= statusClass %>">
                                <%= statusText %>
                            </span>
                        </td>
                        <td><%= createdAt %></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-view" onclick="viewContactMessage(<%= id %>)" title="Baca Pesan">
                                    <i data-feather="eye"></i>
                                </button>
                                <% if ("unread".equals(status)) { %>
                                    <button class="btn-edit" onclick="markAsRead(<%= id %>)" title="Tandai Dibaca">
                                        <i data-feather="check"></i>
                                    </button>
                                <% } %>
                                <button class="btn-delete" onclick="openDeleteContactModal(<%= id %>, '<%= subject.replace("'", "\\'") %>')" title="Hapus">
                                    <i data-feather="trash-2"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <%
                            }
                            rs.close();
                            ps.close();
                        } catch (SQLException e) {
                            out.println("<tr><td colspan='7'>Error: " + e.getMessage() + "</td></tr>");
                        }
                    %>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Detail Pesan -->
<div id="contactMessageModal" class="modal">
    <div class="modal-content modal-large">
        <div class="modal-header">
            <h3>Detail Pesan Kontak</h3>
            <span class="close-modal" onclick="closeModal('contactMessageModal')">&times;</span>
        </div>
        <div class="modal-body">
            <div id="contactMessageContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Modal Konfirmasi Hapus -->
<div id="deleteContactModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Konfirmasi Hapus</h3>
            <span class="close-modal" onclick="closeModal('deleteContactModal')">&times;</span>
        </div>
        <div class="modal-body">
            <p>Apakah Anda yakin ingin menghapus pesan "<span id="deleteContactSubject"></span>"?</p>
            <p class="warning-text">Tindakan ini tidak dapat dibatalkan.</p>
        </div>
        <div class="modal-actions">
            <button type="button" class="btn-secondary" onclick="closeModal('deleteContactModal')">Batal</button>
            <button type="button" class="btn-danger" onclick="confirmDeleteContact()">Hapus</button>
        </div>
    </div>
</div>

<script>
    let deleteContactId = null;

    function viewContactMessage(id) {
        console.log('Viewing contact message with ID:', id);

        // Show loading state
        const modal = document.getElementById('contactMessageModal');
        const content = document.getElementById('contactMessageContent');
        content.innerHTML = '<div class="loading-message">Memuat detail pesan...</div>';
        modal.style.display = 'block';

        // Load contact message details via AJAX
        fetch('process/admin/contactDetailModal.jsp?id=' + id)
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.text();
            })
            .then(data => {
                console.log('Received data:', data);
                content.innerHTML = data;

                // Initialize Feather icons for the new content
                if (typeof feather !== 'undefined') {
                    feather.replace();
                }

                // Automatically mark as read when viewed (but don't reload page)
                markAsReadSilent(id);
            })
            .catch(error => {
                console.error('Error loading contact details:', error);
                content.innerHTML = '<div class="error-message">Gagal memuat detail pesan. Silakan coba lagi.</div>';
                if (typeof showNotification !== 'undefined') {
                    showNotification('Gagal memuat detail pesan', 'error');
                }
            });
    }

    function markAsRead(id) {
        console.log('Marking contact as read:', id);

        fetch('process/admin/updateContactStatus.jsp?id=' + id, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        })
        .then(response => {
            console.log('Mark as read response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Mark as read response:', data);
            if (data.success) {
                // Refresh the page to show updated status
                location.reload();
            } else {
                console.error('Error:', data.message);
                if (typeof showNotification !== 'undefined') {
                    showNotification(data.message || 'Gagal menandai pesan sebagai dibaca', 'error');
                }
            }
        })
        .catch(error => {
            console.error('Error marking as read:', error);
            if (typeof showNotification !== 'undefined') {
                showNotification('Gagal menandai pesan sebagai dibaca', 'error');
            }
        });
    }

    function markAsReadSilent(id) {
        console.log('Silently marking contact as read:', id);

        fetch('process/admin/updateContactStatus.jsp?id=' + id, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        })
        .then(response => {
            console.log('Silent mark as read response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Silent mark as read response:', data);
            if (data.success) {
                // Update the status badge in the table without reloading
                updateContactStatusInTable(id, 'read');
            } else {
                console.error('Silent mark as read error:', data.message);
            }
        })
        .catch(error => {
            console.error('Error silently marking as read:', error);
        });
    }

    function markAsReadFromModal(id) {
        console.log('Marking contact as read from modal:', id);

        fetch('process/admin/updateContactStatus.jsp?id=' + id, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        })
        .then(response => {
            console.log('Mark as read from modal response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Mark as read from modal response:', data);
            if (data.success) {
                // Close modal and refresh page to show updated status
                closeModal('contactMessageModal');
                location.reload();
            } else {
                console.error('Error:', data.message);
                if (typeof showNotification !== 'undefined') {
                    showNotification(data.message || 'Gagal menandai pesan sebagai dibaca', 'error');
                }
            }
        })
        .catch(error => {
            console.error('Error marking contact as read from modal:', error);
            if (typeof showNotification !== 'undefined') {
                showNotification('Gagal menandai pesan sebagai dibaca', 'error');
            }
        });
    }

    function updateContactStatusInTable(id, newStatus) {
        const row = document.querySelector(`tr[data-contact-id="${id}"]`);
        if (row) {
            const statusBadge = row.querySelector('.status-badge');
            if (statusBadge && newStatus === 'read') {
                statusBadge.className = 'status-badge status-read';
                statusBadge.textContent = 'Sudah Dibaca';

                // Remove the "Mark as Read" button
                const markReadBtn = row.querySelector('.btn-edit');
                if (markReadBtn) {
                    markReadBtn.remove();
                }

                // Remove unread row highlighting
                row.classList.remove('unread-row');
                row.setAttribute('data-status', 'read');
            }
        }
    }

    function openDeleteContactModal(id, subject) {
        deleteContactId = id;
        document.getElementById('deleteContactSubject').textContent = subject;
        document.getElementById('deleteContactModal').style.display = 'block';
    }

    function confirmDeleteContact() {
        if (deleteContactId) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = 'process/admin/deleteContactProcess.jsp';

            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'id';
            idInput.value = deleteContactId;

            form.appendChild(idInput);
            document.body.appendChild(form);
            form.submit();
        }
    }

    function filterContacts() {
        const filter = document.getElementById('statusFilter').value;
        const rows = document.querySelectorAll('#contactTableBody tr');

        rows.forEach(row => {
            const status = row.getAttribute('data-status');
            if (filter === '' || status === filter) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    function closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }

    // Tutup modal ketika klik di luar modal
    window.onclick = function(event) {
        const modals = ['contactMessageModal', 'deleteContactModal'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (event.target === modal) {
                closeModal(modalId);
            }
        });
    }

    // Initialize Feather icons when page loads
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof feather !== 'undefined') {
            feather.replace();
        }
    });
</script>


