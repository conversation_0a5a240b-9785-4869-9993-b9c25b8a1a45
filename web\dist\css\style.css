/* 
 * style.css
 * Main stylesheet for Arqeta website
 * Created on: June 1, 2025
 * Author: Arqeta
 * Description: Contains styles for the main website including responsive design, animations, and theme switching
 */

:root {
  /* Color variables */
  --primary-color: #181818;
  --secondary-color: #ffffff;
  --background-color: #ffffff;
  --text-color: #181818;
  --card-bg-color: #ffffff;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --overlay-color: rgba(0, 0, 0, 0.7);
  --card-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  --nav-bg-color: #ffffff;
  --footer-bg-color: #f5f5f5;
  --border-color: #e5e5e5;
  --input-bg-color: #f5f5f5;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;

  /* Transition variables */
  --transition-speed: 0.3s;
  --border-radius: 8px;

  /* Font variables */
  --font-family: "Quicksand", sans-serif;
}

/* Dark mode variables */
.dark-mode {
  --primary-color: #ffffff;
  --secondary-color: #181818;
  --background-color: #181818;
  --text-color: #ffffff;
  --card-bg-color: #2d2d2d;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --overlay-color: rgba(0, 0, 0, 0.8);
  --card-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  --nav-bg-color: #1e1e1e;
  --footer-bg-color: #1e1e1e;
  --border-color: #3d3d3d;
  --input-bg-color: #2d2d2d;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  color: var(--text-color);
  background-color: var(--background-color);
  line-height: 1.6;
  transition: background-color var(--transition-speed),
    color var(--transition-speed);
}

a {
  color: inherit;
  text-decoration: none;
}

img,
svg {
  max-width: 100%;
  height: auto;
}

button,
input,
textarea {
  font-family: var(--font-family);
}

textarea {
  resize: vertical;
}

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navbar */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  background-color: var(--nav-bg-color);
  box-shadow: 0 2px 10px var(--shadow-color);
  transition: background-color var(--transition-speed),
    box-shadow var(--transition-speed), transform var(--transition-speed);
}

.navbar.transparent {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.dark-mode .navbar.transparent {
  background-color: rgba(24, 24, 24, 0.8);
}

.navbar .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.navbar-logo .logo {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-color);
}

.navbar-menu {
  display: flex;
}

.navbar-menu ul {
  display: flex;
  list-style: none;
}

.navbar-menu li {
  position: relative;
  margin: 0 15px;
}

.navbar-menu li a {
  display: flex;
  align-items: center;
  font-weight: 500;
  padding: 5px 0;
  transition: color var(--transition-speed);
}

.navbar-menu li.dropdown i {
  margin-left: 5px;
  transition: transform var(--transition-speed);
}

.navbar-menu li a:hover {
  color: var(--primary-color);
}

.navbar-menu li a:after {
  content: "";
  position: absolute;
  width: 50%;
  transform: scaleX(0);
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transform-origin: bottom right;
  transition: transform 0.3s ease-out;
}

.navbar-menu li a:hover:after,
.navbar-menu li.active a:after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

/* Dropdown content - Fixed spacing and precision */
.dropdown-content {
  display: none;
  position: absolute;
  background-color: var(--card-bg-color);
  min-width: 300px;
  max-width: 350px;
  box-shadow: 0 8px 25px var(--shadow-color);
  z-index: 1001;
  border-radius: 12px;
  top: calc(100% + 5px);
  left: -20px;
  border: 1px solid var(--border-color);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-15px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.dropdown-content a {
  display: block;
  padding: 18px 28px;
  text-decoration: none;
  transition: all 0.2s ease;
  white-space: nowrap;
  color: var(--text-color);
  font-weight: 500;
  font-size: 1rem;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  line-height: 1.4;
}

.dropdown-content a:first-child {
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.dropdown-content a:last-child {
  border-bottom: none;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.dropdown-content a:hover {
  background-color: var(--primary-color);
  color: var(--secondary-color);
  transform: translateX(8px);
  padding-left: 36px;
}

.dropdown.open .dropdown-content {
  display: block;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown.open i {
  transform: rotate(180deg);
}

/* Navbar actions */
.navbar-actions {
  display: flex;
  align-items: center;
}

.btn-login {
  display: inline-block;
  padding: 8px 20px;
  background-color: var(--primary-color);
  color: var(--secondary-color);
  border-radius: 5px;
  font-weight: 500;
  margin-right: 15px;
  transition: background-color var(--transition-speed),
    transform var(--transition-speed);
}

.btn-login:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px var(--shadow-color);
}

.theme-toggle,
.menu-toggle {
  background: none;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  font-size: 1.2rem;
  padding: 8px;
  margin-left: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--transition-speed);
  width: 40px;
  height: 40px;
}

.theme-toggle:hover {
  background-color: var(--card-bg-color);
  transform: scale(1.1);
}

.menu-toggle {
  display: none;
  flex-direction: column;
}

.menu-toggle span {
  display: block;
  width: 25px;
  height: 3px;
  background-color: var(--text-color);
  margin: 2px 0;
  transition: transform 0.3s, opacity 0.3s;
}

.menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(5px, -5px);
}

/* Mobile menu */
.mobile-menu {
  position: fixed;
  top: 70px;
  left: 0;
  right: 0;
  background-color: var(--background-color);
  box-shadow: 0 5px 10px var(--shadow-color);
  z-index: 999;
  transform: translateY(-100%);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.mobile-menu.active {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-menu-content {
  max-height: 80vh;
  overflow-y: auto;
  padding: 20px;
}

.mobile-menu ul {
  list-style: none;
}

.mobile-menu li {
  margin-bottom: 15px;
}

.mobile-menu a {
  display: block;
  padding: 10px 0;
  font-weight: 500;
  position: relative;
}

.mobile-menu a:after {
  content: "";
  position: absolute;
  width: 50%;
  transform: scaleX(0);
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transform-origin: bottom right;
  transition: transform 0.3s ease-out;
}

.mobile-menu a:hover:after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

.theme-toggle-mobile {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  padding: 10px 0;
  border-top: 1px solid var(--shadow-color);
}

/* Toggle switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 26px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 4px;
  bottom: 4px;
  background-color: var(--secondary-color);
  transition: 0.4s;
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(24px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

/* Hero Section */
.hero-section {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  padding: 70px 0 0 0;
  background-color: var(--background-color);
  min-height: 600px;
}

/* Remove background image as requested - no background image needed */
.hero-bg {
  display: none;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  padding: 40px 20px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  /* Light mode: black text */
  color: var(--text-color);
}

/* Dark mode: white text */
.dark-mode .hero-content {
  color: var(--text-color);
}

.hero-content h1 {
  font-size: 3rem;
  font-weight: 600;
  margin-bottom: 20px;
  animation: fadeInUp 1s;
  /* Ensure text is visible in both modes */
  color: var(--text-color);
  text-shadow: none;
  line-height: 1.2;
  text-align: center;
}

.dark-mode .hero-content h1 {
  color: var(--text-color);
}

.hero-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  animation: fadeInUp 1s 0.3s both;
  color: var(--text-color);
  text-shadow: none;
  line-height: 1.6;
  text-align: center;
  max-width: 600px;
}

.dark-mode .hero-content p {
  color: var(--text-color);
}

.btn-primary {
  display: inline-block;
  padding: 12px 30px;
  background-color: var(--primary-color);
  color: var(--secondary-color);
  border-radius: 50px;
  font-weight: 500;
  transition: all var(--transition-speed);
  animation: fadeInUp 1s 0.6s both;
}

.btn-primary:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Section styles */
section {
  padding: 80px 0;
  position: relative;
  background-color: var(--background-color);
}

.section-title {
  text-align: center;
  margin-bottom: 60px;
}

.section-title h2 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.section-title h2:after {
  content: "";
  position: absolute;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 2px;
}

.section-title p {
  color: var(--text-color);
  opacity: 0.8;
  margin-top: 25px;
  font-size: 1.1rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* About section */
.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
}

.about-info h3,
.about-social h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.about-info p {
  margin-bottom: 20px;
}

.about-social p {
  margin-bottom: 15px;
}

.social-icons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.social-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: transparent;
  border: 2px solid var(--text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-speed);
  padding: 8px;
  position: relative;
  overflow: hidden;
}

.social-icon img {
  width: 20px;
  height: 20px;
  opacity: 1;
  transform: scale(1);
  transition: all var(--transition-speed);
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(7500%)
    hue-rotate(0deg) brightness(0%) contrast(100%);
}

.dark-mode .social-icon {
  background-color: transparent;
  border-color: var(--text-color);
}

.dark-mode .social-icon img {
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7500%)
    hue-rotate(0deg) brightness(100%) contrast(100%);
}

.social-icon:hover {
  transform: translateY(-5px) scale(1.1);
  background-color: var(--text-color);
  border-color: var(--text-color);
}

.social-icon:hover img {
  opacity: 1;
  transform: scale(1.1);
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7500%)
    hue-rotate(0deg) brightness(100%) contrast(100%);
}

.dark-mode .social-icon:hover {
  background-color: var(--text-color);
  border-color: var(--text-color);
}

.dark-mode .social-icon:hover img {
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(7500%)
    hue-rotate(0deg) brightness(0%) contrast(100%);
}

/* Services section */
.services-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.service-card {
  background-color: var(--card-bg-color);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: transform var(--transition-speed);
}

.service-card:hover {
  transform: translateY(-10px);
}

.service-img {
  height: 200px;
  overflow: hidden;
}

.service-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
}

.service-card:hover .service-img img {
  transform: scale(1.1);
}

.service-info {
  padding: 20px;
}

.service-info h3 {
  margin-bottom: 10px;
  font-weight: 600;
}

.service-price {
  font-weight: 600;
  font-size: 1.2rem;
  color: var(--primary-color);
  margin-top: 10px;
}

.service-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.btn-buy {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 10px 15px;
  background-color: var(--primary-color);
  color: var(--secondary-color);
  border: none;
  border-radius: 5px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-speed);
}

.btn-buy:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px var(--shadow-color);
}

.btn-cart {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  border-radius: 5px;
  cursor: pointer;
  transition: all var(--transition-speed);
}

.btn-cart:hover {
  background-color: var(--primary-color);
  color: var(--secondary-color);
  transform: translateY(-2px);
}

/* Portfolio section */
.portfolio-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.portfolio-card {
  background-color: var(--card-bg-color);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px var(--shadow-color);
}

.portfolio-img {
  height: 200px;
  overflow: hidden;
  position: relative;
}

.portfolio-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
}

.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--overlay-color);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity var(--transition-speed);
}

.portfolio-card:hover .portfolio-overlay {
  opacity: 1;
}

.portfolio-card:hover .portfolio-img img {
  transform: scale(1.1);
}

.btn-preview {
  padding: 8px 20px;
  background-color: var(--secondary-color);
  color: var(--primary-color);
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: all var(--transition-speed);
}

.btn-preview:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.portfolio-info {
  padding: 20px;
}

.portfolio-info h3 {
  margin-bottom: 10px;
  font-weight: 600;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  z-index: 2000;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--overlay-color);
  overflow: auto;
}

.modal-content {
  background-color: var(--background-color);
  margin: 10% auto;
  padding: 30px;
  width: 80%;
  max-width: 800px;
  border-radius: 10px;
  animation: fadeInDown 0.5s;
  position: relative;
}

.close-modal {
  position: absolute;
  top: 10px;
  right: 20px;
  font-size: 1.8rem;
  color: var(--text-color);
  cursor: pointer;
  transition: color var(--transition-speed);
}

.close-modal:hover {
  color: var(--primary-color);
}

/* Transaction Modal Styles */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  color: var(--text-color);
}

.modal-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  justify-content: flex-end;
}

.btn-secondary {
  padding: 10px 20px;
  background-color: transparent;
  color: var(--text-color);
  border: 2px solid var(--border-color);
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: all var(--transition-speed);
}

.btn-secondary:hover {
  background-color: var(--border-color);
  transform: translateY(-2px);
}

.btn-danger {
  padding: 10px 20px;
  background-color: var(--danger-color);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: all var(--transition-speed);
}

.btn-danger:hover {
  background-color: #c82333;
  transform: translateY(-2px);
}

/* Blog section */
.blog-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.blog-card {
  background-color: var(--card-bg-color);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px var(--shadow-color);
  transition: transform var(--transition-speed);
}

.blog-card:hover {
  transform: translateY(-10px);
}

.blog-img {
  height: 200px;
  overflow: hidden;
}

.blog-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
}

.blog-card:hover .blog-img img {
  transform: scale(1.1);
}

.blog-info {
  padding: 20px;
}

.blog-info h3 {
  margin-bottom: 10px;
  font-weight: 600;
  font-size: 1.2rem;
}

.blog-info p {
  margin-bottom: 15px;
  color: var(--text-color);
  opacity: 0.8;
}

.blog-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.blog-date {
  font-size: 0.8rem;
  color: var(--text-color);
  opacity: 0.7;
}

.read-more {
  color: var(--primary-color);
  font-weight: 500;
  text-decoration: none;
  transition: color var(--transition-speed);
}

.read-more:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

/* Blog Detail Modal Styles */
.blog-detail {
  max-width: 800px;
  margin: 0 auto;
}

.blog-detail-header h2 {
  font-size: 2rem;
  margin-bottom: 15px;
  color: var(--text-color);
}

.blog-detail-meta {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.blog-detail-meta .blog-date {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
}

.blog-detail-image {
  margin-bottom: 30px;
}

.blog-detail-image img {
  width: 100%;
  border-radius: 8px;
}

.blog-detail-content {
  line-height: 1.8;
  font-size: 1.1rem;
  margin-bottom: 30px;
}

.blog-detail-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.blog-tags {
  display: flex;
  gap: 10px;
}

.tag {
  background-color: var(--primary-color);
  color: var(--secondary-color);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.blog-share {
  display: flex;
  align-items: center;
  gap: 10px;
}

.share-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background-color: var(--card-bg-color);
  color: var(--text-color);
  transition: all var(--transition-speed);
}

.share-link:hover {
  background-color: var(--primary-color);
  color: var(--secondary-color);
  transform: translateY(-2px);
}

/* Partnership section */
.partnership-section {
  background-color: var(--card-bg-color);
}

.partnership-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  gap: 30px;
}

.partner {
  width: 180px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--transition-speed);
}

.partner-logo {
  max-height: 60px;
  max-width: 100%;
  filter: grayscale(100%);
  transition: filter var(--transition-speed);
}

.partner:hover {
  transform: scale(1.1);
}

.partner:hover .partner-logo {
  filter: grayscale(0%);
}

/* Contact section */
.contact-container {
  max-width: 700px;
  margin: 0 auto;
}

.contact-form {
  background-color: var(--card-bg-color);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px var(--shadow-color);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  background-color: var(--background-color);
  color: var(--text-color);
  transition: border var(--transition-speed);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.btn-submit {
  display: block;
  width: 100%;
  padding: 12px;
  background-color: var(--primary-color);
  color: var(--secondary-color);
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: all var(--transition-speed);
}

.btn-submit:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px var(--shadow-color);
}

/* Footer */
.footer {
  background-color: var(--footer-bg-color);
  color: var(--text-color);
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-logo h3 {
  font-size: 1.8rem;
  margin-bottom: 10px;
}

.footer-links h4,
.footer-social h4 {
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.footer-links h4:after,
.footer-social h4:after {
  content: "";
  position: absolute;
  width: 50%;
  height: 2px;
  background-color: var(--text-color);
  bottom: -5px;
  left: 0;
}

.footer-links ul {
  list-style: none;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  transition: opacity var(--transition-speed);
}

.footer-links a:hover {
  opacity: 0.8;
}

.footer-social .social-icons {
  margin-top: 15px;
}

.footer-social .social-icon {
  background-color: transparent;
  color: var(--text-color);
  border-color: var(--text-color);
}

.footer-social .social-icon img {
  opacity: 1;
  transform: scale(1);
  transition: all var(--transition-speed);
}

.footer-social .social-icon:hover {
  background-color: var(--text-color);
  border-color: var(--text-color);
}

.footer-social .social-icon:hover img {
  opacity: 1;
  transform: scale(1.1);
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7500%)
    hue-rotate(0deg) brightness(100%) contrast(100%);
}

.dark-mode .footer-social .social-icon {
  background-color: transparent;
  border-color: var(--text-color);
}

.dark-mode .footer-social .social-icon:hover {
  background-color: var(--text-color);
  border-color: var(--text-color);
}

.dark-mode .footer-social .social-icon:hover img {
  filter: brightness(0) saturate(100%) invert(0%) sepia(0%) saturate(7500%)
    hue-rotate(0deg) brightness(0%) contrast(100%);
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
  opacity: 0.7;
}

/* Back to top button */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: var(--background-color);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-speed);
  z-index: 999;
}

.back-to-top.show {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Notification */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 300px;
  background-color: var(--background-color);
  border-radius: 5px;
  box-shadow: 0 5px 15px var(--shadow-color);
  transform: translateX(100%);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-speed);
  z-index: 9999;
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
  visibility: visible;
}

.notification-content {
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notification-message {
  flex: 1;
  padding-right: 10px;
}

.close-notification {
  cursor: pointer;
  font-size: 1.2rem;
  color: var(--text-color);
}

.notification-info {
  border-left: 4px solid #3498db;
}

.notification-success {
  border-left: 4px solid #2ecc71;
}

.notification-warning {
  border-left: 4px solid #f39c12;
}

.notification-error {
  border-left: 4px solid #e74c3c;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .container {
    max-width: 900px;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }

  .navbar-menu {
    display: none;
  }

  .menu-toggle {
    display: flex;
  }

  .about-content {
    grid-template-columns: 1fr;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-content p {
    font-size: 1rem;
  }

  .section-title h2 {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .navbar .container {
    height: 60px;
  }

  .mobile-menu {
    top: 60px;
  }

  .btn-login {
    padding: 6px 15px;
    font-size: 0.9rem;
  }

  .hero-content h1 {
    font-size: 1.8rem;
  }

  .hero-section {
    height: 80vh;
  }

  .portfolio-container,
  .services-container,
  .blog-container {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 5% auto;
  }

  .back-to-top {
    width: 40px;
    height: 40px;
    bottom: 20px;
    right: 20px;
  }

  .partner {
    width: 120px;
  }
}

/* Print styles */
@media print {
  .navbar,
  .mobile-menu,
  .back-to-top,
  .btn-primary,
  .btn-preview,
  .form-group button,
  .notification {
    display: none !important;
  }

  body {
    color: #000;
    background: #fff;
  }

  a {
    text-decoration: underline;
    color: #000;
  }

  .container {
    width: 100%;
    max-width: 100%;
  }

  section {
    page-break-inside: avoid;
    padding: 20px 0;
  }
}

/* Profile Dropdown Styles */
.profile-dropdown {
  position: relative;
  margin-right: 20px;
}

.profile-link {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  color: #181818;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

body.dark-mode .profile-link {
  color: #ffffff;
}

.profile-link:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

body.dark-mode .profile-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.profile-link i {
  margin-right: 6px;
}

.profile-link span {
  margin: 0 6px;
  font-weight: 500;
}

.profile-dropdown-content {
  position: absolute;
  right: 0;
  top: 100%;
  background-color: #ffffff;
  min-width: 160px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 10;
}

body.dark-mode .profile-dropdown-content {
  background-color: #2a2a2a;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.profile-dropdown-content a {
  display: block;
  padding: 12px 16px;
  color: #181818;
  text-decoration: none;
  transition: background-color 0.3s ease;
}

body.dark-mode .profile-dropdown-content a {
  color: #ffffff;
}

.profile-dropdown-content a:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

body.dark-mode .profile-dropdown-content a:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.profile-dropdown-content a.active {
  font-weight: 600;
  position: relative;
}

.profile-dropdown-content a.active::after {
  content: "";
  position: absolute;
  bottom: 8px;
  left: 16px;
  width: 40px;
  height: 2px;
  background-color: #181818;
}

body.dark-mode .profile-dropdown-content a.active::after {
  background-color: #ffffff;
}

.profile-dropdown:hover .profile-dropdown-content {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Setting Container Styles */
.setting-container {
  padding: 80px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.setting-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  padding: 32px;
}

body.dark-mode .setting-card {
  background-color: #2a2a2a;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.setting-header {
  margin-bottom: 24px;
  text-align: center;
}

.setting-header h1 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #181818;
}

body.dark-mode .setting-header h1 {
  color: #ffffff;
}

.setting-header p {
  font-size: 16px;
  color: #666666;
}

body.dark-mode .setting-header p {
  color: #bbbbbb;
}

.setting-form .form-group {
  margin-bottom: 20px;
}

.setting-form label {
  display: block;
  font-weight: 500;
  margin-bottom: 8px;
  color: #181818;
}

body.dark-mode .setting-form label {
  color: #ffffff;
}

.setting-form input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #ffffff;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

body.dark-mode .setting-form input {
  border-color: #444444;
  background-color: #333333;
  color: #ffffff;
}

.setting-form input:focus {
  border-color: #181818;
  outline: none;
}

body.dark-mode .setting-form input:focus {
  border-color: #ffffff;
}

.setting-form .form-action {
  margin-top: 32px;
}

.setting-form .btn-primary {
  width: 100%;
  padding: 12px 16px;
}

/* Make sure feather icons render properly */
.feather {
  width: 18px;
  height: 18px;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  fill: none;
}
