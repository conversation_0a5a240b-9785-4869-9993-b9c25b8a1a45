<%--
    Document   : forgotPasswordProcess
    Created on : June 16, 2025
    Author     : Arqeta
    Description: Process forgot password request and send OTP
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.util.Random"%>
<%@page import="java.security.MessageDigest"%>
<%@page import="java.security.NoSuchAlgorithmException"%>

<%
    // Disable caching
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setHeader("Pragma", "no-cache");
    response.setDateHeader("Expires", 0);

    String email = request.getParameter("email");
    
    if (email == null || email.trim().isEmpty()) {
        session.setAttribute("message", "Email tidak boleh kosong!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp");
        return;
    }

    Connection conn = null;
    PreparedStatement ps = null;
    ResultSet rs = null;

    try {
        // Load database connection
        Class.forName("com.mysql.cj.jdbc.Driver");
        conn = DriverManager.getConnection("******************************************************************************************************", "root", "");

        // Check if email exists in admin or user table
        boolean emailExists = false;
        String userType = "";
        
        // Check admin table
        ps = conn.prepareStatement("SELECT id FROM admin WHERE email = ?");
        ps.setString(1, email);
        rs = ps.executeQuery();
        
        if (rs.next()) {
            emailExists = true;
            userType = "admin";
        }
        rs.close();
        ps.close();
        
        // If not found in admin, check user table
        if (!emailExists) {
            ps = conn.prepareStatement("SELECT id FROM user WHERE email = ?");
            ps.setString(1, email);
            rs = ps.executeQuery();
            
            if (rs.next()) {
                emailExists = true;
                userType = "user";
            }
            rs.close();
            ps.close();
        }

        if (!emailExists) {
            session.setAttribute("message", "Email tidak ditemukan dalam sistem!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../form/forgotpassword.jsp");
            return;
        }

        // Generate 6-digit OTP
        Random random = new Random();
        String otp = String.format("%06d", random.nextInt(1000000));
        
        // Store OTP in database
        ps = conn.prepareStatement("INSERT INTO otp_log (email, otp, created_at, used) VALUES (?, ?, NOW(), 0)");
        ps.setString(1, email);
        ps.setString(2, otp);
        
        int result = ps.executeUpdate();
        ps.close();

        if (result > 0) {
            // OTP stored successfully
            session.setAttribute("message", "Kode OTP telah dikirim! Kode OTP Anda: " + otp);
            session.setAttribute("messageType", "success");
            session.setAttribute("otpEmail", email);
            
            // Redirect to OTP verification step
            response.sendRedirect("../form/forgotpassword.jsp?step=otp&email=" + email);
        } else {
            session.setAttribute("message", "Gagal mengirim kode OTP. Silakan coba lagi.");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../form/forgotpassword.jsp");
        }

    } catch(ClassNotFoundException e) {
        System.out.println("MySQL Driver not found: " + e.getMessage());
        session.setAttribute("message", "Driver database tidak ditemukan!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp");
    } catch(SQLException e) {
        System.out.println("Database error: " + e.getMessage());
        e.printStackTrace();
        session.setAttribute("message", "Terjadi kesalahan database: " + e.getMessage());
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp");
    } catch(Exception e) {
        System.out.println("General error: " + e.getMessage());
        e.printStackTrace();
        session.setAttribute("message", "Terjadi kesalahan sistem: " + e.getMessage());
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp");
    } finally {
        // Close database connection
        try {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
            if (conn != null && !conn.isClosed()) {
                conn.close();
            }
        } catch (SQLException se) {
            se.printStackTrace();
        }
    }
%>
