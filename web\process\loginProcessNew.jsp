<%--
    Document   : loginProcess
    Created on : Jun 1, 2025, 7:32:11 PM
    Author     : Arqeta
    Description: Process regular login (username/email and password)
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.security.MessageDigest"%>
<%@page import="java.math.BigInteger"%>
<%@include file="../config/connection.jsp" %>

<%
    // Get login credentials from form
    String usernameOrEmail = request.getParameter("username");
    String email = request.getParameter("email");
    String password = request.getParameter("password");

    if (usernameOrEmail == null || email == null || password == null ||
        usernameOrEmail.trim().isEmpty() || email.trim().isEmpty() ||
        password.trim().isEmpty()) {
        // Redirect back to login page with error
        session.setAttribute("message", "Semua kolom harus diisi!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/signin.jsp");
        return;
    }

    // Sanitize input to prevent XSS and SQL injection
    usernameOrEmail = usernameOrEmail.trim().replaceAll("<", "&lt;").replaceAll(">", "&gt;");
    email = email.trim().toLowerCase();

    // Validate email format
    if (!email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")) {
        session.setAttribute("message", "Format email tidak valid!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/signin.jsp");
        return;
    }

    try {
        // First check if it's an admin
        PreparedStatement psAdmin = conn.prepareStatement(
                "SELECT * FROM admin WHERE (username = ? OR name = ?) AND email = ?");
        psAdmin.setString(1, usernameOrEmail);
        psAdmin.setString(2, usernameOrEmail);
        psAdmin.setString(3, email);
        ResultSet rsAdmin = psAdmin.executeQuery();

        if (rsAdmin.next()) {
            // It's an admin, verify password
            String storedPassword = rsAdmin.getString("password");
            String adminId = rsAdmin.getString("id");
            String adminName = rsAdmin.getString("name");

            // Hash the provided password for comparison
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(password.getBytes());
            String hashedPassword = new BigInteger(1, md.digest()).toString(16);

            if (storedPassword.equals(hashedPassword) || storedPassword.equals(password)) {
                // Password matches, set session variables
                session.setAttribute("adminId", adminId);
                session.setAttribute("name", adminName);
                session.setAttribute("email", email);
                session.setAttribute("isLoggedIn", true);
                session.setAttribute("message", "Login berhasil! Selamat datang, " + adminName);
                session.setAttribute("messageType", "success");

                // Redirect to admin dashboard
                response.sendRedirect("../dashboardadmin.jsp");
                return;
            } else {
                // Password doesn't match
                session.setAttribute("message", "Kata sandi tidak sesuai!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../form/signin.jsp");
                return;
            }
        }

        // If it's not an admin, check if it's a regular user
        PreparedStatement psUser = conn.prepareStatement(
                "SELECT * FROM user WHERE (username = ? OR name = ?) AND email = ?");
        psUser.setString(1, usernameOrEmail);
        psUser.setString(2, usernameOrEmail);
        psUser.setString(3, email);
        ResultSet rsUser = psUser.executeQuery();

        if (rsUser.next()) {
            // It's a user, verify password
            String storedPassword = rsUser.getString("password");
            String userId = rsUser.getString("id");
            String userName = rsUser.getString("name");

            // Hash the provided password for comparison
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(password.getBytes());
            String hashedPassword = new BigInteger(1, md.digest()).toString(16);

            if (storedPassword.equals(hashedPassword) || storedPassword.equals(password)) {
                // Password matches, set session variables
                session.setAttribute("userId", userId);
                session.setAttribute("name", userName);
                session.setAttribute("email", email);
                session.setAttribute("isLoggedIn", true);
                session.setAttribute("message", "Login berhasil! Selamat datang, " + userName);
                session.setAttribute("messageType", "success");

                // Redirect to home page
                response.sendRedirect("../home.jsp");
                return;
            } else {
                // Password doesn't match
                session.setAttribute("message", "Kata sandi tidak sesuai!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../form/signin.jsp");
                return;
            }
        }

        // If we get here, no matching user or admin was found
        session.setAttribute("message", "Akun tidak ditemukan dengan nama/username dan email yang diberikan!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/signin.jsp");

    } catch(Exception e) {
        // Log the error
        System.out.println("Login error: " + e.getMessage());
        e.printStackTrace();

        // Redirect with error
        session.setAttribute("message", "Terjadi kesalahan sistem: " + e.getMessage());
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/signin.jsp");
    } finally {
        // Close database connection
        try {
            if (conn != null && !conn.isClosed()) {
                conn.close();
            }
        } catch (SQLException se) {
            se.printStackTrace();
        }
    }
%>
