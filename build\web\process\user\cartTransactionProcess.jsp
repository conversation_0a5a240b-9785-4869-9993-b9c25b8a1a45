<%--
    Document   : cartTransactionProcess
    Created on : Jun 16, 2025
    Author     : Arqeta
    Description: 
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.math.BigDecimal"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Cek apakah user sudah login
    String userId = (String) session.getAttribute("userId");
    if (userId == null) {
        response.sendRedirect("../../form/signin.jsp");
        return;
    }
    
    // Ambil parameter dari form
    String serviceId = request.getParameter("serviceId");
    String serviceName = request.getParameter("serviceName");
    String quantity = request.getParameter("quantity");
    
    if (serviceId == null || serviceName == null || quantity == null) {
        session.setAttribute("message", "Data transaksi tidak lengkap!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../../form/user/basket.jsp");
        return;
    }
    
    try {
        int serviceIdInt = Integer.parseInt(serviceId);
        int quantityInt = Integer.parseInt(quantity);
        
        // Ambil data layanan dari database untuk mendapatkan harga terkini
        PreparedStatement psService = conn.prepareStatement(
            "SELECT name, price, quantity FROM services WHERE id = ?"
        );
        psService.setInt(1, serviceIdInt);
        ResultSet rsService = psService.executeQuery();
        
        if (!rsService.next()) {
            session.setAttribute("message", "Layanan tidak ditemukan!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../form/user/basket.jsp");
            return;
        }
        
        String currentServiceName = rsService.getString("name");
        BigDecimal servicePrice = rsService.getBigDecimal("price");
        int availableQuantity = rsService.getInt("quantity");
        
        // Cek apakah kuantitas mencukupi
        if (availableQuantity < quantityInt) {
            session.setAttribute("message", "Kuantitas layanan tidak mencukupi!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../form/user/basket.jsp");
            return;
        }
        
        rsService.close();
        psService.close();
        
        // Hitung total harga
        BigDecimal totalPrice = servicePrice.multiply(new BigDecimal(quantityInt));
        
        // Insert transaksi
        PreparedStatement psTransaction = conn.prepareStatement(
            "INSERT INTO transaction (name, amount, unit, total, user_id, service_id, status, price) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
        );
        
        psTransaction.setString(1, currentServiceName);
        psTransaction.setBigDecimal(2, servicePrice);
        psTransaction.setInt(3, quantityInt);
        psTransaction.setBigDecimal(4, totalPrice);
        psTransaction.setInt(5, Integer.parseInt(userId));
        psTransaction.setInt(6, serviceIdInt);
        psTransaction.setString(7, "pending");
        psTransaction.setBigDecimal(8, servicePrice);
        
        int result = psTransaction.executeUpdate();
        psTransaction.close();
        
        if (result > 0) {
            // Update kuantitas layanan
            PreparedStatement psUpdate = conn.prepareStatement(
                "UPDATE services SET quantity = quantity - ? WHERE id = ?"
            );
            psUpdate.setInt(1, quantityInt);
            psUpdate.setInt(2, serviceIdInt);
            psUpdate.executeUpdate();
            psUpdate.close();
            
            // Hapus item dari keranjang setelah berhasil dibeli
            PreparedStatement psDeleteCart = conn.prepareStatement(
                "DELETE FROM cart WHERE user_id = ? AND service_id = ?"
            );
            psDeleteCart.setInt(1, Integer.parseInt(userId));
            psDeleteCart.setInt(2, serviceIdInt);
            psDeleteCart.executeUpdate();
            psDeleteCart.close();
            
            session.setAttribute("message", "Transaksi berhasil! Silakan tunggu konfirmasi dari admin.");
            session.setAttribute("messageType", "success");
        } else {
            session.setAttribute("message", "Gagal memproses transaksi!");
            session.setAttribute("messageType", "error");
        }
        
    } catch (NumberFormatException e) {
        session.setAttribute("message", "Format data tidak valid!");
        session.setAttribute("messageType", "error");
    } catch (SQLException e) {
        session.setAttribute("message", "Terjadi kesalahan database: " + e.getMessage());
        session.setAttribute("messageType", "error");
    }
    
    response.sendRedirect("../../form/user/basket.jsp");
%>
