# Fitur Ekspor Data Transaksi

## Deskripsi
Fitur ini memungkinkan admin untuk mengekspor data transaksi dalam format Excel (CSV) dan PDF (HTML) dari halaman Dashboard Admin.

## Lokasi Fitur
- **Halaman**: Dashboard Admin > Data Transaksi
- **Posisi**: Button ekspor berada di sebelah kiri dropdown "Semua Status"

## Fitur yang Ditambahkan

### 1. Button Ekspor
- **Excel Button**: Mengekspor data dalam format CSV yang dapat dibuka di Microsoft Excel
- **PDF Button**: Mengekspor data dalam format HTML yang dapat dicetak sebagai PDF

### 2. File yang Dimodifikasi/Ditambahkan

#### File yang Dimodifikasi:
1. `web/form/admin/transactiondata.jsp`
   - Menambahkan button ekspor Excel dan PDF
   - Menambahkan fungsi JavaScript untuk ekspor

2. `web/dist/css/dashboard.css`
   - Menambahkan styling untuk button ekspor
   - Menambahkan responsive design untuk mobile

3. `web/WEB-INF/web.xml`
   - Menambahkan konfigurasi servlet untuk export processor

#### File yang Ditambahkan:
1. `web/process/admin/exportTransactions.jsp`
   - Processor untuk menghandle ekspor data
   - Mendukung format Excel (CSV) dan PDF (HTML)

### 3. Data yang Diekspor
Data transaksi yang diekspor meliputi:
- ID Transaksi
- Nama Layanan
- Nama Pengguna
- Email Pengguna
- Jumlah (Amount)
- Unit
- Total
- Status (dalam bahasa Indonesia)
- Tanggal Dibuat

### 4. Format Ekspor

#### Excel (CSV):
- File dengan ekstensi `.csv`
- Dapat dibuka di Microsoft Excel, Google Sheets, dll.
- Nama file: `data_transaksi_YYYYMMDD_HHMMSS.csv`

#### PDF (HTML):
- File HTML yang dapat dicetak sebagai PDF
- Styling yang rapi untuk pencetakan
- Auto-print dialog saat file dibuka
- Nama file: `data_transaksi_YYYYMMDD_HHMMSS.html`

### 5. Keamanan
- Hanya admin yang login yang dapat mengakses fitur ekspor
- Validasi session admin sebelum proses ekspor
- Error handling untuk database connection

### 6. Responsive Design
- Button ekspor responsive untuk desktop, tablet, dan mobile
- Pada mobile kecil, teks button disembunyikan, hanya icon yang ditampilkan
- Layout yang optimal untuk semua ukuran layar

## Cara Penggunaan
1. Login sebagai admin
2. Masuk ke halaman "Data Transaksi"
3. Klik button "Excel" untuk ekspor ke format CSV
4. Klik button "PDF" untuk ekspor ke format HTML/PDF
5. File akan otomatis didownload

## Dependensi
- MySQL JDBC Driver (sudah ada)
- JSP/Servlet support (sudah ada)
- Tidak memerlukan library tambahan

## Catatan Penting
- Fitur ini mengekspor SEMUA data transaksi, tidak terpengaruh oleh filter status
- File CSV menggunakan encoding UTF-8 untuk mendukung karakter Indonesia
- File HTML sudah dioptimasi untuk pencetakan PDF
- Error handling sudah diimplementasi untuk menangani masalah database

## Troubleshooting
Jika terjadi masalah:
1. Pastikan admin sudah login
2. Periksa koneksi database
3. Pastikan folder temp dapat diakses untuk file download
4. Periksa log server untuk error detail
