-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jun 11, 2025 at 12:07 PM
-- Server version: 8.0.30
-- PHP Version: 8.1.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `arqeta`
--

-- --------------------------------------------------------

--
-- Table structure for table `portfolio`
--

CREATE TABLE `portfolio` (
  `id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `image` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `portfolio`
--

INSERT INTO `portfolio` (`id`, `name`, `image`, `description`, `created_at`, `updated_at`) VALUES
(1, 'Dashboard Analytics', 'portfolio-1.jpg', 'UI Dashboard Analytics untuk sebuah perusahaan teknologi dengan visualisasi data yang komprehensif dan mudah dipahami.', '2025-05-25 08:30:00', '2025-05-25 08:30:00'),
(2, 'Mobile Banking App', 'portfolio-2.jpg', 'Desain UI aplikasi mobile banking dengan fokus pada kemudahan penggunaan dan keamanan transaksi.', '2025-05-25 09:15:00', '2025-05-25 09:15:00'),
(3, 'E-commerce Website', 'portfolio-3.jpg', 'UI Website e-commerce dengan pengalaman belanja yang menyenangkan dan proses checkout yang efisien.', '2025-05-25 10:00:00', '2025-05-25 10:00:00'),
(4, 'Travel App', 'portfolio-4.jpg', 'Aplikasi travel dengan fitur pencarian destinasi, booking hotel, dan pemesanan tiket dalam satu platform.', '2025-05-25 10:45:00', '2025-05-25 10:45:00'),
(5, 'Health Monitoring App', 'portfolio-5.jpg', 'Aplikasi monitoring kesehatan dengan tampilan yang intuitif dan visualisasi data kesehatan yang mudah dipahami.', '2025-05-25 11:30:00', '2025-05-25 11:30:00'),
(6, 'Learning Management System', 'portfolio-6.jpg', 'UI untuk platform pembelajaran online dengan fitur kursus interaktif, quiz, dan tracking progress belajar.', '2025-05-25 12:15:00', '2025-05-25 12:15:00');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `portfolio`
--
ALTER TABLE `portfolio`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `portfolio`
--
ALTER TABLE `portfolio`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
