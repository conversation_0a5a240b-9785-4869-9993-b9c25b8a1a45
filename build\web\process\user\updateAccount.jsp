<%--
    Document   : updateAccount
    Created on : Jun 17, 2025
    Author     : Arqeta
    Description: Process user account updates for non-Google users
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>
<%@page import="java.security.MessageDigest"%>
<%@page import="java.nio.charset.StandardCharsets"%>
<%@page import="java.math.BigInteger"%>

<%
    // Pastikan user sudah login
    String userId = (String) session.getAttribute("userId");
    if (userId == null) {
        response.sendRedirect("../../form/signin.jsp");
        return;
    }

    // Pastikan ini adalah POST request
    if (!"POST".equalsIgnoreCase(request.getMethod())) {
        response.sendRedirect("../../dashboarduser.jsp?page=account");
        return;
    }

    try {
        // Ambil data dari form
        String name = request.getParameter("name");
        String username = request.getParameter("username");
        String email = request.getParameter("email");
        String currentPassword = request.getParameter("currentPassword");
        String newPassword = request.getParameter("newPassword");
        String confirmPassword = request.getParameter("confirmPassword");

        // Validasi input dasar
        if (name == null || name.trim().isEmpty() ||
            username == null || username.trim().isEmpty() ||
            email == null || email.trim().isEmpty()) {
            session.setAttribute("message", "Semua field wajib diisi!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../dashboarduser.jsp?page=account");
            return;
        }

        // Trim input
        name = name.trim();
        username = username.trim();
        email = email.trim();

        // Cek apakah user adalah Google user (tidak boleh update)
        PreparedStatement psGoogleCheck = conn.prepareStatement("SELECT id FROM user_google WHERE user_id = ?");
        psGoogleCheck.setString(1, userId);
        ResultSet rsGoogleCheck = psGoogleCheck.executeQuery();
        
        if (rsGoogleCheck.next()) {
            rsGoogleCheck.close();
            psGoogleCheck.close();
            session.setAttribute("message", "Akun Google tidak dapat diubah melalui sistem ini!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../dashboarduser.jsp?page=account");
            return;
        }
        rsGoogleCheck.close();
        psGoogleCheck.close();

        // Ambil data user saat ini
        PreparedStatement psCurrentUser = conn.prepareStatement("SELECT * FROM user WHERE id = ?");
        psCurrentUser.setString(1, userId);
        ResultSet rsCurrentUser = psCurrentUser.executeQuery();
        
        if (!rsCurrentUser.next()) {
            rsCurrentUser.close();
            psCurrentUser.close();
            session.setAttribute("message", "User tidak ditemukan!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../dashboarduser.jsp?page=account");
            return;
        }
        
        String currentStoredPassword = rsCurrentUser.getString("password");
        String currentName = rsCurrentUser.getString("name");
        String currentUsername = rsCurrentUser.getString("username");
        String currentEmail = rsCurrentUser.getString("email");
        
        rsCurrentUser.close();
        psCurrentUser.close();

        // Validasi jika ingin mengubah password
        boolean updatePassword = false;
        String hashedNewPassword = "";
        
        if ((newPassword != null && !newPassword.trim().isEmpty()) || 
            (confirmPassword != null && !confirmPassword.trim().isEmpty())) {
            
            // Validasi current password harus diisi
            if (currentPassword == null || currentPassword.trim().isEmpty()) {
                session.setAttribute("message", "Masukkan kata sandi saat ini untuk mengubah kata sandi!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboarduser.jsp?page=account");
                return;
            }
            
            // Validasi current password benar
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(currentPassword.getBytes());
            String hashedCurrentPassword = new BigInteger(1, md.digest()).toString(16);
            
            // Pad with leading zeros if necessary
            while (hashedCurrentPassword.length() < 64) {
                hashedCurrentPassword = "0" + hashedCurrentPassword;
            }
            
            if (!currentStoredPassword.equals(hashedCurrentPassword)) {
                session.setAttribute("message", "Kata sandi saat ini tidak benar!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboarduser.jsp?page=account");
                return;
            }
            
            // Validasi new password dan confirm password
            if (newPassword == null || newPassword.trim().isEmpty()) {
                session.setAttribute("message", "Kata sandi baru tidak boleh kosong!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboarduser.jsp?page=account");
                return;
            }
            
            if (!newPassword.equals(confirmPassword)) {
                session.setAttribute("message", "Konfirmasi kata sandi tidak sesuai!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboarduser.jsp?page=account");
                return;
            }
            
            if (newPassword.length() < 6) {
                session.setAttribute("message", "Kata sandi baru minimal 6 karakter!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboarduser.jsp?page=account");
                return;
            }
            
            // Hash password baru
            MessageDigest mdNew = MessageDigest.getInstance("SHA-256");
            mdNew.update(newPassword.getBytes());
            hashedNewPassword = new BigInteger(1, mdNew.digest()).toString(16);
            
            // Pad with leading zeros if necessary
            while (hashedNewPassword.length() < 64) {
                hashedNewPassword = "0" + hashedNewPassword;
            }
            
            updatePassword = true;
        }

        // Cek apakah ada perubahan data
        boolean hasChanges = false;
        if (!name.equals(currentName) || !username.equals(currentUsername) || 
            !email.equals(currentEmail) || updatePassword) {
            hasChanges = true;
        }
        
        if (!hasChanges) {
            session.setAttribute("message", "Tidak ada perubahan data yang disimpan!");
            session.setAttribute("messageType", "info");
            response.sendRedirect("../../dashboarduser.jsp?page=account");
            return;
        }

        // Validasi username dan email unik (jika berubah)
        if (!username.equals(currentUsername)) {
            PreparedStatement psCheckUsername = conn.prepareStatement("SELECT id FROM user WHERE username = ? AND id != ?");
            psCheckUsername.setString(1, username);
            psCheckUsername.setString(2, userId);
            ResultSet rsCheckUsername = psCheckUsername.executeQuery();
            
            if (rsCheckUsername.next()) {
                rsCheckUsername.close();
                psCheckUsername.close();
                session.setAttribute("message", "Username sudah digunakan oleh user lain!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboarduser.jsp?page=account");
                return;
            }
            rsCheckUsername.close();
            psCheckUsername.close();
        }
        
        if (!email.equals(currentEmail)) {
            PreparedStatement psCheckEmail = conn.prepareStatement("SELECT id FROM user WHERE email = ? AND id != ?");
            psCheckEmail.setString(1, email);
            psCheckEmail.setString(2, userId);
            ResultSet rsCheckEmail = psCheckEmail.executeQuery();
            
            if (rsCheckEmail.next()) {
                rsCheckEmail.close();
                psCheckEmail.close();
                session.setAttribute("message", "Email sudah digunakan oleh user lain!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboarduser.jsp?page=account");
                return;
            }
            rsCheckEmail.close();
            psCheckEmail.close();
        }

        // Update data user
        String updateQuery;
        PreparedStatement psUpdate;
        
        if (updatePassword) {
            updateQuery = "UPDATE user SET name = ?, username = ?, email = ?, password = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            psUpdate = conn.prepareStatement(updateQuery);
            psUpdate.setString(1, name);
            psUpdate.setString(2, username);
            psUpdate.setString(3, email);
            psUpdate.setString(4, hashedNewPassword);
            psUpdate.setString(5, userId);
        } else {
            updateQuery = "UPDATE user SET name = ?, username = ?, email = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
            psUpdate = conn.prepareStatement(updateQuery);
            psUpdate.setString(1, name);
            psUpdate.setString(2, username);
            psUpdate.setString(3, email);
            psUpdate.setString(4, userId);
        }
        
        int result = psUpdate.executeUpdate();
        psUpdate.close();
        
        if (result > 0) {
            // Update session data jika berhasil
            session.setAttribute("name", name);
            session.setAttribute("email", email);
            
            session.setAttribute("message", "Akun berhasil diperbarui!");
            session.setAttribute("messageType", "success");
        } else {
            session.setAttribute("message", "Gagal memperbarui akun!");
            session.setAttribute("messageType", "error");
        }

    } catch (Exception e) {
        session.setAttribute("message", "Terjadi kesalahan pada server: " + e.getMessage());
        session.setAttribute("messageType", "error");
    }

    // Redirect kembali ke halaman account
    response.sendRedirect("../../dashboarduser.jsp?page=account");
%>
