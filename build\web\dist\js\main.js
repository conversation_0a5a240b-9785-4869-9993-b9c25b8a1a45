/**
 * Main JavaScript for Arqeta website
 * Author: Arqeta Team
 * Version: 1.0
 */

document.addEventListener("DOMContentLoaded", function () {
  // Initialize variables
  const navbar = document.getElementById("navbar");
  const menuToggle = document.getElementById("menuToggle");
  const mobileMenu = document.getElementById("mobileMenu");
  const themeToggle = document.getElementById("themeToggle");
  const themeIcon = document.getElementById("themeIcon");
  const themeSwitchMobile = document.getElementById("themeSwitchMobile");
  const backToTopBtn = document.getElementById("backToTopBtn");
  const portfolioModal = document.getElementById("portfolioModal");
  const dropdownLinks = document.querySelectorAll(".dropdown > a");

  // Check for saved theme in local storage or set default to light mode
  const savedTheme = localStorage.getItem("theme");
  if (savedTheme === "dark") {
    document.body.classList.add("dark-mode");
    if (themeIcon) {
      themeIcon.setAttribute("data-feather", "sun");
    }
    if (themeSwitchMobile) {
      themeSwitchMobile.checked = true;
    }
  } else {
    // Default to light mode - moon icon for light mode
    document.body.classList.remove("dark-mode");
    if (themeIcon) {
      themeIcon.setAttribute("data-feather", "moon");
    }
    if (themeSwitchMobile) {
      themeSwitchMobile.checked = false;
    }
    localStorage.setItem("theme", "light");
  }

  // Navbar transparency on scroll
  window.addEventListener("scroll", function () {
    if (window.scrollY > 100) {
      navbar.classList.add("transparent");
      if (backToTopBtn) {
        backToTopBtn.classList.add("show");
      }
    } else {
      navbar.classList.remove("transparent");
      if (backToTopBtn) {
        backToTopBtn.classList.remove("show");
      }
    }
  });

  // Mobile menu toggle
  if (menuToggle && mobileMenu) {
    menuToggle.addEventListener("click", function () {
      mobileMenu.classList.toggle("active");

      // Animate hamburger icon
      this.classList.toggle("active");

      // Prevent body scroll when menu is open
      if (mobileMenu.classList.contains("active")) {
        document.body.style.overflow = "hidden";
      } else {
        document.body.style.overflow = "";
      }
    });

    // Close mobile menu when clicking on a link
    const mobileMenuLinks = mobileMenu.querySelectorAll("a");
    mobileMenuLinks.forEach(function (link) {
      link.addEventListener("click", function () {
        mobileMenu.classList.remove("active");
        menuToggle.classList.remove("active");
        document.body.style.overflow = "";
      });
    });
  }

  // Theme toggle
  if (themeToggle) {
    themeToggle.addEventListener("click", toggleTheme);
  }

  // Mobile theme switch
  if (themeSwitchMobile) {
    themeSwitchMobile.addEventListener("change", toggleTheme);
  }

  // Toggle theme function - Fixed icon switching
  function toggleTheme() {
    document.body.classList.toggle("dark-mode");

    // Toggle icon: Light mode = moon, Dark mode = sun
    if (document.body.classList.contains("dark-mode")) {
      // Dark mode: show sun icon
      if (themeIcon) {
        themeIcon.setAttribute("data-feather", "sun");
      }
      localStorage.setItem("theme", "dark");
      if (themeSwitchMobile) {
        themeSwitchMobile.checked = true;
      }
    } else {
      // Light mode: show moon icon
      if (themeIcon) {
        themeIcon.setAttribute("data-feather", "moon");
      }
      localStorage.setItem("theme", "light");
      if (themeSwitchMobile) {
        themeSwitchMobile.checked = false;
      }
    }

    // Re-render feather icons immediately
    if (typeof feather !== "undefined") {
      feather.replace();
    }
  }

  // Back to top button
  if (backToTopBtn) {
    backToTopBtn.addEventListener("click", function () {
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    });
  }

  // Portfolio modal
  const previewBtns = document.querySelectorAll(".btn-preview");
  if (previewBtns.length > 0 && portfolioModal) {
    previewBtns.forEach(function (btn) {
      btn.addEventListener("click", function () {
        const portfolioId = this.getAttribute("data-id");
        showPortfolioDetails(portfolioId);
      });
    });

    // Close modal when clicking the close button
    const closeModal = document.querySelector(".close-modal");
    if (closeModal) {
      closeModal.addEventListener("click", function () {
        portfolioModal.style.display = "none";
      });
    }

    // Close modal when clicking outside of it
    window.addEventListener("click", function (e) {
      if (e.target === portfolioModal) {
        portfolioModal.style.display = "none";
      }
    });
  }

  // Function to show portfolio details
  function showPortfolioDetails(id) {
    // Make AJAX request to get portfolio details
    const xhr = new XMLHttpRequest();
    xhr.open("GET", "process/getPortfolioDetails.jsp?id=" + id, true);

    xhr.onload = function () {
      if (this.status === 200) {
        document.getElementById("portfolioModalContent").innerHTML =
          this.responseText;
        portfolioModal.style.display = "block";
      }
    };

    xhr.send();
  }

  // Dropdown functionality - Fixed arrow animation
  if (dropdownLinks.length > 0) {
    dropdownLinks.forEach(function (link) {
      link.addEventListener("click", function (e) {
        e.preventDefault();
        const parent = this.parentElement;
        const isOpen = parent.classList.contains("open");
        const chevronIcon = this.querySelector(
          '[data-feather="chevron-down"], [data-feather="chevron-up"]'
        );

        // Close all other dropdowns first
        dropdownLinks.forEach(function (otherLink) {
          const otherParent = otherLink.parentElement;
          const otherChevron = otherLink.querySelector(
            '[data-feather="chevron-down"], [data-feather="chevron-up"]'
          );
          if (otherParent !== parent) {
            otherParent.classList.remove("open");
            if (otherChevron) {
              otherChevron.setAttribute("data-feather", "chevron-down");
            }
          }
        });

        // Toggle current dropdown
        if (!isOpen) {
          parent.classList.add("open");
          if (chevronIcon) {
            chevronIcon.setAttribute("data-feather", "chevron-up");
          }
        } else {
          parent.classList.remove("open");
          if (chevronIcon) {
            chevronIcon.setAttribute("data-feather", "chevron-down");
          }
        }

        // Re-render feather icons immediately
        if (typeof feather !== "undefined") {
          feather.replace();
        }
      });
    });

    // Close dropdown when clicking outside
    document.addEventListener("click", function (e) {
      let clickedInside = false;

      dropdownLinks.forEach(function (link) {
        const parent = link.parentElement;
        const dropdown = parent.querySelector(".dropdown-content");

        if (parent.contains(e.target)) {
          clickedInside = true;
        }
      });

      if (!clickedInside) {
        dropdownLinks.forEach(function (link) {
          const parent = link.parentElement;
          const chevronIcon = link.querySelector(
            '[data-feather="chevron-down"], [data-feather="chevron-up"]'
          );
          parent.classList.remove("open");
          if (chevronIcon) {
            chevronIcon.setAttribute("data-feather", "chevron-down");
          }
        });

        // Re-render feather icons
        if (typeof feather !== "undefined") {
          feather.replace();
        }
      }
    });
  }

  // Parallax effect for sections
  window.addEventListener("scroll", function () {
    const sections = document.querySelectorAll(
      ".hero-section, .about-section, .services-section, .portfolio-section, .blog-section, .partnership-section, .contact-section"
    );

    sections.forEach(function (section) {
      const sectionTop = section.getBoundingClientRect().top;
      const scrollPosition = window.innerHeight;

      if (sectionTop < scrollPosition - 100) {
        section.classList.add("active");
      }
    });
  });

  // Initialize animations
  initAnimations();

  // Disable right-click
  document.addEventListener("contextmenu", function (e) {
    e.preventDefault();
    showNotification("Klik kanan dinonaktifkan pada situs ini!", "error");
  });

  // Disable inspect element (F12 and Ctrl+Shift+I)
  document.addEventListener("keydown", function (e) {
    if (e.keyCode === 123 || (e.ctrlKey && e.shiftKey && e.keyCode === 73)) {
      e.preventDefault();
      showNotification(
        "Inspeksi elemen dinonaktifkan pada situs ini!",
        "error"
      );
    }
  });

  // Prevent copy-paste
  document.addEventListener("copy", function (e) {
    e.preventDefault();
    showNotification("Tindakan salin dinonaktifkan pada situs ini!", "error");
  });

  document.addEventListener("paste", function (e) {
    e.preventDefault();
    showNotification("Tindakan tempel dinonaktifkan pada situs ini!", "error");
  });

  // Contact form validation
  const contactForm = document.getElementById("contactForm");
  if (contactForm) {
    contactForm.addEventListener("submit", function (e) {
      const name = document.getElementById("name").value;
      const email = document.getElementById("email").value;
      const subject = document.getElementById("subject").value;
      const message = document.getElementById("message").value;

      if (!name || !email || !subject || !message) {
        e.preventDefault();
        showNotification("Mohon isi semua kolom!", "error");
      }
    });
  }

  // Initialize Feather Icons
  if (typeof feather !== "undefined") {
    feather.replace();
  }

  // Profile Dropdown Toggle
  const profileLink = document.querySelector(".profile-link");

  if (profileLink) {
    // Close dropdown when clicking outside
    document.addEventListener("click", function (e) {
      const dropdown = document.querySelector(".profile-dropdown");
      const content = document.querySelector(".profile-dropdown-content");

      if (dropdown && content) {
        if (!dropdown.contains(e.target)) {
          content.style.opacity = "0";
          content.style.visibility = "hidden";
          content.style.transform = "translateY(10px)";
        }
      }
    });

    // Toggle dropdown on profile link click
    profileLink.addEventListener("click", function (e) {
      e.preventDefault();
      const content = document.querySelector(".profile-dropdown-content");

      if (content) {
        if (content.style.visibility === "visible") {
          content.style.opacity = "0";
          content.style.visibility = "hidden";
          content.style.transform = "translateY(10px)";
        } else {
          content.style.opacity = "1";
          content.style.visibility = "visible";
          content.style.transform = "translateY(0)";
        }
      }
    });
  }
});

// Initialize animations function
function initAnimations() {
  const animatedElements = document.querySelectorAll(
    ".fade-in, .fade-in-up, .fade-in-down"
  );

  animatedElements.forEach(function (element) {
    element.classList.add("animate");
  });
}

// Notification functions
function showNotification(message, type = "info") {
  const notification = document.getElementById("notification");
  const notificationMessage = document.getElementById("notificationMessage");

  if (!notification || !notificationMessage) return;

  notificationMessage.textContent = message;
  notification.className = "notification";
  notification.classList.add(`notification-${type}`);
  notification.classList.add("show");

  // Auto hide after 5 seconds
  setTimeout(() => {
    closeNotification();
  }, 5000);
}

function closeNotification() {
  const notification = document.getElementById("notification");
  if (!notification) return;

  notification.classList.remove("show");
}
