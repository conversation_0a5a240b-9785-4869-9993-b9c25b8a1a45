<%--
    Document   : getPortfolioDetails
    Created on : Jun 1, 2025, 10:15:30 PM
    Author     : Arqeta
    Description: Get and display portfolio details for modal
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../config/connection.jsp" %>

<%
    // Get the portfolio ID from the request
    String portfolioId = request.getParameter("id");

    if (portfolioId != null && !portfolioId.trim().isEmpty()) {
        try {
            // Get portfolio details from database
            PreparedStatement ps = conn.prepareStatement("SELECT * FROM portfolio WHERE id = ?");
            ps.setString(1, portfolioId);
            ResultSet rs = ps.executeQuery();

            if (rs.next()) {
                String name = rs.getString("name");
                String image = rs.getString("image");
                String description = rs.getString("description");
                String createdAt = rs.getString("created_at");
%>
<div class="portfolio-modal-header">
    <h3><%= name %></h3>
    <p class="portfolio-date">Dipublikasikan: <%= createdAt %></p>
</div>
<div class="portfolio-modal-img">
    <img src="../dist/img/<%= image %>" alt="<%= name %>" />
</div>
<div class="portfolio-modal-description">
    <h4>Deskripsi</h4>
    <p><%= description %></p>
</div>
<%
            } else {
                // Portfolio not found
%>
<div class="portfolio-modal-header">
    <h3>Portfolio tidak ditemukan</h3>
</div>
<div class="portfolio-modal-description">
    <p>Maaf, portfolio yang Anda cari tidak tersedia.</p>
</div>
<%
            }
            rs.close();
            ps.close();
        } catch (Exception e) {
            // Error occurred
%>
<div class="portfolio-modal-header">
    <h3>Terjadi Kesalahan</h3>
</div>
<div class="portfolio-modal-description">
    <p>Maaf, terjadi kesalahan: <%= e.getMessage() %></p>
</div>
<%
        }
    } else {
        // Missing ID
%>
<div class="portfolio-modal-header">
    <h3>Parameter Tidak Valid</h3>
</div>
<div class="portfolio-modal-description">
    <p>Maaf, parameter ID tidak valid.</p>
</div>
<%
    }
%>
