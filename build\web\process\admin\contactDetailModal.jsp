<%--
    Document   : contactDetailModal
    Created on : June 17, 2025
    Author     : Arqeta
    Description: Mengambil detail pesan kontak untuk ditampilkan di modal
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>
<%@page import="java.sql.*" %>
<%@page import="java.text.SimpleDateFormat" %>

<%
    // Set response headers untuk HTML
    response.setContentType("text/html");
    response.setCharacterEncoding("UTF-8");
    
    // Debug logging
    System.out.println("contactDetailModal.jsp called with method: " + request.getMethod());
    System.out.println("Request parameters: " + request.getParameterMap().keySet());
    
    // Cek apakah user adalah admin
    String adminId = (String) session.getAttribute("adminId");
    System.out.println("Admin ID from session: " + adminId);
    
    if (adminId == null) {
        out.print("<div class='error-message'>Akses ditolak. Hanya admin yang dapat mengakses fitur ini.</div>");
        return;
    }
    
    String contactId = request.getParameter("id");
    System.out.println("Contact ID parameter: " + contactId);
    
    if (contactId == null || contactId.trim().isEmpty()) {
        out.print("<div class='error-message'>ID kontak tidak valid.</div>");
        return;
    }
    
    try {
        // Validasi koneksi database
        if (conn == null || conn.isClosed()) {
            out.print("<div class='error-message'>Koneksi database tidak tersedia.</div>");
            return;
        }
        
        // Ambil detail kontak dari database
        PreparedStatement ps = conn.prepareStatement(
            "SELECT id, name, email, subject, message, status, created_at, updated_at FROM contact WHERE id = ?"
        );
        ps.setInt(1, Integer.parseInt(contactId));
        ResultSet rs = ps.executeQuery();
        
        if (rs.next()) {
            String name = rs.getString("name");
            String email = rs.getString("email");
            String subject = rs.getString("subject");
            String message = rs.getString("message");
            String status = rs.getString("status");
            Timestamp createdAt = rs.getTimestamp("created_at");
            Timestamp updatedAt = rs.getTimestamp("updated_at");
            
            // Format tanggal
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd MMMM yyyy, HH:mm");
            String formattedCreatedAt = dateFormat.format(createdAt);
            String formattedUpdatedAt = updatedAt != null ? dateFormat.format(updatedAt) : "-";
            
            // Status dalam bahasa Indonesia
            String statusText = "";
            String statusClass = "";
            switch (status) {
                case "unread":
                    statusText = "Belum Dibaca";
                    statusClass = "status-unread";
                    break;
                case "read":
                    statusText = "Sudah Dibaca";
                    statusClass = "status-read";
                    break;
                case "replied":
                    statusText = "Sudah Dibalas";
                    statusClass = "status-replied";
                    break;
                default:
                    statusText = "Tidak Diketahui";
                    statusClass = "status-unknown";
            }
%>
            <div class="contact-details">
                <div class="contact-header">
                    <h3>Detail Pesan Kontak</h3>
                    <span class="status-badge <%= statusClass %>"><%= statusText %></span>
                </div>
                
                <div class="contact-info">
                    <div class="info-row">
                        <label>ID:</label>
                        <span><%= contactId %></span>
                    </div>
                    <div class="info-row">
                        <label>Nama:</label>
                        <span><%= name != null ? name : "-" %></span>
                    </div>
                    <div class="info-row">
                        <label>Email:</label>
                        <span><%= email != null ? email : "-" %></span>
                    </div>
                    <div class="info-row">
                        <label>Subjek:</label>
                        <span><%= subject != null ? subject : "-" %></span>
                    </div>
                    <div class="info-row">
                        <label>Tanggal Dikirim:</label>
                        <span><%= formattedCreatedAt %></span>
                    </div>
                    <% if (updatedAt != null && !updatedAt.equals(createdAt)) { %>
                    <div class="info-row">
                        <label>Terakhir Diperbarui:</label>
                        <span><%= formattedUpdatedAt %></span>
                    </div>
                    <% } %>
                </div>
                
                <div class="message-content">
                    <label>Pesan:</label>
                    <div class="message-text">
                        <%
                            if (message != null && !message.trim().isEmpty()) {
                                // Clean and format the message for better display
                                String formattedMessage = message.trim()
                                    .replace("\r\n", "\n")  // Normalize Windows line endings
                                    .replace("\r", "\n")    // Handle Mac line endings
                                    .replaceAll("\n{3,}", "\n\n")  // Replace multiple newlines with double
                                    .replace("\n", "<br>");  // Convert to HTML breaks
                                out.print(formattedMessage);
                            } else {
                                out.print("Tidak ada pesan");
                            }
                        %>
                    </div>
                </div>
                
                <div class="contact-actions">
                    <% if ("unread".equals(status)) { %>
                        <button type="button" class="btn-primary" onclick="markAsReadFromModal(<%= contactId %>);">
                            <i data-feather="check"></i> Tandai Sudah Dibaca
                        </button>
                    <% } %>
                    <button type="button" class="btn-secondary" onclick="closeModal('contactMessageModal')">
                        <i data-feather="x"></i> Tutup
                    </button>
                </div>
            </div>
<%
        } else {
            out.print("<div class='error-message'>Pesan kontak tidak ditemukan.</div>");
        }
        
        rs.close();
        ps.close();
        
    } catch (NumberFormatException e) {
        System.out.println("NumberFormatException: " + e.getMessage());
        out.print("<div class='error-message'>Format ID kontak tidak valid.</div>");
    } catch (SQLException e) {
        System.out.println("SQLException: " + e.getMessage());
        out.print("<div class='error-message'>Terjadi kesalahan database: " + e.getMessage() + "</div>");
    } catch (Exception e) {
        System.out.println("Exception: " + e.getMessage());
        out.print("<div class='error-message'>Terjadi kesalahan sistem: " + e.getMessage() + "</div>");
    }
%>
