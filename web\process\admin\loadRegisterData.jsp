<%--
    Document   : loadRegisterData
    Created on : Jun 14, 2025
    Author     : Arqeta
    Description: Load register data (admin/user) via AJAX untuk dashboard admin
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Mendapatkan tipe data yang diminta (admin atau user)
    String type = request.getParameter("type");
    if (type == null || (!type.equals("admin") && !type.equals("user"))) {
        type = "admin";
    }
%>

<div class="table-responsive">
    <table class="data-table" id="registerTable">
        <thead>
            <tr>
                <th onclick="sortTable(0)">ID <i data-feather="arrow-up-down"></i></th>
                <th onclick="sortTable(1)">Nama <i data-feather="arrow-up-down"></i></th>
                <th onclick="sortTable(2)">Username <i data-feather="arrow-up-down"></i></th>
                <th onclick="sortTable(3)">Email <i data-feather="arrow-up-down"></i></th>
                <th onclick="sortTable(4)">Waktu Pembuatan <i data-feather="arrow-up-down"></i></th>
                <th onclick="sortTable(5)">Waktu Perubahan <i data-feather="arrow-up-down"></i></th>
                <th>Aksi</th>
            </tr>
        </thead>
        <tbody id="registerTableBody">
            <%
                try {
                    // Query untuk mengambil data berdasarkan tipe yang dipilih, diurutkan berdasarkan ID
                    String query = "SELECT * FROM " + type + " ORDER BY id ASC";
                    PreparedStatement ps = conn.prepareStatement(query);
                    ResultSet rs = ps.executeQuery();

                    while (rs.next()) {
            %>
            <tr>
                <td><%= rs.getInt("id") %></td>
                <td><%= rs.getString("name") %></td>
                <td><%= rs.getString("username") %></td>
                <td><%= rs.getString("email") %></td>
                <td><%= rs.getTimestamp("created_at") %></td>
                <td><%= rs.getTimestamp("updated_at") %></td>
                <td class="action-buttons">
                    <button class="btn-edit" onclick="openEditAccountModal(<%= rs.getInt("id") %>, '<%= rs.getString("name").replace("'", "\\'") %>', '<%= rs.getString("username") %>', '<%= rs.getString("email") %>', '<%= type %>')" title="Edit">
                        <i data-feather="edit"></i>
                    </button>
                    <button class="btn-delete" onclick="openDeleteModal(<%= rs.getInt("id") %>, '<%= type %>')" title="Hapus">
                        <i data-feather="trash-2"></i>
                    </button>
                </td>
            </tr>
            <%
                    }
                    rs.close();
                    ps.close();
                } catch (SQLException e) {
                    out.println("<tr><td colspan='7'>Error: " + e.getMessage() + "</td></tr>");
                }
            %>
        </tbody>
    </table>
</div>

<script>
// Inisialisasi ikon feather untuk tabel yang baru dimuat
feather.replace();

// Fungsi untuk mencari data dalam tabel
function searchData() {
    const input = document.getElementById('searchInput');
    const filter = input.value.toLowerCase();
    const table = document.getElementById('registerTable');
    const rows = table.getElementsByTagName('tr');

    for (let i = 1; i < rows.length; i++) {
        const cells = rows[i].getElementsByTagName('td');
        let found = false;

        // Cari di kolom nama, username, dan email
        for (let j = 1; j <= 3; j++) {
            if (cells[j] && cells[j].textContent.toLowerCase().includes(filter)) {
                found = true;
                break;
            }
        }

        rows[i].style.display = found ? '' : 'none';
    }
}

// Fungsi untuk mengurutkan tabel
function sortTable(columnIndex) {
    const table = document.getElementById('registerTable');
    const tbody = table.getElementsByTagName('tbody')[0];
    const rows = Array.from(tbody.getElementsByTagName('tr'));

    // Tentukan arah pengurutan
    const isAscending = table.getAttribute('data-sort-direction') !== 'asc';
    table.setAttribute('data-sort-direction', isAscending ? 'asc' : 'desc');

    // Urutkan baris
    rows.sort((a, b) => {
        const aValue = a.getElementsByTagName('td')[columnIndex].textContent.trim();
        const bValue = b.getElementsByTagName('td')[columnIndex].textContent.trim();

        // Penanganan pengurutan numerik untuk kolom ID
        if (columnIndex === 0) {
            return isAscending ? parseInt(aValue) - parseInt(bValue) : parseInt(bValue) - parseInt(aValue);
        }

        // Penanganan pengurutan tanggal untuk kolom timestamp
        if (columnIndex === 4 || columnIndex === 5) {
            const aDate = new Date(aValue);
            const bDate = new Date(bValue);
            return isAscending ? aDate - bDate : bDate - aDate;
        }

        // Pengurutan string
        return isAscending ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    });

    // Tambahkan kembali baris yang sudah diurutkan
    rows.forEach(row => tbody.appendChild(row));

    // Update indikator pengurutan
    updateSortIndicators(columnIndex, isAscending);
}

// Fungsi untuk memperbarui indikator pengurutan
function updateSortIndicators(activeColumn, isAscending) {
    const headers = document.querySelectorAll('#registerTable th');
    headers.forEach((header, index) => {
        const icon = header.querySelector('i');
        if (icon) {
            if (index === activeColumn) {
                icon.setAttribute('data-feather', isAscending ? 'arrow-up' : 'arrow-down');
            } else {
                icon.setAttribute('data-feather', 'arrow-up-down');
            }
        }
    });
    feather.replace();
}
</script>

