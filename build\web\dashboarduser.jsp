<%--
    Document   : dashboarduser
    Created on : Jun 14, 2025
    Author     : Arqeta
    Description: User dashboard page for Arqeta website
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="config/connection.jsp" %>

<%
    // Check if user is logged in
    String userId = (String) session.getAttribute("userId");
    if (userId == null) {
        response.sendRedirect("form/signin.jsp");
        return;
    }

    // Get user details
    String userName = "";
    String userEmail = "";
    boolean isGoogleUser = false;
    String userPhotoUrl = "";
    try {
        // Get user name and email
        PreparedStatement ps = conn.prepareStatement("SELECT name, email FROM user WHERE id = ?");
        ps.setString(1, userId);
        ResultSet rs = ps.executeQuery();

        if (rs.next()) {
            userName = rs.getString("name");
            userEmail = rs.getString("email");
        }
        rs.close();
        ps.close();

        // Check if user has Google account
        PreparedStatement ps2 = conn.prepareStatement("SELECT photo_url FROM user_google WHERE user_id = ?");
        ps2.setString(1, userId);
        ResultSet rs2 = ps2.executeQuery();

        if (rs2.next()) {
            isGoogleUser = true;
            userPhotoUrl = rs2.getString("photo_url");
        }
        rs2.close();
        ps2.close();
    } catch (SQLException e) {
        out.println("Error: " + e.getMessage());
    }

    // Get active page
    String activePage = request.getParameter("page");
    if (activePage == null) {
        activePage = "beranda";
    }
%>

<!DOCTYPE html>
<html lang="id">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="Dashboard User Arqeta">
        <title>Dashboard User | Arqeta</title>

        <link rel="stylesheet" href="dist/css/dashboard.css">
        <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600&display=swap" rel="stylesheet">
        <script src="https://unpkg.com/feather-icons"></script>

        <script>
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                showNotification('Klik kanan dinonaktifkan pada situs ini!', 'error');
            });

            // Prevent inspect element
            document.addEventListener('keydown', function(e) {
                if (e.keyCode === 123 || (e.ctrlKey && e.shiftKey && e.keyCode === 73)) {
                    e.preventDefault();
                    showNotification('Inspeksi elemen dinonaktifkan pada situs ini!', 'error');
                }
            });
        </script>
    </head>
    <body>
        <div class="dashboard-container">
            <div class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <h2>Arqeta</h2>
                    <button id="sidebarToggle" class="sidebar-toggle">
                        <i data-feather="menu"></i>
                    </button>
                </div>

                <div class="sidebar-content">
                    <div class="admin-info">
                        <div class="admin-avatar">
                            <% if (isGoogleUser && userPhotoUrl != null && !userPhotoUrl.isEmpty()) { %>
                                <img src="<%= userPhotoUrl %>" alt="<%= userName %>" onerror="this.src='dist/img/default-avatar.png';">
                            <% } else { %>
                                <i data-feather="user"></i>
                            <% } %>
                        </div>
                        <div class="admin-details">
                            <h3><%= userName %></h3>
                            <p>User</p>
                        </div>
                    </div>

                    <nav class="sidebar-menu">
                        <ul>
                            <li class="<%= activePage.equals("beranda") ? "active" : "" %>">
                                <a href="?page=beranda">
                                    <span class="menu-icon"><i data-feather="home"></i></span>
                                    <span class="menu-text">Beranda</span>
                                </a>
                            </li>
                            <li class="<%= activePage.equals("account") ? "active" : "" %>">
                                <a href="?page=account">
                                    <span class="menu-icon"><i data-feather="user"></i></span>
                                    <span class="menu-text">Akun</span>
                                </a>
                            </li>
                            <li class="<%= activePage.equals("basket") ? "active" : "" %>">
                                <a href="?page=basket">
                                    <span class="menu-icon"><i data-feather="shopping-cart"></i></span>
                                    <span class="menu-text">Keranjang</span>
                                </a>
                            </li>
                            <li class="<%= activePage.equals("history") ? "active" : "" %>">
                                <a href="?page=history">
                                    <span class="menu-icon"><i data-feather="clock"></i></span>
                                    <span class="menu-text">Riwayat Transaksi</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>

                <div class="sidebar-footer">
                    <div class="theme-toggle-wrapper">
                        <span>Mode Gelap</span>
                        <label class="toggle-switch">
                            <input type="checkbox" id="themeSwitchSidebar">
                            <span class="slider round"></span>
                        </label>
                    </div>
                    <a href="home.jsp" class="logout-btn">
                        <i data-feather="home"></i>
                        <span>Kembali ke Beranda</span>
                    </a>
                </div>
            </div>

            <div class="main-content">
                <header class="top-navbar">
                    <div class="navbar-left">
                        <button id="mobileMenuToggle" class="mobile-menu-toggle">
                            <i data-feather="menu"></i>
                        </button>
                        <h2 class="page-title">
                            <%= activePage.equals("beranda") ? "Beranda" :
                               activePage.equals("account") ? "Akun" :
                               activePage.equals("basket") ? "Keranjang" :
                               activePage.equals("history") ? "Riwayat Transaksi" : "Dashboard"
                            %>
                        </h2>
                    </div>
                    <div class="navbar-right">
                        <div id="themeIndicator" class="theme-indicator" title="Mode Tema">
                            <i data-feather="sun" class="feather-sun"></i>
                            <i data-feather="moon" class="feather-moon"></i>
                        </div>
                    </div>
                </header>

                <div class="content">
                    <% if (activePage.equals("beranda")) { %>
                        <div class="dashboard-stats">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i data-feather="shopping-cart"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>
                                        <%
                                            try {
                                                PreparedStatement ps = conn.prepareStatement("SELECT COUNT(*) AS total FROM cart WHERE user_id = ?");
                                                ps.setInt(1, Integer.parseInt(userId));
                                                ResultSet rs = ps.executeQuery();
                                                rs.next();
                                                out.print(rs.getInt("total"));
                                                rs.close();
                                                ps.close();
                                            } catch (SQLException e) {
                                                out.print("0");
                                            }
                                        %>
                                    </h3>
                                    <p>Item di Keranjang</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i data-feather="clock"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>
                                        <%
                                            try {
                                                PreparedStatement ps = conn.prepareStatement("SELECT COUNT(*) AS total FROM transaction WHERE user_id = ?");
                                                ps.setInt(1, Integer.parseInt(userId));
                                                ResultSet rs = ps.executeQuery();
                                                rs.next();
                                                out.print(rs.getInt("total"));
                                                rs.close();
                                                ps.close();
                                            } catch (SQLException e) {
                                                out.print("0");
                                            }
                                        %>
                                    </h3>
                                    <p>Total Transaksi</p>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i data-feather="check-circle"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>
                                        <%
                                            try {
                                                PreparedStatement ps = conn.prepareStatement("SELECT COUNT(*) AS total FROM transaction WHERE user_id = ? AND status = 'completed'");
                                                ps.setInt(1, Integer.parseInt(userId));
                                                ResultSet rs = ps.executeQuery();
                                                rs.next();
                                                out.print(rs.getInt("total"));
                                                rs.close();
                                                ps.close();
                                            } catch (SQLException e) {
                                                out.print("0");
                                            }
                                        %>
                                    </h3>
                                    <p>Transaksi Selesai</p>
                                </div>
                            </div>
                        </div>

                        <div class="dashboard-section">
                            <div class="section-header">
                                <h3>Transaksi Terbaru</h3>
                                <a href="?page=history" class="view-all">Lihat Semua</a>
                            </div>
                            <div class="table-responsive">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Layanan</th>
                                            <th>Jumlah</th>
                                            <th>Total</th>
                                            <th>Status</th>
                                            <th>Tanggal</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <%
                                            try {
                                                PreparedStatement ps = conn.prepareStatement("SELECT * FROM transaction WHERE user_id = ? ORDER BY created_at DESC LIMIT 5");
                                                ps.setInt(1, Integer.parseInt(userId));
                                                ResultSet rs = ps.executeQuery();

                                                while (rs.next()) {
                                        %>
                                        <tr>
                                            <td><%= rs.getString("name") %></td>
                                            <td><%= rs.getInt("unit") %></td>
                                            <td>Rp <%= String.format("%,.0f", rs.getDouble("total")) %></td>
                                            <td>
                                                <span class="status-badge <%= rs.getString("status").equals("pending") ? "status-pending" :
                                                    rs.getString("status").equals("completed") ? "status-completed" : "status-cancelled" %>">
                                                    <%= rs.getString("status").equals("pending") ? "Menunggu" :
                                                        rs.getString("status").equals("completed") ? "Selesai" : "Dibatalkan" %>
                                                </span>
                                            </td>
                                            <td><%= rs.getTimestamp("created_at") %></td>
                                        </tr>
                                        <%
                                                }
                                                rs.close();
                                                ps.close();
                                            } catch (SQLException e) {
                                                out.println("<tr><td colspan='5'>Error: " + e.getMessage() + "</td></tr>");
                                            }
                                        %>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <% } else if (activePage.equals("account")) { %>
                        <jsp:include page="form/user/account.jsp" />
                    <% } else if (activePage.equals("basket")) { %>
                        <jsp:include page="form/user/basket.jsp" />
                    <% } else if (activePage.equals("history")) { %>
                        <jsp:include page="form/user/transactionhistory.jsp" />
                    <% } %>
                </div>
            </div>
        </div>

        <div class="notification" id="notification">
            <div class="notification-content">
                <span class="notification-message" id="notificationMessage"></span>
                <span class="close-notification" onclick="closeNotification()">&times;</span>
            </div>
        </div>

        <button id="backToTopBtn" class="back-to-top">
            <i data-feather="arrow-up"></i>
        </button>

        <script>
            // Feather icons
            document.addEventListener('DOMContentLoaded', function() {
                feather.replace();
                initializeTheme();
            });

            // Toggle sidebar
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');

            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
            });

            mobileMenuToggle.addEventListener('click', function() {
                sidebar.classList.toggle('mobile-active');
            });

            // Initialize sidebar status from localStorage
            if(localStorage.getItem('sidebarCollapsed') === 'true') {
                sidebar.classList.add('collapsed');
            }

            // Theme functionality
            function initializeTheme() {
                const themeSwitchSidebar = document.getElementById('themeSwitchSidebar');
                const themeIndicator = document.getElementById('themeIndicator');
                const body = document.body;
                const isDarkMode = localStorage.getItem('darkMode') === 'true';

                if (isDarkMode) {
                    body.classList.add('dark-mode');
                    themeSwitchSidebar.checked = true;
                    updateThemeIcons(true);
                } else {
                    body.classList.remove('dark-mode');
                    themeSwitchSidebar.checked = false;
                    updateThemeIcons(false);
                }

                themeSwitchSidebar.addEventListener('change', function() {
                    toggleTheme(this.checked);
                });

                themeIndicator.addEventListener('click', function() {
                    const currentDarkMode = body.classList.contains('dark-mode');
                    themeSwitchSidebar.checked = !currentDarkMode;
                    toggleTheme(!currentDarkMode);
                });
            }

            function toggleTheme(isDark) {
                const body = document.body;

                if (isDark) {
                    body.classList.add('dark-mode');
                    localStorage.setItem('darkMode', 'true');
                } else {
                    body.classList.remove('dark-mode');
                    localStorage.setItem('darkMode', 'false');
                }
                updateThemeIcons(isDark);
            }

            function updateThemeIcons(isDark) {
                const themeIndicator = document.getElementById('themeIndicator');
                if (themeIndicator) {
                    const sunIcon = themeIndicator.querySelector('.feather-sun');
                    const moonIcon = themeIndicator.querySelector('.feather-moon');
                    if (isDark) {
                        if (sunIcon) sunIcon.style.display = 'none';
                        if (moonIcon) moonIcon.style.display = 'inline-block';
                    } else {
                        if (sunIcon) sunIcon.style.display = 'inline-block';
                        if (moonIcon) moonIcon.style.display = 'none';
                    }
                }
            }

            // Back to top button
            const backToTopBtn = document.getElementById('backToTopBtn');
            window.addEventListener('scroll', function() {
                if (window.scrollY > 300) {
                    backToTopBtn.classList.add('active');
                } else {
                    backToTopBtn.classList.remove('active');
                }
            });

            if (backToTopBtn) {
                backToTopBtn.addEventListener('click', function() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }

            // Notification functions
            function showNotification(message, type = 'info') {
                const notification = document.getElementById('notification');
                const notificationMessage = document.getElementById('notificationMessage');

                notificationMessage.textContent = message;
                notification.className = 'notification';
                notification.classList.add(`notification-${type}`);
                notification.classList.add('show');

                setTimeout(() => {
                    closeNotification();
                }, 5000);
            }

            function closeNotification() {
                const notification = document.getElementById('notification');
                notification.classList.remove('show');
            }

            // Show notification if there's a message in session
            <%
                String notificationMessage = (String) session.getAttribute("message");
                String notificationType = (String) session.getAttribute("messageType");

                if (notificationMessage != null && !notificationMessage.isEmpty()) {
                    session.removeAttribute("message");
                    session.removeAttribute("messageType");
            %>
                showNotification('<%= notificationMessage.replace("'", "\\'") %>', '<%= notificationType != null ? notificationType : "info" %>');
            <% } %>

            // Show notification if there's a message in URL
            const urlParams = new URLSearchParams(window.location.search);
            const message = urlParams.get('message');
            const status = urlParams.get('status');

            if (message) {
                showNotification(decodeURIComponent(message), status || 'info');
            }
        </script>
    </body>
</html>
