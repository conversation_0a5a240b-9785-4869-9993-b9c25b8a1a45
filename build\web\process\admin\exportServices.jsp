<%--
    Document   : exportServices
    Created on : July 30, 2025
    Author     : Arqeta
    Description: Export services data to Word and JSON formats
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.io.*"%>
<%@page import="java.text.SimpleDateFormat"%>
<%@page import="java.util.Date"%>
<%@page import="java.util.ArrayList"%>
<%@page import="java.util.HashMap"%>
<%@page import="java.util.Map"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Check if user is logged in as admin
    String adminId = (String) session.getAttribute("adminId");
    if (adminId == null) {
        response.sendRedirect("../../form/signin.jsp");
        return;
    }

    String format = request.getParameter("format");
    if (format == null || (!format.equals("word") && !format.equals("json"))) {
        session.setAttribute("notification", "Format ekspor tidak valid!");
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=services");
        return;
    }

    try {
        // Query untuk mengambil semua data layanan
        String query = "SELECT * FROM services ORDER BY id ASC";
        PreparedStatement ps = conn.prepareStatement(query);
        ResultSet rs = ps.executeQuery();

        if (format.equals("word")) {
            // Export to Word (HTML format that can be opened in Word)
            response.setContentType("application/msword");
            response.setHeader("Content-Disposition", "attachment; filename=\"data_layanan_" + 
                new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".doc\"");
            
            PrintWriter writer = response.getWriter();
            
            // Write HTML header for Word document
            writer.println("<html xmlns:o='urn:schemas-microsoft-com:office:office'");
            writer.println("xmlns:w='urn:schemas-microsoft-com:office:word'");
            writer.println("xmlns='http://www.w3.org/TR/REC-html40'>");
            writer.println("<head>");
            writer.println("<meta charset='UTF-8'>");
            writer.println("<meta name='ProgId' content='Word.Document'>");
            writer.println("<meta name='Generator' content='Microsoft Word'>");
            writer.println("<meta name='Originator' content='Microsoft Word'>");
            writer.println("<title>Data Layanan - Arqeta</title>");
            writer.println("<!--[if gte mso 9]>");
            writer.println("<xml>");
            writer.println("<w:WordDocument>");
            writer.println("<w:View>Print</w:View>");
            writer.println("<w:Zoom>90</w:Zoom>");
            writer.println("<w:DoNotPromptForConvert/>");
            writer.println("<w:DoNotShowRevisions/>");
            writer.println("<w:DoNotPrintRevisions/>");
            writer.println("<w:DoNotShowComments/>");
            writer.println("<w:DoNotShowInsertionsAndDeletions/>");
            writer.println("<w:DoNotShowPropertyChanges/>");
            writer.println("</w:WordDocument>");
            writer.println("</xml>");
            writer.println("<![endif]-->");
            writer.println("<style>");
            writer.println("body { font-family: 'Times New Roman', serif; margin: 1in; }");
            writer.println("h1 { color: #333; text-align: center; font-size: 24pt; margin-bottom: 20pt; }");
            writer.println("table { width: 100%; border-collapse: collapse; margin-top: 20pt; }");
            writer.println("th, td { border: 1pt solid #000; padding: 8pt; text-align: left; }");
            writer.println("th { background-color: #f2f2f2; font-weight: bold; }");
            writer.println("tr:nth-child(even) { background-color: #f9f9f9; }");
            writer.println(".export-info { text-align: center; margin-bottom: 20pt; color: #666; font-size: 10pt; }");
            writer.println(".currency { text-align: right; }");
            writer.println(".number { text-align: center; }");
            writer.println("</style>");
            writer.println("</head>");
            writer.println("<body>");
            writer.println("<h1>Data Layanan - Arqeta</h1>");
            writer.println("<div class='export-info'>Diekspor pada: " + 
                new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(new Date()) + "</div>");
            writer.println("<table>");
            writer.println("<thead>");
            writer.println("<tr>");
            writer.println("<th>ID</th>");
            writer.println("<th>Nama Layanan</th>");
            writer.println("<th>Gambar</th>");
            writer.println("<th>Jumlah</th>");
            writer.println("<th>Harga</th>");
            writer.println("<th>Waktu Pembuatan</th>");
            writer.println("<th>Waktu Perubahan</th>");
            writer.println("</tr>");
            writer.println("</thead>");
            writer.println("<tbody>");
            
            // Write data rows
            while (rs.next()) {
                int id = rs.getInt("id");
                String name = rs.getString("name");
                String images = rs.getString("images");
                int quantity = rs.getInt("quantity");
                double price = rs.getDouble("price");
                String createdAt = rs.getString("created_at");
                String updatedAt = rs.getString("updated_at");
                
                writer.println("<tr>");
                writer.println("<td class='number'>" + id + "</td>");
                writer.println("<td>" + (name != null ? name : "Tidak ada nama") + "</td>");
                writer.println("<td>" + (images != null && !images.isEmpty() ? images : "Tidak ada gambar") + "</td>");
                writer.println("<td class='number'>" + quantity + "</td>");
                writer.println("<td class='currency'>Rp " + String.format("%,.0f", price) + "</td>");
                writer.println("<td>" + (createdAt != null ? createdAt : "-") + "</td>");
                writer.println("<td>" + (updatedAt != null ? updatedAt : "-") + "</td>");
                writer.println("</tr>");
            }
            
            writer.println("</tbody>");
            writer.println("</table>");
            writer.println("</body>");
            writer.println("</html>");
            
            writer.flush();
            writer.close();
            
        } else if (format.equals("json")) {
            // Export to JSON
            response.setContentType("application/json");
            response.setHeader("Content-Disposition", "attachment; filename=\"data_layanan_" +
                new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".json\"");

            PrintWriter writer = response.getWriter();

            // First, collect all data in memory to get accurate count
            ArrayList<Map<String, Object>> servicesList = new ArrayList<>();

            while (rs.next()) {
                Map<String, Object> service = new HashMap<>();
                service.put("id", rs.getInt("id"));
                service.put("name", rs.getString("name"));
                service.put("images", rs.getString("images"));
                service.put("quantity", rs.getInt("quantity"));
                service.put("price", rs.getDouble("price"));
                service.put("created_at", rs.getString("created_at"));
                service.put("updated_at", rs.getString("updated_at"));
                servicesList.add(service);
            }

            // Build JSON structure
            writer.println("{");
            writer.println("  \"export_info\": {");
            writer.println("    \"title\": \"Data Layanan - Arqeta\",");
            writer.println("    \"exported_at\": \"" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "\",");
            writer.println("    \"format\": \"JSON\",");
            writer.println("    \"total_records\": " + servicesList.size());
            writer.println("  },");
            writer.println("  \"services\": [");

            // Write data
            for (int i = 0; i < servicesList.size(); i++) {
                Map<String, Object> service = servicesList.get(i);

                if (i > 0) {
                    writer.println(",");
                }

                String name = (String) service.get("name");
                String images = (String) service.get("images");
                String createdAt = (String) service.get("created_at");
                String updatedAt = (String) service.get("updated_at");

                writer.println("    {");
                writer.println("      \"id\": " + service.get("id") + ",");
                writer.println("      \"name\": \"" + (name != null ? name.replace("\"", "\\\"") : "Tidak ada nama") + "\",");
                writer.println("      \"images\": \"" + (images != null ? images.replace("\"", "\\\"") : "") + "\",");
                writer.println("      \"quantity\": " + service.get("quantity") + ",");
                writer.println("      \"price\": " + service.get("price") + ",");
                writer.println("      \"price_formatted\": \"Rp " + String.format("%,.0f", (Double) service.get("price")) + "\",");
                writer.println("      \"created_at\": \"" + (createdAt != null ? createdAt : "") + "\",");
                writer.print("      \"updated_at\": \"" + (updatedAt != null ? updatedAt : "") + "\"");
                writer.print("    }");
            }

            writer.println();
            writer.println("  ]");
            writer.println("}");

            writer.flush();
            writer.close();
        }
        
        rs.close();
        ps.close();
        
    } catch (SQLException e) {
        session.setAttribute("notification", "Error saat mengekspor data: " + e.getMessage());
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=services");
    } catch (Exception e) {
        session.setAttribute("notification", "Error sistem: " + e.getMessage());
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=services");
    }
%>
