<%--
    Document   : exportTransactions
    Created on : July 30, 2025
    Author     : Arqeta
    Description: Export transaction data to Excel and PDF formats
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.io.*"%>
<%@page import="java.text.SimpleDateFormat"%>
<%@page import="java.util.Date"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Check if user is logged in as admin
    String adminId = (String) session.getAttribute("adminId");
    if (adminId == null) {
        response.sendRedirect("../../form/signin.jsp");
        return;
    }

    String format = request.getParameter("format");
    if (format == null || (!format.equals("excel") && !format.equals("pdf"))) {
        session.setAttribute("notification", "Format ekspor tidak valid!");
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=transaction");
        return;
    }

    try {
        // Query untuk mengambil semua data transaksi
        String query = "SELECT t.*, u.name as user_name, u.email as user_email, s.name as service_name "
                     + "FROM transaction t "
                     + "LEFT JOIN user u ON t.user_id = u.id "
                     + "LEFT JOIN services s ON t.service_id = s.id "
                     + "ORDER BY t.id ASC";
        PreparedStatement ps = conn.prepareStatement(query);
        ResultSet rs = ps.executeQuery();

        if (format.equals("excel")) {
            // Export to Excel (CSV format)
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment; filename=\"data_transaksi_" + 
                new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".csv\"");
            
            PrintWriter writer = response.getWriter();
            
            // Write CSV header
            writer.println("ID,Nama Layanan,Pengguna,Email Pengguna,Jumlah,Unit,Total,Status,Tanggal Dibuat");
            
            // Write data rows
            while (rs.next()) {
                int id = rs.getInt("id");
                String serviceName = rs.getString("service_name");
                String userName = rs.getString("user_name");
                String userEmail = rs.getString("user_email");
                double amount = rs.getDouble("amount");
                int unit = rs.getInt("unit");
                double total = rs.getDouble("total");
                String status = rs.getString("status");
                String createdAt = rs.getString("created_at");
                
                // Convert status to Indonesian
                String statusText = "";
                switch(status) {
                    case "menunggu":
                    case "pending":
                        statusText = "Menunggu";
                        break;
                    case "diterima":
                    case "completed":
                        statusText = "Diterima";
                        break;
                    case "ditolak":
                    case "cancelled":
                        statusText = "Ditolak";
                        break;
                    default:
                        statusText = status;
                }
                
                // Escape commas and quotes in CSV
                serviceName = serviceName != null ? "\"" + serviceName.replace("\"", "\"\"") + "\"" : "\"Layanan Tidak Ditemukan\"";
                userName = userName != null ? "\"" + userName.replace("\"", "\"\"") + "\"" : "\"User Tidak Ditemukan\"";
                userEmail = userEmail != null ? "\"" + userEmail.replace("\"", "\"\"") + "\"" : "\"Email Tidak Ditemukan\"";
                
                writer.println(id + "," + serviceName + "," + userName + "," + userEmail + "," + 
                             String.format("%.0f", amount) + "," + unit + "," + 
                             String.format("%.0f", total) + ",\"" + statusText + "\",\"" + createdAt + "\"");
            }
            
            writer.flush();
            writer.close();
            
        } else if (format.equals("pdf")) {
            // Export to PDF (HTML format that can be printed as PDF)
            response.setContentType("text/html");
            response.setHeader("Content-Disposition", "attachment; filename=\"data_transaksi_" + 
                new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".html\"");
            
            PrintWriter writer = response.getWriter();
            
            // Write HTML header
            writer.println("<!DOCTYPE html>");
            writer.println("<html>");
            writer.println("<head>");
            writer.println("<meta charset='UTF-8'>");
            writer.println("<title>Data Transaksi - Arqeta</title>");
            writer.println("<style>");
            writer.println("body { font-family: Arial, sans-serif; margin: 20px; }");
            writer.println("h1 { color: #333; text-align: center; }");
            writer.println("table { width: 100%; border-collapse: collapse; margin-top: 20px; }");
            writer.println("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
            writer.println("th { background-color: #f2f2f2; font-weight: bold; }");
            writer.println("tr:nth-child(even) { background-color: #f9f9f9; }");
            writer.println(".status-pending { color: #ff9800; }");
            writer.println(".status-approved { color: #4caf50; }");
            writer.println(".status-rejected { color: #f44336; }");
            writer.println(".export-info { text-align: center; margin-bottom: 20px; color: #666; }");
            writer.println("@media print { body { margin: 0; } }");
            writer.println("</style>");
            writer.println("</head>");
            writer.println("<body>");
            writer.println("<h1>Data Transaksi - Arqeta</h1>");
            writer.println("<div class='export-info'>Diekspor pada: " + 
                new SimpleDateFormat("dd/MM/yyyy HH:mm:ss").format(new Date()) + "</div>");
            writer.println("<table>");
            writer.println("<thead>");
            writer.println("<tr>");
            writer.println("<th>ID</th>");
            writer.println("<th>Nama Layanan</th>");
            writer.println("<th>Pengguna</th>");
            writer.println("<th>Email</th>");
            writer.println("<th>Jumlah</th>");
            writer.println("<th>Unit</th>");
            writer.println("<th>Total</th>");
            writer.println("<th>Status</th>");
            writer.println("<th>Tanggal</th>");
            writer.println("</tr>");
            writer.println("</thead>");
            writer.println("<tbody>");
            
            // Write data rows
            while (rs.next()) {
                int id = rs.getInt("id");
                String serviceName = rs.getString("service_name");
                String userName = rs.getString("user_name");
                String userEmail = rs.getString("user_email");
                double amount = rs.getDouble("amount");
                int unit = rs.getInt("unit");
                double total = rs.getDouble("total");
                String status = rs.getString("status");
                String createdAt = rs.getString("created_at");
                
                // Convert status to Indonesian and get CSS class
                String statusText = "";
                String statusClass = "";
                switch(status) {
                    case "menunggu":
                    case "pending":
                        statusText = "Menunggu";
                        statusClass = "status-pending";
                        break;
                    case "diterima":
                    case "completed":
                        statusText = "Diterima";
                        statusClass = "status-approved";
                        break;
                    case "ditolak":
                    case "cancelled":
                        statusText = "Ditolak";
                        statusClass = "status-rejected";
                        break;
                    default:
                        statusText = status;
                        statusClass = "status-pending";
                }
                
                writer.println("<tr>");
                writer.println("<td>" + id + "</td>");
                writer.println("<td>" + (serviceName != null ? serviceName : "Layanan Tidak Ditemukan") + "</td>");
                writer.println("<td>" + (userName != null ? userName : "User Tidak Ditemukan") + "</td>");
                writer.println("<td>" + (userEmail != null ? userEmail : "Email Tidak Ditemukan") + "</td>");
                writer.println("<td>Rp " + String.format("%,.0f", amount) + "</td>");
                writer.println("<td>" + unit + "</td>");
                writer.println("<td>Rp " + String.format("%,.0f", total) + "</td>");
                writer.println("<td><span class='" + statusClass + "'>" + statusText + "</span></td>");
                writer.println("<td>" + createdAt + "</td>");
                writer.println("</tr>");
            }
            
            writer.println("</tbody>");
            writer.println("</table>");
            writer.println("<script>window.print();</script>");
            writer.println("</body>");
            writer.println("</html>");
            
            writer.flush();
            writer.close();
        }
        
        rs.close();
        ps.close();
        
    } catch (SQLException e) {
        session.setAttribute("notification", "Error saat mengekspor data: " + e.getMessage());
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=transaction");
    } catch (Exception e) {
        session.setAttribute("notification", "Error sistem: " + e.getMessage());
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=transaction");
    }
%>
