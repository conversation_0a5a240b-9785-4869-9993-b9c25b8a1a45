<%--
  Document   : exportTransactions
  Created on : July 30, 2025
  Author     : Arqeta
  Description: Export transaction data to Excel and PDF formats.
               This version has been refactored for better readability and adherence to JSP standards.
--%>
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*, java.io.*, java.text.SimpleDateFormat, java.util.Date" %>
<%@include file="../../config/connection.jsp" %>
<%
    // --- 1. Security Check & Parameter Validation ---
    
    // Check if the user is logged in as an admin
    if (session.getAttribute("adminId") == null) {
        response.sendRedirect("../../form/signin.jsp");
        return; // Stop further execution
    }

    // Get and validate the export format
    String format = request.getParameter("format");
    if (format == null || (!format.equals("excel") && !format.equals("pdf"))) {
        session.setAttribute("notification", "Format ekspor tidak valid!");
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=transaction");
        return; // Stop further execution
    }

    // --- 2. Data Fetching ---

    String query = "SELECT t.*, u.name as user_name, u.email as user_email, s.name as service_name " +
                   "FROM transaction t " +
                   "LEFT JOIN user u ON t.user_id = u.id " +
                   "LEFT JOIN services s ON t.service_id = s.id " +
                   "ORDER BY t.id ASC";

    PreparedStatement ps = null;
    ResultSet rs = null;

    try {
        ps = conn.prepareStatement(query);
        rs = ps.executeQuery();

        // --- 3. Output Generation ---

        String exportTimestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());

        if ("excel".equals(format)) {
            // --- EXPORT TO EXCEL (CSV) ---
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment; filename=\"data_transaksi_" + exportTimestamp + ".csv\"");

            PrintWriter writer = response.getWriter();
            
            // Write CSV header
            writer.println("ID,Nama Layanan,Pengguna,Email Pengguna,Jumlah,Unit,Total,Status,Tanggal Dibuat");

            // Write data rows
            while (rs.next()) {
                String serviceName = rs.getString("service_name");
                String userName = rs.getString("user_name");
                String userEmail = rs.getString("user_email");
                String status = rs.getString("status");

                // Convert status to Indonesian
                String statusText = "Menunggu"; // Default
                if ("completed".equalsIgnoreCase(status) || "diterima".equalsIgnoreCase(status)) {
                    statusText = "Diterima";
                } else if ("cancelled".equalsIgnoreCase(status) || "ditolak".equalsIgnoreCase(status)) {
                    statusText = "Ditolak";
                }

                // Helper function to escape CSV data
                BiFunction<String, String, String> escapeCsv = (data, fallback) -> 
                    data != null ? "\"" + data.replace("\"", "\"\"") + "\"" : "\"" + fallback + "\"";

                writer.println(
                    rs.getInt("id") + "," +
                    escapeCsv.apply(serviceName, "Layanan Tidak Ditemukan") + "," +
                    escapeCsv.apply(userName, "User Tidak Ditemukan") + "," +
                    escapeCsv.apply(userEmail, "Email Tidak Ditemukan") + "," +
                    String.format("%.0f", rs.getDouble("amount")) + "," +
                    rs.getInt("unit") + "," +
                    String.format("%.0f", rs.getDouble("total")) + "," +
                    "\"" + statusText + "\"," +
                    "\"" + rs.getString("created_at") + "\""
                );
            }
            writer.flush();
            writer.close();

        } else if ("pdf".equals(format)) {
            // --- EXPORT TO PDF (via HTML Print) ---
            response.setContentType("text/html");
            // Although it's HTML, the filename suggests PDF because the user will print it to PDF.
            response.setHeader("Content-Disposition", "attachment; filename=\"data_transaksi_" + exportTimestamp + ".html\"");
%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Data Transaksi - Arqeta</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; margin: 20px; color: #333; }
        h1 { text-align: center; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 12px; }
        th, td { border: 1px solid #ddd; padding: 10px; text-align: left; }
        th { background-color: #f8f8f8; font-weight: 600; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .export-info { text-align: center; margin-bottom: 20px; color: #666; font-size: 12px; }
        .status { font-weight: bold; padding: 4px 8px; border-radius: 4px; color: white; display: inline-block; font-size: 10px; }
        .status-pending { background-color: #ff9800; } /* Orange */
        .status-approved { background-color: #4caf50; } /* Green */
        .status-rejected { background-color: #f44336; } /* Red */
        @media print {
            body { margin: 0; font-size: 10pt; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <h1>Data Transaksi - Arqeta</h1>
    <div class="export-info">
        Diekspor pada: <%= new SimpleDateFormat("dd MMMM yyyy, HH:mm:ss").format(new Date()) %>
    </div>

    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Nama Layanan</th>
                <th>Pengguna</th>
                <th>Email</th>
                <th>Jumlah</th>
                <th>Unit</th>
                <th>Total</th>
                <th>Status</th>
                <th>Tanggal</th>
            </tr>
        </thead>
        <tbody>
            <% while (rs.next()) {
                // Get data for the current row
                String status = rs.getString("status");
                String statusText = "Menunggu";
                String statusClass = "status-pending";

                if ("completed".equalsIgnoreCase(status) || "diterima".equalsIgnoreCase(status)) {
                    statusText = "Diterima";
                    statusClass = "status-approved";
                } else if ("cancelled".equalsIgnoreCase(status) || "ditolak".equalsIgnoreCase(status)) {
                    statusText = "Ditolak";
                    statusClass = "status-rejected";
                }
            %>
            <tr>
                <td><%= rs.getInt("id") %></td>
                <td><%= rs.getString("service_name") != null ? rs.getString("service_name") : "N/A" %></td>
                <td><%= rs.getString("user_name") != null ? rs.getString("user_name") : "N/A" %></td>
                <td><%= rs.getString("user_email") != null ? rs.getString("user_email") : "N/A" %></td>
                <td>Rp <%= String.format("%,.0f", rs.getDouble("amount")) %></td>
                <td><%= rs.getInt("unit") %></td>
                <td>Rp <%= String.format("%,.0f", rs.getDouble("total")) %></td>
                <td><span class="status <%= statusClass %>"><%= statusText %></span></td>
                <td><%= rs.getString("created_at") %></td>
            </tr>
            <% } %>
        </tbody>
    </table>

    <script class="no-print">
        // Automatically trigger the print dialog for the user.
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
<%
        } // End of PDF format block
    } catch (SQLException e) {
        // Log the error for debugging
        e.printStackTrace(); 
        session.setAttribute("notification", "Error database saat mengekspor data: " + e.getMessage());
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=transaction");
    } catch (Exception e) {
        // Log the error for debugging
        e.printStackTrace();
        session.setAttribute("notification", "Terjadi error sistem: " + e.getMessage());
        session.setAttribute("notificationType", "error");
        response.sendRedirect("../../dashboardadmin.jsp?page=transaction");
    } finally {
        // --- 4. Resource Cleanup ---
        if (rs != null) try { rs.close(); } catch (SQLException e) { e.printStackTrace(); }
        if (ps != null) try { ps.close(); } catch (SQLException e) { e.printStackTrace(); }
        // The connection is likely closed by the included file or a filter, but closing it here is safer if not.
        // if (conn != null) try { conn.close(); } catch (SQLException e) { e.printStackTrace(); }
    }
%>