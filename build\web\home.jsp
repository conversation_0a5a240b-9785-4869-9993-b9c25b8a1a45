<%--
    Document   : home
    Created on : May 30, 2025, 1:19:31 AM
    Author     : Arqeta
    Description : Home page for Arqeta website
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="config/connection.jsp" %>

<!DOCTYPE html>
<html lang="id">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="Arqeta - Jasa Pembuatan UI Web dan Mobile">
        <meta name="keywords" content="UI, UX, Web Design, Mobile Design, User Interface">
        <title>Arqeta - Jasa Pembuatan UI Web dan Mobile</title>

        <link rel="stylesheet" href="dist/css/style.css">
        <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600&display=swap" rel="stylesheet">
        <script src="https://unpkg.com/feather-icons"></script>

        <script>
            document.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                // Using custom notification instead of alert()
                showNotification('Klik kanan dinonaktifkan pada situs ini!', 'error');
            });

            // Prevent inspect element
            document.addEventListener('keydown', function(e) {
                if (e.keyCode === 123 || (e.ctrlKey && e.shiftKey && e.keyCode === 73)) {
                    e.preventDefault();
                    // Using custom notification instead of alert()
                    showNotification('Inspeksi elemen dinonaktifkan pada situs ini!', 'error');
                }
            });
        </script>
    </head>
    <body>
        <header class="navbar" id="navbar">
            <div class="container">
                <div class="navbar-logo">
                    <a href="#" class="logo">Arqeta</a>
                </div>

                <nav class="navbar-menu">
                    <ul>
                        <li><a href="#about">Tentang Kami</a></li>
                        <li class="dropdown">
                            <a href="#services">Layanan <i data-feather="chevron-down"></i></a>
                            <div class="dropdown-content">
                                <a href="#services">Layanan Pembuatan UI Web</a>
                                <a href="#services">Layanan Pembuatan UI Mobile</a>
                            </div>
                        </li>
                        <li><a href="#portfolio">Portfolio</a></li>
                        <li><a href="#blog">Blog</a></li>
                        <li><a href="#partnership">Partnership</a></li>
                        <li><a href="#contact">Kontak</a></li>
                    </ul>
                </nav>

                <div class="navbar-actions">
                    <% if (session.getAttribute("userId") != null) {
                        String userName = (String) session.getAttribute("name");
                    %>
                        <div class="profile-dropdown">
                            <a href="#" class="profile-link">
                                <i data-feather="user"></i>
                                <span><%= userName %></span>
                                <i data-feather="chevron-down"></i>
                            </a>
                            <div class="profile-dropdown-content">
                                <a href="dashboarduser.jsp">Dashboard</a>
                                <a href="process/logoutProcess.jsp">Logout</a>
                            </div>
                        </div>
                    <% } else { %>
                        <a href="form/signin.jsp" class="btn-login">Login</a>
                    <% } %>
                    <button class="theme-toggle" id="themeToggle">
                        <i data-feather="moon" id="themeIcon"></i>
                    </button>
                    <button class="menu-toggle" id="menuToggle">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </header>

        <div class="mobile-menu" id="mobileMenu">
            <div class="mobile-menu-content">
                <ul>
                    <li><a href="#about">Tentang Kami</a></li>
                    <li><a href="#services">Layanan Pembuatan UI Web</a></li>
                    <li><a href="#services">Layanan Pembuatan UI Mobile</a></li>
                    <li><a href="#portfolio">Portfolio</a></li>
                    <li><a href="#blog">Blog</a></li>
                    <li><a href="#partnership">Partnership</a></li>
                    <li><a href="#contact">Kontak</a></li>
                    <% if (session.getAttribute("userId") != null) { %>
                        <li><a href="form/usersetting.jsp">Pengaturan</a></li>
                        <li><a href="process/logoutProcess.jsp">Logout</a></li>
                    <% } else { %>
                        <li><a href="form/signin.jsp">Login</a></li>
                    <% } %>
                </ul>
                <div class="theme-toggle-mobile">
                    <span>Mode Gelap</span>
                    <label class="toggle-switch">
                        <input type="checkbox" id="themeSwitchMobile">
                        <span class="slider round"></span>
                    </label>
                </div>
            </div>
        </div>

        <section class="hero-section" id="hero">
            <div class="container">
                <div class="hero-content">
                    <h1>Buat UI Web & Mobile Terbaik untuk Bisnis Anda</h1>
                    <p>Kami hadir untuk membantu Anda menciptakan pengalaman digital yang luar biasa dengan desain UI yang modern, responsif, dan menarik.</p>
                    <% if (session.getAttribute("userId") != null) { %>
                        <a href="#services" class="btn-primary">Lihat Layanan</a>
                    <% } else { %>
                        <a href="form/signin.jsp" class="btn-primary">Mulai Sekarang</a>
                    <% } %>
                </div>
            </div>
            <div class="hero-bg"></div>
        </section>

        <section class="about-section" id="about">
            <div class="container">
                <div class="section-title">
                    <h2>Tentang Kami</h2>
                </div>
                <div class="about-content">
                    <div class="about-info">
                        <h3>Siapa Kami?</h3>
                        <p>Arqeta adalah tim desainer UI/UX profesional yang fokus pada pembuatan antarmuka pengguna yang estetik, fungsional, dan intuitif untuk web dan aplikasi mobile. Kami menggabungkan keahlian teknis dan desain untuk memberikan solusi digital terbaik bagi klien kami.</p>
                    </div>
                    <div class="about-social">
                        <h3>Ingin Mengenal Kami Lebih Jauh?</h3>
                        <p>Kunjungi sosial media kami</p>
                        <div class="social-icons">
                            <a href="#" class="social-icon">
                                <img src="dist/svg/instagram.svg" alt="Instagram">
                            </a>
                            <a href="#" class="social-icon">
                                <img src="dist/svg/linkedin.svg" alt="LinkedIn">
                            </a>
                            <a href="#" class="social-icon">
                                <img src="dist/svg/x.svg" alt="X (Twitter)">
                            </a>
                            <a href="#" class="social-icon">
                                <img src="dist/svg/medium.svg" alt="Medium">
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="services-section" id="services">
            <div class="container">
                <div class="section-title">
                    <h2>Layanan Kami</h2>
                    <p>Kami menyediakan berbagai layanan UI/UX berkualitas tinggi</p>
                </div>
                <div class="services-container">
                    <%
                        try {
                            java.sql.PreparedStatement ps = conn.prepareStatement("SELECT * FROM services");
                            java.sql.ResultSet rs = ps.executeQuery();

                            while (rs.next()) {
                    %>
                        <div class="service-card">
                            <div class="service-img">
                                <img src="dist/img/<%= rs.getString("images") %>" alt="<%= rs.getString("name") %>">
                            </div>
                            <div class="service-info">
                                <h3><%= rs.getString("name") %></h3>
                                <p>Kuota: <%= rs.getInt("quantity") %></p>
                                <p class="service-price">Rp <%= String.format("%,.0f", rs.getDouble("price")) %></p>
                                <div class="service-actions">
                                    <button class="btn-buy" onclick="handleBuyClick(<%= rs.getInt("id") %>, '<%= rs.getString("name") %>', <%= rs.getDouble("price") %>)">
                                        <i data-feather="credit-card"></i> Beli
                                    </button>
                                    <button class="btn-cart" onclick="handleCartClick(<%= rs.getInt("id") %>, '<%= rs.getString("name") %>', <%= rs.getDouble("price") %>)">
                                        <i data-feather="shopping-cart"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    <%
                            }
                            rs.close();
                            ps.close();
                        } catch (Exception e) {
                            out.println("Error: " + e.getMessage());
                        }
                    %>
                </div>
            </div>
        </section>

        <section class="portfolio-section" id="portfolio">
            <div class="container">
                <div class="section-title">
                    <h2>Portfolio</h2>
                    <p>Hasil karya terbaik dari tim kami</p>
                </div>
                <div class="portfolio-container">
                    <%
                        try {
                            java.sql.PreparedStatement ps = conn.prepareStatement("SELECT * FROM portfolio");
                            java.sql.ResultSet rs = ps.executeQuery();

                            while (rs.next()) {
                    %>
                        <div class="portfolio-card" data-id="<%= rs.getInt("id") %>">
                            <div class="portfolio-img">
                                <img src="dist/img/<%= rs.getString("image") %>" alt="<%= rs.getString("name") %>">
                                <div class="portfolio-overlay">
                                    <button class="btn-preview" data-id="<%= rs.getInt("id") %>">Preview</button>
                                </div>
                            </div>
                            <div class="portfolio-info">
                                <h3><%= rs.getString("name") %></h3>
                            </div>
                        </div>
                    <%
                            }
                            rs.close();
                            ps.close();
                        } catch (Exception e) {
                            out.println("Error: " + e.getMessage());
                        }
                    %>
                </div>

                <div id="portfolioModal" class="modal">
                    <div class="modal-content">
                        <span class="close-modal">&times;</span>
                        <div id="portfolioModalContent">
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Transaction Modal -->
        <div id="transactionModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Beli Layanan</h3>
                    <span class="close-modal" onclick="closeTransactionModal()">&times;</span>
                </div>
                <form id="transactionForm" action="process/user/transactionProcess.jsp" method="POST">
                    <input type="hidden" id="serviceId" name="serviceId">
                    <div class="form-group">
                        <label for="serviceName">Layanan</label>
                        <input type="text" id="serviceName" name="serviceName" readonly>
                    </div>
                    <div class="form-group">
                        <label for="servicePrice">Harga per Unit</label>
                        <input type="text" id="servicePrice" name="servicePrice" readonly>
                    </div>
                    <div class="form-group">
                        <label for="quantity">Jumlah</label>
                        <input type="number" id="quantity" name="quantity" min="1" value="1" onchange="calculateTotal()">
                    </div>
                    <div class="form-group">
                        <label for="totalPrice">Total Harga</label>
                        <input type="text" id="totalPrice" name="totalPrice" readonly>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn-secondary" onclick="closeTransactionModal()">Batal</button>
                        <button type="submit" class="btn-primary">Beli Sekarang</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Blog Modal -->
        <div id="blogModal" class="modal">
            <div class="modal-content">
                <span class="close-modal" onclick="closeBlogModal()">&times;</span>
                <div id="blogModalContent">
                </div>
            </div>
        </div>
            </div>
        </section>

        <section class="blog-section" id="blog">
            <div class="container">
                <div class="section-title">
                    <h2>Blog</h2>
                    <p>Insight dan tips seputar UI/UX</p>
                </div>
                <div class="blog-container">
                    <%
                        try {
                            java.sql.PreparedStatement ps = conn.prepareStatement("SELECT * FROM blog ORDER BY created_at DESC LIMIT 3");
                            java.sql.ResultSet rs = ps.executeQuery();

                            while (rs.next()) {
                                // Extract a portion of content for preview
                                String content = rs.getString("content");
                                String previewContent = content.length() > 150 ? content.substring(0, 150) + "..." : content;
                    %>
                        <div class="blog-card">
                            <div class="blog-img">
                                <img src="dist/img/<%= rs.getString("image") %>" alt="<%= rs.getString("title") %>">
                            </div>
                            <div class="blog-info">
                                <h3><%= rs.getString("title") %></h3>
                                <p><%= previewContent %></p>
                                <div class="blog-meta">
                                    <div class="blog-date"><%= rs.getTimestamp("created_at") %></div>
                                    <a href="#" class="read-more" onclick="showBlogDetails(<%= rs.getInt("id") %>)">Baca Selengkapnya</a>
                                </div>
                            </div>
                        </div>
                    <%
                            }
                            rs.close();
                            ps.close();
                        } catch (Exception e) {
                            out.println("Error: " + e.getMessage());
                        }
                    %>
                </div>
            </div>
        </section>

        <section class="partnership-section" id="partnership">
            <div class="container">
                <div class="section-title">
                    <h2>Partnership</h2>
                </div>
                <div class="partnership-container">
                    <div class="partner">
                        <img src="dist/svg/anthropic.svg" alt="Anthropic" class="partner-logo">
                    </div>
                    <div class="partner">
                        <img src="dist/svg/figma.svg" alt="Figma" class="partner-logo">
                    </div>
                    <div class="partner">
                        <img src="dist/svg/google.svg" alt="Google" class="partner-logo">
                    </div>
                </div>
            </div>
        </section>

        <section class="contact-section" id="contact">
            <div class="container">
                <div class="section-title">
                    <h2>Kontak</h2>
                    <p>Hubungi kami untuk konsultasi</p>
                </div>
                <div class="contact-container">
                    <div class="contact-form">
                        <form id="contactForm" action="process/contactProcessTest.jsp" method="POST">
                            <div class="form-group">
                                <label for="name">Nama</label>
                                <input type="text" id="name" name="name" required>
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                            <div class="form-group">
                                <label for="subject">Subjek</label>
                                <input type="text" id="subject" name="subject" required>
                            </div>
                            <div class="form-group">
                                <label for="message">Pesan</label>
                                <textarea id="message" name="message" rows="5" required></textarea>
                            </div>
                            <button type="submit" class="btn-submit">Kirim Pesan</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <footer class="footer">
            <div class="container">
                <div class="footer-content">
                    <div class="footer-logo">
                        <h3>Arqeta</h3>
                        <p><EMAIL></p>
                    </div>
                    <div class="footer-links">
                        <h4>Layanan</h4>
                        <ul>
                            <li><a href="#services">Layanan Pembuatan UI Web</a></li>
                            <li><a href="#services">Layanan Pembuatan UI Mobile</a></li>
                        </ul>
                    </div>
                    <div class="footer-links">
                        <h4>Cari Apa?</h4>
                        <ul>
                            <li><a href="#about">Tentang Kami</a></li>
                            <li><a href="#services">Layanan</a></li>
                            <li><a href="#portfolio">Portfolio</a></li>
                            <li><a href="#blog">Blog</a></li>
                            <li><a href="#partnership">Partnership</a></li>
                            <li><a href="#contact">Kontak</a></li>
                        </ul>
                    </div>
                    <div class="footer-social">
                        <h4>Ikuti Kami</h4>
                        <div class="social-icons">
                            <a href="#" class="social-icon">
                                <img src="dist/svg/instagram.svg" alt="Instagram">
                            </a>
                            <a href="#" class="social-icon">
                                <img src="dist/svg/linkedin.svg" alt="LinkedIn">
                            </a>
                            <a href="#" class="social-icon">
                                <img src="dist/svg/x.svg" alt="X (Twitter)">
                            </a>
                            <a href="#" class="social-icon">
                                <img src="dist/svg/medium.svg" alt="Medium">
                            </a>
                        </div>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>&copy; <%= new java.util.Date().getYear() + 1900 %> Arqeta. Hak Cipta Dilindungi.</p>
                </div>
            </div>
        </footer>

        <button id="backToTopBtn" class="back-to-top">
            <i data-feather="arrow-up"></i>
        </button>

        <div class="notification" id="notification">
            <div class="notification-content">
                <span class="notification-message" id="notificationMessage"></span>
                <span class="close-notification" onclick="closeNotification()">&times;</span>
            </div>
        </div>

        <script src="dist/js/main.js"></script>
        <script>
            // Initialize feather icons first
            feather.replace();

            // Check if user is logged in
            const isLoggedIn = <%= session.getAttribute("userId") != null ? "true" : "false" %>;

            // Check for session messages
            window.onload = function() {
                // Re-initialize feather icons after page load
                feather.replace();

                <% if (session.getAttribute("message") != null) { %>
                    showNotification("<%= session.getAttribute("message") %>", "<%= session.getAttribute("messageType") %>");
                    <%
                    // Clear the message after displaying
                    session.removeAttribute("message");
                    session.removeAttribute("messageType");
                    %>
                <% } %>
            };

            // Handle buy button click
            function handleBuyClick(serviceId, serviceName, servicePrice) {
                if (!isLoggedIn) {
                    window.location.href = 'form/signin.jsp';
                    return;
                }

                // Open transaction modal
                document.getElementById('serviceId').value = serviceId;
                document.getElementById('serviceName').value = serviceName;
                document.getElementById('servicePrice').value = 'Rp ' + new Intl.NumberFormat('id-ID').format(servicePrice);
                document.getElementById('quantity').value = 1;
                calculateTotal();
                document.getElementById('transactionModal').style.display = 'block';
            }

            // Handle cart button click
            function handleCartClick(serviceId, serviceName, servicePrice) {
                if (!isLoggedIn) {
                    window.location.href = 'form/signin.jsp';
                    return;
                }

                // Prepare form data (using same approach as basket.jsp)
                const requestBody = `serviceId=${serviceId}&quantity=1`;

                // Add to cart via fetch API
                fetch('process/user/addToCartNew.jsp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: requestBody
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);
                    console.log('Response ok:', response.ok);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
                    }
                    return response.text();
                })
                .then(text => {
                    console.log('Response text:', text);

                    // Check if response is empty
                    if (!text || text.trim() === '') {
                        throw new Error('Server returned empty response');
                    }

                    try {
                        const data = JSON.parse(text);
                        if (data.success) {
                            showNotification(data.message, 'success');
                        } else {
                            showNotification(data.message, 'error');
                        }
                    } catch (parseError) {
                        console.error('JSON parse error:', parseError);
                        console.error('Response text:', text);
                        showNotification('Terjadi kesalahan dalam memproses respons server: ' + parseError.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Fetch error:', error);
                    console.error('Error details:', {
                        message: error.message,
                        stack: error.stack
                    });
                    showNotification('Terjadi kesalahan saat menambahkan ke keranjang: ' + error.message, 'error');
                });
            }

            // Calculate total price
            function calculateTotal() {
                const priceText = document.getElementById('servicePrice').value;
                const price = parseFloat(priceText.replace(/[^\d]/g, ''));
                const quantity = parseInt(document.getElementById('quantity').value) || 1;
                const total = price * quantity;

                document.getElementById('totalPrice').value = 'Rp ' + new Intl.NumberFormat('id-ID').format(total);
            }

            // Close transaction modal
            function closeTransactionModal() {
                document.getElementById('transactionModal').style.display = 'none';
            }

            // Show blog details
            function showBlogDetails(blogId) {
                const xhr = new XMLHttpRequest();
                xhr.open('GET', 'process/getBlogDetails.jsp?id=' + blogId, true);

                xhr.onload = function() {
                    if (this.status === 200) {
                        document.getElementById('blogModalContent').innerHTML = this.responseText;
                        document.getElementById('blogModal').style.display = 'block';
                    }
                };

                xhr.send();
            }

            // Close blog modal
            function closeBlogModal() {
                document.getElementById('blogModal').style.display = 'none';
            }

            // Notification functions are already defined in main.js

            // Close modals when clicking outside
            window.onclick = function(event) {
                const transactionModal = document.getElementById('transactionModal');
                const blogModal = document.getElementById('blogModal');

                if (event.target === transactionModal) {
                    closeTransactionModal();
                }
                if (event.target === blogModal) {
                    closeBlogModal();
                }
            }
        </script>
    </body>
</html>
