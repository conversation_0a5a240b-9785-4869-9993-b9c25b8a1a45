<%--
    Document   : signin
    Created on : Jun 1, 2025, 3:28:10 PM
    Author     : Arqeta
    Description: Login page for Arqeta website
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../config/connection.jsp" %>

<!DOCTYPE html>
<html lang="id">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="description" content="Login ke Arqeta" />
        <title>Login | Arqeta</title>

        <link rel="stylesheet" href="../dist/css/auth.css" />
        <link
            href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600&display=swap"
            rel="stylesheet"
        />
        <script src="https://unpkg.com/feather-icons"></script>

        <script>
            document.addEventListener("contextmenu", function (e) {
                e.preventDefault();
                // Using custom notification instead of alert()
                showNotification("Klik kanan dinonaktifkan pada situs ini!", "error");
            });

            // Prevent inspect element
            document.addEventListener("keydown", function (e) {
                if (
                    e.keyCode === 123 ||
                    (e.ctrlKey && e.shiftKey && e.keyCode === 73)
                ) {
                    e.preventDefault();
                    // Using custom notification instead of alert()
                    showNotification(
                        "Inspeksi elemen dinonaktifkan pada situs ini!",
                        "error"
                    );
                }
            });
        </script>
    </head>
    <body>
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <h1>Login</h1>
                    <p>Masuk untuk mengakses akun Anda</p>
                </div>

                <form
                    class="auth-form"
                    id="loginForm"
                    action="../process/loginProcessNew.jsp"
                    method="POST"
                >
                    <div class="form-group">
                        <label for="username">Nama atau Username</label>
                        <input
                            type="text"
                            id="username"
                            name="username"
                            placeholder="Masukkan nama atau username"
                            required
                        />
                    </div>

                    <div class="form-group">
                        <label for="email">Email</label>
                        <input
                            type="email"
                            id="email"
                            name="email"
                            placeholder="Masukkan email"
                            required
                        />
                    </div>

                    <div class="form-group">
                        <label for="password">Kata Sandi</label>
                        <div class="password-input">
                            <input
                                type="password"
                                id="password"
                                name="password"
                                placeholder="Masukkan kata sandi"
                                required
                            />
                            <span class="password-toggle" onclick="togglePassword()">
                                <i id="passwordIcon" data-feather="eye-off"></i>
                            </span>
                        </div>
                    </div>

                    <div class="form-action">
                        <a href="forgotpassword.jsp" class="forgot-link"
                            >Lupa kata sandi?</a
                        >
                    </div>

                    <button type="submit" class="btn-auth">Login</button>

                    <div class="auth-divider">
                        <span>atau</span>
                    </div>

                    <a href="loginwithgoogle.jsp" class="btn-google">
                        <img src="../dist/svg/googleicon.svg" alt="Google" />
                        <span>Login dengan Google</span>
                    </a>

                    <div class="auth-footer">
                        <p>Belum memiliki akun? <a href="signup.jsp">Daftar</a></p>
                    </div>
                </form>
            </div>
        </div>

        <div class="notification" id="notification">
            <div class="notification-content">
                <span class="notification-message" id="notificationMessage"></span>
                <span class="close-notification" onclick="closeNotification()"
                    >&times;</span
                >
            </div>
        </div>

        <script>
            // Initialize feather icons
            feather.replace();

            // Toggle password visibility
            function togglePassword() {
                const passwordInput = document.getElementById("password");
                const passwordIcon = document.getElementById("passwordIcon");

                if (passwordInput.type === "password") {
                    passwordInput.type = "text";
                    passwordIcon.setAttribute("data-feather", "eye");
                } else {
                    passwordInput.type = "password";
                    passwordIcon.setAttribute("data-feather", "eye-off");
                }

                feather.replace();
            }

            // Clear form on page refresh
            window.onload = function () {
                document.getElementById("loginForm").reset();

                // Check for session message
                <% if (session.getAttribute("message") != null) { %>
                    showNotification("<%= session.getAttribute("message") %>", "<%= session.getAttribute("messageType") %>");
                    <%
                    // Clear the message after displaying
                    session.removeAttribute("message");
                    session.removeAttribute("messageType");
                    %>
                <% } %>
            };

            // Notification functions (copied from forgotpassword.jsp for consistency)
            function showNotification(message, type = "info") {
                const notification = document.getElementById("notification");
                const notificationMessage = document.getElementById(
                    "notificationMessage"
                );

                notificationMessage.textContent = message;
                notification.className = "notification";
                notification.classList.add(`notification-${type}`);
                notification.classList.add("show");

                // Auto hide after 10 seconds
                setTimeout(() => {
                    closeNotification();
                }, 10000);
            }

            function closeNotification() {
                const notification = document.getElementById("notification");
                notification.classList.remove("show");
            }

            // Prevent copy paste
            document.addEventListener("copy", function (e) {
                e.preventDefault();
                showNotification("Maaf, tindakan salin telah dinonaktifkan di situs ini.", "error");
            });

            document.addEventListener("paste", function (e) {
                e.preventDefault();
                showNotification("Maaf, tindakan tempel telah dinonaktifkan di situs ini.", "error");
            });
        </script>
    </body>
</html>
