-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jun 12, 2025 at 04:34 AM
-- Server version: 8.0.30
-- PHP Version: 8.1.10

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `arqeta`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin`
--

CREATE TABLE `admin` (
  `id` int NOT NULL,
  `name` varchar(50) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `admin`
--

INSERT INTO `admin` (`id`, `name`, `username`, `email`, `password`, `created_at`, `updated_at`) VALUES
(1, 'Arqeta', 'arqeta', '<EMAIL>', '729512449c2b0ccd9d5dcfc864a062c76f2f2269fee442563467c8607e2f52ab', '2025-05-31 15:27:35', '2025-06-02 13:32:59'),
(2, 'Muhammad Syaamil Muzhaffar', 'musyaml', '<EMAIL>', '729512449c2b0ccd9d5dcfc864a062c76f2f2269fee442563467c8607e2f52ab', '2025-06-02 18:14:22', '2025-06-03 02:17:53');

-- --------------------------------------------------------

--
-- Table structure for table `admin_google`
--

CREATE TABLE `admin_google` (
  `id` int NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `admin_id` varchar(100) NOT NULL,
  `photo_url` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `admin_google`
--

INSERT INTO `admin_google` (`id`, `name`, `email`, `admin_id`, `photo_url`, `created_at`, `last_login`, `updated_at`) VALUES
(2, 'Muhammad Syaamil Muzhaffar', '<EMAIL>', '2', 'https://lh3.googleusercontent.com/a/ACg8ocL4_W2Lr0C7Hf1-y9asRk3H1DX5bALSuirpNC344A_F9aniqtA=s96-c', '2025-06-03 15:47:59', '2025-06-10 08:57:08', '2025-06-10 08:57:08');

-- --------------------------------------------------------

--
-- Table structure for table `blog`
--

CREATE TABLE `blog` (
  `id` int NOT NULL,
  `title` varchar(255) NOT NULL,
  `image` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `blog`
--

INSERT INTO `blog` (`id`, `title`, `image`, `content`, `created_at`, `updated_at`) VALUES
(1, 'Tren Desain UI Web 2025', 'blog-1.jpg', 'Pada tahun 2025, tren desain UI web semakin berkembang dengan fokus pada pengalaman pengguna yang lebih personal dan imersif. Beberapa tren yang menonjol antara lain: penggunaan AI untuk personalisasi konten, desain 3D yang lebih realistis, dark mode sebagai pilihan default, dan animasi mikro yang lebih halus. Selain itu, penggunaan warna gradien dan tipografi yang berani juga semakin populer. Desainer UI perlu terus mengikuti perkembangan ini untuk menciptakan antarmuka yang modern dan menarik bagi pengguna.', '2025-05-26 08:00:00', '2025-05-26 08:00:00'),
(2, 'Tips Meningkatkan UX pada Mobile App', 'blog-2.jpg', 'Meningkatkan UX pada aplikasi mobile adalah kunci untuk mendapatkan pengguna loyal. Beberapa tips yang bisa diterapkan: pastikan onboarding yang simpel dan jelas, gunakan gestur yang intuitif, prioritaskan kecepatan loading, terapkan desain yang konsisten, dan berikan feedback yang jelas pada setiap interaksi. Jangan lupa untuk melakukan testing dengan pengguna nyata untuk mendapatkan masukan yang berharga. Dengan memperhatikan aspek-aspek ini, aplikasi mobile Anda akan memberikan pengalaman yang lebih baik bagi pengguna.', '2025-05-26 09:30:00', '2025-05-26 09:30:00'),
(3, 'Pentingnya Aksesibilitas dalam Desain UI', 'blog-3.jpg', 'Aksesibilitas adalah aspek penting dalam desain UI yang sering terabaikan. Desain yang aksesibel memastikan semua pengguna, termasuk mereka dengan keterbatasan, dapat menggunakan produk Anda dengan baik. Beberapa praktik terbaik meliputi: penggunaan kontras warna yang cukup, teks alternatif untuk gambar, navigasi keyboard yang baik, dan struktur konten yang jelas. Dengan menerapkan prinsip aksesibilitas, tidak hanya memperluas jangkauan pengguna tetapi juga meningkatkan kualitas UI secara keseluruhan untuk semua orang.', '2025-05-26 10:45:00', '2025-05-26 10:45:00');

-- --------------------------------------------------------

--
-- Table structure for table `contact`
--

CREATE TABLE `contact` (
  `id` int NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `status` enum('unread','read','replied') NOT NULL DEFAULT 'unread',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `otp_log`
--

CREATE TABLE `otp_log` (
  `id` int NOT NULL,
  `email` varchar(255) NOT NULL,
  `otp` varchar(6) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `used` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Table structure for table `portfolio`
--

CREATE TABLE `portfolio` (
  `id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `image` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `portfolio`
--

INSERT INTO `portfolio` (`id`, `name`, `image`, `description`, `created_at`, `updated_at`) VALUES
(1, 'Dashboard Analytics', 'portfolio-1.jpg', 'UI Dashboard Analytics untuk sebuah perusahaan teknologi dengan visualisasi data yang komprehensif dan mudah dipahami.', '2025-05-25 08:30:00', '2025-05-25 08:30:00'),
(2, 'Mobile Banking App', 'portfolio-2.jpg', 'Desain UI aplikasi mobile banking dengan fokus pada kemudahan penggunaan dan keamanan transaksi.', '2025-05-25 09:15:00', '2025-05-25 09:15:00'),
(3, 'E-commerce Website', 'portfolio-3.jpg', 'UI Website e-commerce dengan pengalaman belanja yang menyenangkan dan proses checkout yang efisien.', '2025-05-25 10:00:00', '2025-05-25 10:00:00'),
(4, 'Travel App', 'portfolio-4.jpg', 'Aplikasi travel dengan fitur pencarian destinasi, booking hotel, dan pemesanan tiket dalam satu platform.', '2025-05-25 10:45:00', '2025-05-25 10:45:00'),
(5, 'Health Monitoring App', 'portfolio-5.jpg', 'Aplikasi monitoring kesehatan dengan tampilan yang intuitif dan visualisasi data kesehatan yang mudah dipahami.', '2025-05-25 11:30:00', '2025-05-25 11:30:00'),
(6, 'Learning Management System', 'portfolio-6.jpg', 'UI untuk platform pembelajaran online dengan fitur kursus interaktif, quiz, dan tracking progress belajar.', '2025-05-25 12:15:00', '2025-05-25 12:15:00');

-- --------------------------------------------------------

--
-- Table structure for table `services`
--

CREATE TABLE `services` (
  `id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `images` varchar(255) NOT NULL,
  `quantity` int NOT NULL,
  `price` decimal(15,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `services`
--

INSERT INTO `services` (`id`, `name`, `images`, `quantity`, `price`, `created_at`, `updated_at`) VALUES
(1, 'Basic Web UI Design', 'web-basic.jpg', 5, 2000000.00, '2025-05-24 09:00:00', '2025-05-24 09:00:00'),
(2, 'Standard Web UI Design', 'web-standard.jpg', 10, 3500000.00, '2025-05-24 09:30:00', '2025-05-24 09:30:00'),
(3, 'Premium Web UI Design', 'web-premium.jpg', 15, 5000000.00, '2025-05-24 10:00:00', '2025-05-24 10:00:00'),
(4, 'Basic Mobile UI Design', 'mobile-basic.jpg', 5, 2500000.00, '2025-05-24 10:30:00', '2025-05-24 10:30:00'),
(5, 'Standard Mobile UI Design', 'mobile-standard.jpg', 10, 4000000.00, '2025-05-24 11:00:00', '2025-05-24 11:00:00'),
(6, 'Premium Mobile UI Design', 'mobile-premium.jpg', 15, 5500000.00, '2025-05-24 11:30:00', '2025-05-24 11:30:00');

-- --------------------------------------------------------

--
-- Table structure for table `transaction`
--

CREATE TABLE `transaction` (
  `id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `unit` int NOT NULL,
  `total` decimal(15,2) NOT NULL,
  `user_id` int NOT NULL,
  `service_id` int NOT NULL,
  `status` enum('pending','completed','cancelled') NOT NULL DEFAULT 'pending',
  `price` decimal(15,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `transaction`
--

INSERT INTO `transaction` (`id`, `name`, `amount`, `unit`, `total`, `user_id`, `service_id`, `status`, `price`, `created_at`, `updated_at`) VALUES
(1, 'Basic Web UI Design', 2000000.00, 2, 4000000.00, 5, 1, 'completed', 2000000.00, '2025-06-01 03:00:00', '2025-06-01 03:00:00'),
(2, 'Premium Mobile UI Design', 5500000.00, 1, 5500000.00, 6, 6, 'pending', 5500000.00, '2025-06-02 07:30:00', '2025-06-02 07:30:00');

-- --------------------------------------------------------

--
-- Table structure for table `user`
--

CREATE TABLE `user` (
  `id` int NOT NULL,
  `name` varchar(50) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `user`
--

INSERT INTO `user` (`id`, `name`, `username`, `email`, `password`, `created_at`, `updated_at`) VALUES
(5, 'Arthur', 'arthur', '<EMAIL>', '932f3c1b56257ce8539ac269d7aab42550dacf8818d075f0bdf1990562aae3ef', '2025-05-31 17:08:14', '2025-06-03 01:52:39'),
(6, 'Micah', 'micah', '<EMAIL>', '96cae35ce8a9b0244178bf28e4966c2ce1b8385723a96a6b838858cdd6ca0a1e', '2025-06-01 09:48:44', '2025-06-01 17:30:37'),
(7, 'Pearson', 'pearson', '<EMAIL>', '96cae35ce8a9b0244178bf28e4966c2ce1b8385723a96a6b838858cdd6ca0a1e', '2025-06-01 17:26:58', '2025-06-01 17:26:58'),
(8, 'Dutch', 'dutch', '<EMAIL>', '96cae35ce8a9b0244178bf28e4966c2ce1b8385723a96a6b838858cdd6ca0a1e', '2025-06-02 05:22:38', '2025-06-02 05:22:38');

-- --------------------------------------------------------

--
-- Table structure for table `user_google`
--

CREATE TABLE `user_google` (
  `id` int NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `user_id` varchar(100) NOT NULL,
  `photo_url` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `user_google`
--

INSERT INTO `user_google` (`id`, `name`, `email`, `user_id`, `photo_url`, `created_at`, `last_login`, `updated_at`) VALUES
(4, 'MUHAMMAD SYAAMIL MUZHAFFAR', '<EMAIL>', '14', 'https://lh3.googleusercontent.com/a/ACg8ocIow0O9Bx3Yr8V7i9juRxcTI8VM9kfdZnFQ_nleBRSSrNkQRo0=s96-c', '2025-06-04 05:42:40', '2025-06-04 05:50:36', '2025-06-04 05:50:36');

-- --------------------------------------------------------

--
-- Table structure for table `cart`
--

CREATE TABLE `cart` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `service_id` int NOT NULL,
  `service_name` varchar(255) NOT NULL,
  `price` decimal(15,2) NOT NULL,
  `quantity` int NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin`
--
ALTER TABLE `admin`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `admin_google`
--
ALTER TABLE `admin_google`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `admin_id` (`admin_id`);

--
-- Indexes for table `blog`
--
ALTER TABLE `blog`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `contact`
--
ALTER TABLE `contact`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `otp_log`
--
ALTER TABLE `otp_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `email` (`email`,`otp`);

--
-- Indexes for table `portfolio`
--
ALTER TABLE `portfolio`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `transaction`
--
ALTER TABLE `transaction`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `service_id` (`service_id`);

--
-- Indexes for table `user`
--
ALTER TABLE `user`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `user_google`
--
ALTER TABLE `user_google`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `user_id` (`user_id`);

--
-- Indexes for table `cart`
--
ALTER TABLE `cart`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `service_id` (`service_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin`
--
ALTER TABLE `admin`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `admin_google`
--
ALTER TABLE `admin_google`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `blog`
--
ALTER TABLE `blog`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `contact`
--
ALTER TABLE `contact`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `otp_log`
--
ALTER TABLE `otp_log`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `portfolio`
--
ALTER TABLE `portfolio`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `services`
--
ALTER TABLE `services`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `transaction`
--
ALTER TABLE `transaction`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `user`
--
ALTER TABLE `user`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `user_google`
--
ALTER TABLE `user_google`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `cart`
--
ALTER TABLE `cart`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
