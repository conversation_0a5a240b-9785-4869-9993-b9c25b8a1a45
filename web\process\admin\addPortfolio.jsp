<%--
  Document   : addPortfolio
  Created on : Jun 15, 2025
  Author     : Arqeta
  Description: Process untuk menambah portfolio baru
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.io.*"%>
<%@page import="java.nio.file.*"%>
<%@page import="jakarta.servlet.http.Part"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Pastikan request adalah POST, jika bukan, redirect kembali.
    if (!"POST".equalsIgnoreCase(request.getMethod())) {
        response.sendRedirect("../../dashboardadmin.jsp?page=portfolio");
        return;
    }

    try {
        // 1. Ambil parameter dari form
        String name = request.getParameter("name");
        String description = request.getParameter("description");

        // 2. Validasi input tidak boleh kosong
        if (name == null || name.trim().isEmpty() || description == null || description.trim().isEmpty()) {
            session.setAttribute("message", "Semua field harus diisi!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../dashboardadmin.jsp?page=portfolio");
            return;
        }

        // 3. Handle proses upload file gambar
        String fileName = null;
        Part filePart = request.getPart("image");

        if (filePart != null && filePart.getSize() > 0) {
            // Validasi tipe file (harus gambar)
            String contentType = filePart.getContentType();
            if (!contentType.startsWith("image/")) {
                session.setAttribute("message", "File harus berupa gambar!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboardadmin.jsp?page=portfolio");
                return;
            }

            // Validasi ukuran file (maksimal 10MB)
            if (filePart.getSize() > 10 * 1024 * 1024) {
                session.setAttribute("message", "Ukuran file maksimal 10MB!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboardadmin.jsp?page=portfolio");
                return;
            }

            // Generate nama file unik untuk mencegah duplikasi
            String originalFileName = filePart.getSubmittedFileName();
            String fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
            fileName = "portfolio_" + System.currentTimeMillis() + fileExtension;

            // Path untuk menyimpan file - simpan ke direktori build dan source
            String buildUploadPath = application.getRealPath("/dist/img/");
            String sourceUploadPath = application.getRealPath("/").replace("build" + File.separator + "web", "web") + "dist" + File.separator + "img";

            // Buat direktori jika belum ada
            File buildUploadDir = new File(buildUploadPath);
            File sourceUploadDir = new File(sourceUploadPath);
            if (!buildUploadDir.exists()) buildUploadDir.mkdirs();
            if (!sourceUploadDir.exists()) sourceUploadDir.mkdirs();

            // Simpan file ke kedua lokasi
            String buildFilePath = buildUploadPath + File.separator + fileName;
            String sourceFilePath = sourceUploadPath + File.separator + fileName;

            try (InputStream input = filePart.getInputStream()) {
                Files.copy(input, Paths.get(buildFilePath), StandardCopyOption.REPLACE_EXISTING);
            }
            try (InputStream input = filePart.getInputStream()) {
                Files.copy(input, Paths.get(sourceFilePath), StandardCopyOption.REPLACE_EXISTING);
            }

        } else {
            // Jika tidak ada file yang diupload
            session.setAttribute("message", "Gambar portfolio harus diupload!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../dashboardadmin.jsp?page=portfolio");
            return;
        }

        // 4. Simpan data ke database
        PreparedStatement ps = conn.prepareStatement("INSERT INTO portfolio (name, image, description) VALUES (?, ?, ?)");
        ps.setString(1, name.trim());
        ps.setString(2, fileName);
        ps.setString(3, description.trim());
        
        int result = ps.executeUpdate();
        ps.close();

        if (result > 0) {
            session.setAttribute("message", "Portfolio berhasil ditambahkan!");
            session.setAttribute("messageType", "success");
        } else {
            session.setAttribute("message", "Gagal menambahkan portfolio!");
            session.setAttribute("messageType", "error");
        }

    } catch (Exception e) {
        session.setAttribute("message", "Terjadi kesalahan sistem: " + e.getMessage());
        session.setAttribute("messageType", "error");
        e.printStackTrace(); // Penting untuk debugging di console server
    }

    // 5. Redirect kembali ke halaman portfolio setelah semua proses selesai
    response.sendRedirect("../../dashboardadmin.jsp?page=portfolio");
%>
