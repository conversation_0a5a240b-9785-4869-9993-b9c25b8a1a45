/* 
 * Dashboard Stylesheet for Arqeta Website
 * Created on : June 1, 2025
 * Author     : Arqeta
 * Description: Styles for admin dashboard pages
 */

/* ===== Reset & Base Styles ===== */
:root {
  /* Color variables */
  --primary-color: #181818;
  --secondary-color: #ffffff;
  --accent-color: #3f51b5;
  --danger-color: #f44336;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --info-color: #2196f3;
  --bg-color: #f5f5f5;
  --card-bg: #ffffff;
  --text-color: #333333;
  --text-muted: #666666;
  --border-color: #e0e0e0;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --header-height: 60px;
  --sidebar-width: 260px;
  --sidebar-collapsed-width: 70px;
  --transition-speed: 0.3s;

  /* Transition variables */
  --border-radius: 8px;

  /* Font variables */
  --font-family: "Quicksand", sans-serif;

  /* Status colors */
  --status-pending: #ff9800;
  --status-approved: #4caf50;
  --status-rejected: #f44336;
  --status-read: #4caf50;
  --status-unread: #ff9800;
}

/* Dark mode variables */
body.dark-mode {
  --primary-color: #181818;
  --secondary-color: #ffffff;
  --accent-color: #5c6bc0;
  --bg-color: #121212;
  --card-bg: #1e1e1e;
  --text-color: #f0f0f0;
  --text-muted: #aaaaaa;
  --border-color: #333333;
  --shadow-color: rgba(0, 0, 0, 0.3);

  /* Status colors for dark mode */
  --status-pending: #ffb74d;
  --status-approved: #66bb6a;
  --status-rejected: #ef5350;
  --status-read: #66bb6a;
  --status-unread: #ffb74d;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  color: var(--text-color);
  background-color: var(--bg-color);
  line-height: 1.6;
  transition: background-color var(--transition-speed),
    color var(--transition-speed);
}

a {
  color: inherit;
  text-decoration: none;
}

img,
svg {
  max-width: 100%;
  height: auto;
}

button,
input,
textarea,
select {
  font-family: var(--font-family);
}

textarea {
  resize: vertical;
}

/* Dashboard Container */
.dashboard-container {
  display: flex;
  min-height: 100vh;
}

/* Sidebar */
.sidebar {
  width: var(--sidebar-width);
  background-color: var(--card-bg);
  box-shadow: 0 0 15px var(--shadow-color);
  display: flex;
  flex-direction: column;
  transition: width var(--transition-speed), transform var(--transition-speed);
  position: fixed;
  height: 100vh;
  z-index: 1000;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
}

.sidebar-toggle {
  color: var(--text-color);
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.3s ease;
  background: none;
  border: none;
  position: relative;
  width: 40px;
  height: 40px;
}

/* Enhanced Hamburger menu animation - Presisi dan Responsif */
.sidebar-toggle {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px;
}

.sidebar-toggle i {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
  position: relative;
  z-index: 2;
}

/* Animasi rotasi icon hamburger ketika sidebar collapsed */
.sidebar.collapsed .sidebar-toggle i {
  transform: rotate(180deg) scale(0.9);
  opacity: 0.7;
}

.sidebar-toggle:hover {
  background-color: rgba(0, 0, 0, 0.08);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark-mode .sidebar-toggle:hover {
  background-color: rgba(255, 255, 255, 0.08);
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

/* Pastikan hamburger icon tetap terlihat ketika collapsed */
.sidebar.collapsed .sidebar-toggle {
  display: flex !important;
  background-color: rgba(0, 0, 0, 0.05);
}

.dark-mode .sidebar.collapsed .sidebar-toggle {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Animated hamburger menu lines - Lebih presisi */
.sidebar-toggle::before,
.sidebar-toggle::after {
  content: "";
  position: absolute;
  width: 18px;
  height: 2px;
  background: currentColor;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  border-radius: 1px;
  z-index: 1;
}

.sidebar-toggle::before {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -6px);
}

.sidebar-toggle::after {
  top: 50%;
  left: 50%;
  transform: translate(-50%, 6px);
}

/* Animasi X ketika sidebar collapsed */
.sidebar.collapsed .sidebar-toggle::before {
  opacity: 1;
  transform: translate(-50%, -50%) rotate(45deg);
}

.sidebar.collapsed .sidebar-toggle::after {
  opacity: 1;
  transform: translate(-50%, -50%) rotate(-45deg);
}

/* Sembunyikan icon feather ketika collapsed */
.sidebar.collapsed .sidebar-toggle i {
  opacity: 0;
  transform: rotate(180deg) scale(0);
}

.admin-info {
  display: flex;
  align-items: center;
  padding: 10px 20px 20px;
  border-bottom: 1px solid var(--border-color);
  min-height: 70px; /* Ensure consistent height */
}

.admin-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--accent-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  overflow: hidden;
  flex-shrink: 0; /* Prevent shrinking */
}

.admin-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%; /* Ensure image is perfectly circular */
}

.admin-details h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 3px;
}

.admin-details p {
  font-size: 0.85rem;
  color: var(--text-muted);
}

/* Improved Sidebar Menu */
.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 0;
}

.sidebar-menu {
  padding: 15px 0;
}

.sidebar-menu ul li {
  position: relative;
  margin-bottom: 5px;
}

.sidebar-menu ul li a {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: var(--text-color);
  font-weight: 500;
  transition: background-color 0.2s, color 0.2s;
  border-radius: 5px;
  margin: 0 10px;
}

.sidebar-menu ul li a:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark-mode .sidebar-menu ul li a:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.sidebar-menu ul li.active a {
  background-color: rgba(63, 81, 181, 0.1);
  color: var(--accent-color);
  font-weight: 600;
}

.sidebar-menu ul li.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: var(--accent-color);
  border-radius: 0 3px 3px 0;
}

/* Menu icon and text spacing */
.menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  width: 24px;
  height: 24px;
}

.menu-text {
  flex: 1;
  font-size: 0.95rem;
}

.sidebar.collapsed .menu-text {
  display: none;
}

.sidebar.collapsed .sidebar-header h2,
.sidebar.collapsed .admin-details {
  display: none;
}

.sidebar.collapsed .admin-info {
  justify-content: center;
  padding: 10px;
  border-bottom: 1px solid var(--border-color);
}

.sidebar.collapsed .admin-avatar {
  margin-right: 0;
}

/* Ensure hamburger menu icon positioning in collapsed state */
.sidebar.collapsed .sidebar-header {
  justify-content: center;
  padding: 15px 10px;
}

/* Enhanced hamburger menu positioning dan visibility */
.sidebar.collapsed .sidebar-header h2 {
  display: none;
}

.sidebar.collapsed .sidebar-toggle {
  margin: 0 auto;
  position: relative;
  z-index: 10;
}

/* Smooth transition untuk semua elemen sidebar */
.sidebar * {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Pastikan hamburger icon tetap clickable dalam collapsed state */
.sidebar.collapsed .sidebar-toggle {
  pointer-events: all;
  cursor: pointer;
}

/* Enhanced focus states untuk accessibility */
.sidebar-toggle:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.mobile-menu-toggle:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.sidebar.collapsed .sidebar-menu ul li a {
  justify-content: center;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Theme toggle switch */
.theme-toggle-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.theme-toggle-wrapper span {
  font-size: 0.9rem;
  font-weight: 500;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  margin-left: 10px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: var(--accent-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--accent-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.slider.round {
  border-radius: 24px;
}

.slider.round:before {
  border-radius: 50%;
}

.logout-btn {
  display: flex;
  align-items: center;
  padding: 12px 8px;
  color: var(--accent-color);
  font-weight: 500;
  transition: all 0.2s ease;
  border-radius: 8px;
  text-decoration: none;
  width: 100%;
  box-sizing: border-box;
}

.logout-btn i {
  margin-right: 12px;
  font-size: 1.1rem;
}

.logout-btn:hover {
  background-color: rgba(var(--accent-color-rgb), 0.1);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(var(--accent-color-rgb), 0.2);
}

.sidebar.collapsed .theme-toggle-wrapper span,
.sidebar.collapsed .logout-btn span {
  display: none;
}

.sidebar.collapsed .logout-btn {
  justify-content: center;
  padding: 12px;
}

.sidebar.collapsed .logout-btn i {
  margin-right: 0;
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: margin var(--transition-speed);
}

.sidebar.collapsed ~ .main-content {
  margin-left: var(--sidebar-collapsed-width);
}

/* Top Navbar */
.top-navbar {
  height: var(--header-height);
  background-color: var(--card-bg);
  box-shadow: 0 2px 10px var(--shadow-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 990;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.mobile-menu-toggle {
  display: none;
  margin-right: 15px;
  color: var(--text-color);
  background: none;
  border: none;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  align-items: center;
  justify-content: center;
}

.mobile-menu-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.1);
}

.dark-mode .mobile-menu-toggle:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Mobile hamburger animation */
.mobile-menu-toggle i {
  transition: transform 0.3s ease;
}

.sidebar.mobile-active ~ .main-content .mobile-menu-toggle i {
  transform: rotate(90deg);
}

.page-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
}

.navbar-right {
  display: flex;
  align-items: center;
}

/* Theme indicator in navbar */
.theme-indicator {
  color: var(--text-color);
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 15px;
}

/* Data Management Styles */
.page-content {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
}

.page-header h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.data-type-selector {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.selector-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: 2px solid var(--border-color);
  background: var(--card-bg);
  color: var(--text-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.95rem;
}

.selector-btn:hover {
  border-color: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow-color);
}

.selector-btn.active {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
  box-shadow: 0 4px 12px rgba(63, 81, 181, 0.3);
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

/* Export Buttons */
.export-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.btn-export {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-export i {
  width: 16px;
  height: 16px;
}

.btn-excel {
  background-color: #217346;
  color: white;
}

.btn-excel:hover {
  background-color: #1a5c37;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 115, 70, 0.3);
}

.btn-pdf {
  background-color: #dc3545;
  color: white;
}

.btn-pdf:hover {
  background-color: #c82333;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.btn-word {
  background-color: #2b579a;
  color: white;
}

.btn-word:hover {
  background-color: #1e3f73;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(43, 87, 154, 0.3);
}

.btn-json {
  background-color: #6f42c1;
  color: white;
}

.btn-json:hover {
  background-color: #5a2d91;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
}

/* Search Bar - Desain sesuai gambar "desain yang diinginkan.png" */
.search-bar {
  position: relative;
  display: inline-block;
  width: fit-content;
}

.search-bar input {
  padding: 12px 45px 12px 20px;
  border: 1px solid #ddd;
  border-radius: 25px;
  background: #ffffff;
  color: #333;
  font-size: 14px;
  width: 300px;
  height: 44px;
  box-sizing: border-box;
  outline: none;
  transition: all 0.3s ease;
  font-family: "Quicksand", sans-serif;
  font-weight: 400;
}

.search-bar input:focus {
  border-color: #999;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.search-bar input::placeholder {
  color: #999;
  font-size: 14px;
}

.search-bar i {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  pointer-events: none;
  z-index: 2;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  stroke-width: 2;
}

/* Hover effect untuk search bar */
.search-bar:hover input {
  border-color: #666;
}

.search-bar:hover i {
  color: #333;
}

/* Styling khusus untuk action-bar search - menggunakan desain yang sama */
.action-bar .search-bar {
  position: relative;
  display: inline-block;
  width: fit-content;
}

.action-bar .search-bar input {
  padding: 12px 45px 12px 20px;
  border: 1px solid #ddd;
  border-radius: 25px;
  background: #ffffff;
  color: #333;
  font-size: 14px;
  width: 300px;
  height: 44px;
  box-sizing: border-box;
  outline: none;
  transition: all 0.3s ease;
  font-family: "Quicksand", sans-serif;
  font-weight: 400;
}

.action-bar .search-bar input:focus {
  border-color: #999;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.action-bar .search-bar input::placeholder {
  color: #999;
  font-size: 14px;
}

.action-bar .search-bar i {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  pointer-events: none;
  z-index: 2;
  width: 18px;
  height: 18px;
  stroke-width: 2;
}

/* Table Styles */
.table-container {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 10px var(--shadow-color);
  overflow: hidden;
}

.table-responsive {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.95rem;
}

.data-table th {
  background: var(--bg-color);
  color: var(--text-color);
  font-weight: 600;
  padding: 15px 12px;
  text-align: left;
  border-bottom: 2px solid var(--border-color);
  cursor: pointer;
  transition: background-color 0.3s ease;
  position: relative;
  user-select: none;
}

.data-table th:hover {
  background: var(--border-color);
}

.data-table th i {
  margin-left: 5px;
  font-size: 0.8rem;
  opacity: 0.6;
}

.data-table td {
  padding: 12px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-color);
  vertical-align: middle;
}

.data-table tr:hover {
  background: rgba(63, 81, 181, 0.05);
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.btn-edit,
.btn-delete,
.btn-view {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-edit {
  background: var(--info-color);
  color: white;
}

.btn-edit:hover {
  background: #1976d2;
  transform: scale(1.1);
}

.btn-delete {
  background: var(--danger-color);
  color: white;
}

.btn-delete:hover {
  background: #d32f2f;
  transform: scale(1.1);
}

.btn-view {
  background: var(--success-color);
  color: white;
}

.btn-view:hover {
  background: #388e3c;
  transform: scale(1.1);
}

/* Button Styles */
.btn-primary,
.btn-secondary,
.btn-danger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-primary {
  background: var(--accent-color);
  color: white;
}

.btn-primary:hover {
  background: #3949ab;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(63, 81, 181, 0.3);
}

.btn-secondary {
  background: var(--text-muted);
  color: white;
}

.btn-secondary:hover {
  background: #555;
  transform: translateY(-2px);
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background: #d32f2f;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

/* Status Badge Styles */
.status-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.status-pending {
  background: rgba(255, 152, 0, 0.1);
  color: var(--status-pending);
  border: 1px solid var(--status-pending);
}

.status-approved,
.status-completed {
  background: rgba(76, 175, 80, 0.1);
  color: var(--status-approved);
  border: 1px solid var(--status-approved);
}

.status-rejected,
.status-cancelled {
  background: rgba(244, 67, 54, 0.1);
  color: var(--status-rejected);
  border: 1px solid var(--status-rejected);
}

.status-read {
  background: rgba(76, 175, 80, 0.1);
  color: var(--status-read);
  border: 1px solid var(--status-read);
}

.status-unread {
  background: rgba(255, 152, 0, 0.1);
  color: var(--status-unread);
  border: 1px solid var(--status-unread);
}

/* Modal Improvements */
.modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background-color: var(--card-bg);
  margin: 5% auto;
  padding: 0;
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-color);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
}

.close-modal {
  color: var(--text-muted);
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.3s ease;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-modal:hover {
  color: var(--danger-color);
  background: rgba(244, 67, 54, 0.1);
}

.modal-body {
  padding: 25px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px 25px;
  border-top: 1px solid var(--border-color);
  background: var(--bg-color);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Form Styles */
.form-group {
  margin-bottom: 25px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-color);
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--card-bg);
  color: var(--text-color);
  font-size: 0.95rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.1);
}

.password-input {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.toggle-password:hover {
  color: var(--accent-color);
  background: rgba(63, 81, 181, 0.1);
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Content */
.content {
  padding: 20px;
}

/* Page Content Styles */
.page-content {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 25px;
  box-shadow: 0 2px 10px var(--shadow-color);
  margin-bottom: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  flex-wrap: wrap;
  gap: 15px;
}

.page-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.action-bar {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* Data Type Selector */
.data-type-selector {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.selector-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background-color: var(--bg-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.9rem;
}

.selector-btn:hover {
  background-color: var(--accent-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(63, 81, 181, 0.3);
}

.selector-btn.active {
  background-color: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

/* Table Container */
.table-container {
  background-color: var(--card-bg);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px var(--shadow-color);
}

.table-responsive {
  overflow-x: auto;
}

/* Enhanced Data Table */
.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.data-table th {
  background-color: var(--bg-color);
  color: var(--text-color);
  font-weight: 600;
  padding: 15px 12px;
  text-align: left;
  border-bottom: 2px solid var(--border-color);
  white-space: nowrap;
}

.data-table td {
  padding: 12px;
  border-bottom: 1px solid var(--border-color);
  vertical-align: middle;
}

.data-table tr:hover {
  background-color: rgba(63, 81, 181, 0.05);
}

.dark-mode .data-table tr:hover {
  background-color: rgba(92, 107, 192, 0.1);
}

/* Table Image */
.table-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
  border: 2px solid var(--border-color);
}

.no-image {
  color: var(--text-muted);
  font-style: italic;
  font-size: 0.85rem;
}

/* Description Cell */
.description-cell {
  max-width: 200px;
  word-wrap: break-word;
  line-height: 1.4;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.btn-view,
.btn-edit,
.btn-delete {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-view {
  background-color: var(--info-color);
  color: white;
}

.btn-view:hover {
  background-color: #1976d2;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

.btn-edit {
  background-color: var(--warning-color);
  color: white;
}

.btn-edit:hover {
  background-color: #f57c00;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 152, 0, 0.3);
}

.btn-delete {
  background-color: var(--danger-color);
  color: white;
}

.btn-delete:hover {
  background-color: #d32f2f;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
}

/* Status Badges */
.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
}

.status-pending {
  background-color: var(--status-pending);
  color: white;
}

.status-approved {
  background-color: var(--status-approved);
  color: white;
}

.status-rejected {
  background-color: var(--status-rejected);
  color: white;
}

.status-read {
  background-color: var(--status-read);
  color: white;
}

.status-unread {
  background-color: var(--status-unread);
  color: white;
}

/* Unread row highlighting */
.unread-row {
  background-color: rgba(255, 152, 0, 0.05);
}

.dark-mode .unread-row {
  background-color: rgba(255, 183, 77, 0.1);
}

/* Filter Styles */
.status-filter select {
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--card-bg);
  color: var(--text-color);
  font-size: 0.9rem;
  cursor: pointer;
  transition: border-color 0.3s;
}

.status-filter select:focus {
  outline: none;
  border-color: var(--accent-color);
}

/* Warning text */
.warning-text {
  color: var(--danger-color);
  font-size: 0.9rem;
  margin-top: 10px;
}

/* Modal Large */
.modal-large .modal-content {
  max-width: 800px;
  width: 90%;
}

/* No image placeholder */
.no-image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: var(--bg-color);
  border: 2px dashed var(--border-color);
  border-radius: 6px;
  color: var(--text-muted);
  font-size: 0.8rem;
}

.no-image-placeholder i {
  margin-bottom: 4px;
}

/* Form text helper */
.form-text {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin-top: 4px;
}

/* Dashboard Stats */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px var(--shadow-color);
  display: flex;
  align-items: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px var(--shadow-color);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  background-color: rgba(63, 81, 181, 0.1);
  color: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 1.5rem;
}

.stat-info h3 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.stat-info p {
  font-size: 0.9rem;
  color: var(--text-muted);
}

/* Dashboard Section */
.dashboard-section {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px var(--shadow-color);
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.section-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
}

.view-all {
  color: var(--accent-color);
  font-size: 0.9rem;
  font-weight: 500;
}

.view-all:hover {
  text-decoration: underline;
}

/* Page Content Styles */
.page-content {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px var(--shadow-color);
}

.page-header {
  margin-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 15px;
}

.page-header h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.header-info p {
  color: var(--text-muted);
  font-size: 0.9rem;
}

/* Data Type Selector */
.data-type-selector {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.selector-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border: 2px solid var(--border-color);
  background-color: transparent;
  color: var(--text-color);
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.selector-btn:hover {
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.selector-btn.active {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

/* Action Bar */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.btn-primary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: #303f9f;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(63, 81, 181, 0.3);
}

.btn-secondary {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: transparent;
  color: var(--text-color);
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  border-color: var(--accent-color);
  color: var(--accent-color);
}

.btn-danger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: var(--danger-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-danger:hover {
  background-color: #d32f2f;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

/* Stats Row */
.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-item {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px var(--shadow-color);
  display: flex;
  align-items: center;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px var(--shadow-color);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 1.5rem;
  color: white;
}

.stat-icon.pending {
  background-color: var(--status-pending);
}

.stat-icon.approved {
  background-color: var(--status-approved);
}

.stat-icon.rejected {
  background-color: var(--status-rejected);
}

.stat-details h3 {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.stat-details p {
  font-size: 0.9rem;
  color: var(--text-muted);
}

/* Table Styles */
.table-container {
  background-color: var(--card-bg);
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px var(--shadow-color);
}

.table-responsive {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.data-table th,
.data-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.data-table th {
  background-color: var(--bg-color);
  font-weight: 600;
  color: var(--text-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table tbody tr:hover {
  background-color: rgba(63, 81, 181, 0.05);
}

.dark-mode .data-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Action Buttons in Table */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.btn-edit,
.btn-delete,
.btn-approve,
.btn-reject {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-edit {
  background-color: var(--info-color);
  color: white;
}

.btn-edit:hover {
  background-color: #1976d2;
  transform: scale(1.1);
}

.btn-delete {
  background-color: var(--danger-color);
  color: white;
}

.btn-delete:hover {
  background-color: #d32f2f;
  transform: scale(1.1);
}

.btn-approve {
  background-color: var(--success-color);
  color: white;
}

.btn-approve:hover {
  background-color: #388e3c;
  transform: scale(1.1);
}

.btn-reject {
  background-color: var(--warning-color);
  color: white;
}

.btn-reject:hover {
  background-color: #f57c00;
  transform: scale(1.1);
}

/* Status Badges */
.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.status-pending {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--status-pending);
  border: 1px solid var(--status-pending);
}

.status-approved {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--status-approved);
  border: 1px solid var(--status-approved);
}

.status-rejected {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--status-rejected);
  border: 1px solid var(--status-rejected);
}

.status-read {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--status-read);
  border: 1px solid var(--status-read);
}

.status-unread {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--status-unread);
  border: 1px solid var(--status-unread);
}

/* Profile Photo */
.profile-photo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--border-color);
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: var(--card-bg);
  margin: 5% auto;
  padding: 0;
  border-radius: 10px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.close-modal {
  font-size: 24px;
  font-weight: bold;
  color: var(--text-muted);
  cursor: pointer;
  transition: color 0.2s;
}

.close-modal:hover {
  color: var(--danger-color);
}

.modal-body {
  padding: 20px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid var(--border-color);
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-color);
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.9rem;
  background-color: var(--card-bg);
  color: var(--text-color);
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--accent-color);
}

.password-input {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
}

.toggle-password:hover {
  color: var(--accent-color);
}

/* Notification Styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 15px 20px;
  box-shadow: 0 5px 15px var(--shadow-color);
  z-index: 1001;
  transform: translateX(400px);
  transition: transform 0.3s ease;
  max-width: 350px;
  border-left: 4px solid var(--info-color);
}

.notification.show {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notification-message {
  flex: 1;
  margin-right: 15px;
  font-weight: 500;
}

.close-notification {
  font-size: 18px;
  font-weight: bold;
  color: var(--text-muted);
  cursor: pointer;
  transition: color 0.2s;
}

.close-notification:hover {
  color: var(--danger-color);
}

.notification-success {
  border-left-color: var(--success-color);
}

.notification-error {
  border-left-color: var(--danger-color);
}

.notification-warning {
  border-left-color: var(--warning-color);
}

.notification-info {
  border-left-color: var(--info-color);
}

/* Back to Top Button */
.back-to-top {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(63, 81, 181, 0.3);
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
}

.back-to-top.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top:hover {
  background-color: #303f9f;
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(63, 81, 181, 0.4);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.mobile-active {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .mobile-menu-toggle {
    display: flex !important;
  }

  .stats-row {
    grid-template-columns: 1fr;
  }

  .data-type-selector {
    flex-direction: column;
  }

  .action-bar {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .export-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .btn-export {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .modal-content {
    width: 95%;
    margin: 10% auto;
  }

  .notification {
    right: 10px;
    left: 10px;
    max-width: none;
  }

  .table-responsive {
    font-size: 0.8rem;
  }

  .data-table th,
  .data-table td {
    padding: 8px 10px;
  }
}

@media (max-width: 480px) {
  .content {
    padding: 15px;
  }

  .page-content {
    padding: 15px;
  }

  .modal-content {
    margin: 5% auto;
  }

  .stat-item {
    padding: 15px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .stat-details h3 {
    font-size: 1.5rem;
  }
}

/* Additional utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.p-10 {
  padding: 10px;
}

.p-20 {
  padding: 20px;
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-muted);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.5;
}

.empty-state h3 {
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.empty-state p {
  font-size: 0.9rem;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow-color);
}

/* Focus styles */
button:focus,
input:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* Disabled state */
button:disabled,
input:disabled,
textarea:disabled,
select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Success/Error states */
.form-group.success input {
  border-color: var(--success-color);
}

.form-group.error input {
  border-color: var(--danger-color);
}

.form-error {
  color: var(--danger-color);
  font-size: 0.8rem;
  margin-top: 5px;
}

.form-success {
  color: var(--success-color);
  font-size: 0.8rem;
  margin-top: 5px;
}

/* Table Styles */
.table-responsive {
  width: 100%;
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

table th,
table td {
  padding: 12px 15px;
  text-align: left;
}

table th {
  background-color: rgba(0, 0, 0, 0.02);
  color: var(--text-muted);
  font-weight: 600;
  font-size: 0.9rem;
  border-bottom: 2px solid var(--border-color);
}

.dark-mode table th {
  background-color: rgba(255, 255, 255, 0.05);
}

table td {
  border-bottom: 1px solid var(--border-color);
  font-size: 0.95rem;
}

table tr:last-child td {
  border-bottom: none;
}

.status-badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-unread {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--warning-color);
}

.status-read {
  background-color: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
}

.btn-view,
.btn-edit,
.btn-delete {
  padding: 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.btn-view {
  background-color: rgba(33, 150, 243, 0.1);
  color: var(--info-color);
}

.btn-edit {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--warning-color);
}

.btn-delete {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--danger-color);
}

.btn-view:hover {
  background-color: rgba(33, 150, 243, 0.2);
}

.btn-edit:hover {
  background-color: rgba(255, 152, 0, 0.2);
}

.btn-delete:hover {
  background-color: rgba(244, 67, 54, 0.2);
}

/* Add Button */
.btn-add {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: var(--accent-color);
  color: white;
  border-radius: 5px;
  font-weight: 500;
}

.btn-add i {
  margin-right: 8px;
}

.btn-add:hover {
  background-color: #303f9f;
}

/* Back to top button */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--accent-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
  z-index: 1000;
}

.back-to-top.active {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background-color: #303f9f;
}

/* Enhanced Notification System */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: var(--card-bg);
  color: var(--text-color);
  padding: 0;
  border-radius: var(--border-radius);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 2000;
  transform: translateX(120%);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 420px;
  min-width: 300px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  opacity: 0;
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification.notification-fade-out {
  transform: translateX(120%);
  opacity: 0;
}

.notification-content {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  gap: 12px;
  position: relative;
}

.notification-content i:first-child {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
}

.notification-content span {
  flex: 1;
  font-size: 0.95rem;
  line-height: 1.4;
  font-weight: 500;
}

.notification-close {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notification-close:hover {
  color: var(--text-color);
  background: rgba(0, 0, 0, 0.1);
}

.dark-mode .notification-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Notification types with colored left border and icons */
.notification-success {
  border-left: 4px solid var(--success-color);
}

.notification-success .notification-content i:first-child {
  color: var(--success-color);
}

.notification-error {
  border-left: 4px solid var(--danger-color);
}

.notification-error .notification-content i:first-child {
  color: var(--danger-color);
}

.notification-warning {
  border-left: 4px solid var(--warning-color);
}

.notification-warning .notification-content i:first-child {
  color: var(--warning-color);
}

.notification-info {
  border-left: 4px solid var(--info-color);
}

.notification-info .notification-content i:first-child {
  color: var(--info-color);
}

/* Progress bar for auto-dismiss */
.notification::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background: var(--accent-color);
  animation: notificationProgress 5s linear forwards;
}

.notification-success::after {
  background: var(--success-color);
}

.notification-error::after {
  background: var(--danger-color);
}

.notification-warning::after {
  background: var(--warning-color);
}

.notification-info::after {
  background: var(--info-color);
}

@keyframes notificationProgress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1200;
  overflow: auto;
}

.modal-content {
  background-color: var(--card-bg);
  margin: 50px auto;
  padding: 20px;
  border-radius: 10px;
  width: 100%;
  max-width: 600px;
  box-shadow: 0 5px 15px var(--shadow-color);
  transform: translateY(-20px);
  opacity: 0;
  animation: modalFadeIn 0.3s forwards;
}

@keyframes modalFadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
  font-size: 1.3rem;
  font-weight: 600;
}

.close-modal {
  font-size: 1.5rem;
  cursor: pointer;
  transition: color 0.2s;
}

.close-modal:hover {
  color: var(--danger-color);
}

.modal-body {
  margin-bottom: 20px;
}

.modal-form .form-group {
  margin-bottom: 15px;
}

.modal-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 0.95rem;
}

.modal-form input,
.modal-form select,
.modal-form textarea {
  width: 100%;
  padding: 10px;
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 5px;
  color: var(--text-color);
}

.modal-form input[type="file"] {
  padding: 8px;
}

.form-hint {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin-top: 5px;
}

.current-image {
  margin-top: 10px;
  padding: 10px;
  background-color: var(--bg-color);
  border-radius: 5px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.current-image p {
  margin-bottom: 5px;
  font-size: 0.9rem;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 40px;
  padding: 25px 0;
  border-top: 1px solid var(--border-color);
  background: var(--bg-color);
  border-radius: 0 0 10px 10px;
  margin: 30px -30px -30px -30px;
  padding: 25px 30px;
}

.btn-save,
.submit-button {
  background-color: var(--accent-color);
  color: white;
}

.btn-save:hover,
.submit-button:hover {
  background-color: #303f9f;
}

.btn-cancel,
.cancel-button {
  background-color: var(--bg-color);
  color: var(--text-color);
}

.btn-cancel:hover,
.cancel-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark-mode .btn-cancel:hover,
.dark-mode .cancel-button:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.btn-delete,
.delete-button {
  background-color: var(--danger-color);
  color: white;
}

.btn-delete:hover,
.delete-button:hover {
  background-color: #d32f2f;
}

/* Contact details */
.contact-details {
  margin-bottom: 20px;
}

.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 2px solid var(--border-color);
}

.contact-header h3 {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text-color);
}

.contact-info {
  background: var(--bg-color);
  padding: 20px;
  border-radius: var(--border-radius);
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
}

.message-content {
  background: var(--bg-color);
  padding: 20px;
  border-radius: var(--border-radius);
  margin-bottom: 20px;
  border: 1px solid var(--border-color);
}

.message-content label {
  font-weight: 600;
  color: var(--text-muted);
  margin-bottom: 10px;
  display: block;
}

.message-text {
  color: var(--text-color);
  line-height: 1.8;
  padding: 20px;
  background: var(--card-bg);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  min-height: 120px;
  max-height: 300px;
  overflow-y: auto;
  font-size: 0.95rem;
  text-align: justify;
  hyphens: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.contact-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

.loading-message {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-muted);
  font-style: italic;
}

.error-message {
  text-align: center;
  padding: 40px 20px;
  color: var(--danger-color);
  background: rgba(244, 67, 54, 0.1);
  border-radius: var(--border-radius);
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.warning-text {
  color: var(--warning-color);
  font-size: 0.9rem;
  margin-top: 10px;
}

.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.contact-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
}

.contact-info {
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.info-row label {
  font-weight: 600;
  min-width: 140px;
  color: var(--text-muted);
  margin-right: 15px;
}

.info-row span {
  flex: 1;
  color: var(--text-color);
  word-break: break-word;
}

.message-content {
  margin-bottom: 25px;
}

.message-content label {
  font-weight: 600;
  color: var(--text-muted);
  display: block;
  margin-bottom: 10px;
}

.message-text {
  background-color: var(--bg-color);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  line-height: 1.8;
  color: var(--text-color);
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 0.95rem;
  max-height: 300px;
  overflow-y: auto;
  text-align: justify;
  hyphens: auto;
}

.contact-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
}

.contact-actions button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.contact-actions button i {
  width: 16px;
  height: 16px;
}

/* Loading and error messages */
.loading-message {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-muted);
  font-style: italic;
}

.error-message {
  text-align: center;
  padding: 20px;
  color: var(--danger-color);
  background-color: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.2);
  border-radius: 6px;
  margin: 10px 0;
}

.detail-item {
  margin-bottom: 10px;
  display: flex;
}

.detail-label {
  font-weight: 600;
  min-width: 100px;
}

.detail-value {
  flex: 1;
}

.detail-item.full {
  flex-direction: column;
}

.detail-message {
  background-color: var(--bg-color);
  padding: 15px;
  border-radius: 5px;
  margin-top: 5px;
  white-space: pre-wrap;
}

/* Unread row highlighting */
.unread-row {
  background-color: rgba(255, 152, 0, 0.05);
}

/* Data table styling */
.data-table-container {
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-title {
  font-size: 1.3rem;
  font-weight: 600;
}

.add-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: var(--accent-color);
  color: white;
  border-radius: 5px;
  font-weight: 500;
}

.add-button i {
  margin-right: 8px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 8px var(--shadow-color);
  background-color: var(--card-bg);
}

.data-table th,
.data-table td {
  padding: 12px 15px;
  text-align: left;
}

.data-table th {
  background-color: rgba(0, 0, 0, 0.02);
  color: var(--text-muted);
  font-weight: 600;
  font-size: 0.9rem;
  border-bottom: 1px solid var(--border-color);
}

.dark-mode .data-table th {
  background-color: rgba(255, 255, 255, 0.05);
}

.data-table td {
  border-bottom: 1px solid var(--border-color);
  font-size: 0.95rem;
}

.data-table tr:last-child td {
  border-bottom: none;
}

.edit-button,
.delete-button {
  padding: 6px 12px;
  margin-right: 5px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  font-size: 0.85rem;
}

.edit-button i,
.delete-button i {
  margin-right: 5px;
}

.edit-button {
  background-color: rgba(255, 152, 0, 0.1);
  color: var(--warning-color);
}

.delete-button {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--danger-color);
}

.edit-button:hover {
  background-color: rgba(255, 152, 0, 0.2);
}

.delete-button:hover {
  background-color: rgba(244, 67, 54, 0.2);
}

/* User photo */
.user-photo {
  border-radius: 50%;
  object-fit: cover;
}

/* Content wrapper for included pages */
.content-wrapper {
  padding: 20px;
}

.content-header {
  margin-bottom: 20px;
}

.content-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-title h3 {
  font-size: 1.3rem;
  font-weight: 600;
}

.content-body {
  background-color: var(--card-bg);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px var(--shadow-color);
}

/* Thumbnail for images */
.thumbnail {
  border-radius: 5px;
  object-fit: cover;
}

/* Google login button */
.google-login-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 10px 15px;
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  color: #333;
  font-weight: 500;
  transition: background-color 0.2s;
  width: 100%;
  margin-top: 20px;
}

.google-login-btn img {
  width: 20px;
  height: 20px;
}

.google-login-btn:hover {
  background-color: #f5f5f5;
}

/* Enhanced Mobile Responsive Styles */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .action-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .export-buttons {
    order: -1; /* Tampilkan export buttons di atas */
  }

  .btn-export span {
    display: none; /* Sembunyikan teks pada mobile kecil */
  }

  .btn-export {
    padding: 10px;
    min-width: 44px;
    justify-content: center;
  }

  .search-bar input {
    width: 100%;
  }
}

@media (max-width: 991px) {
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .data-type-selector {
    flex-direction: column;
    gap: 8px;
  }

  .selector-btn {
    justify-content: center;
  }
}

@media (max-width: 767px) {
  :root {
    --sidebar-width: 280px;
    --header-height: 70px;
  }

  /* Mobile Sidebar Improvements */
  .sidebar {
    transform: translateX(-100%);
    box-shadow: none;
    z-index: 1500;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar.mobile-active {
    transform: translateX(0);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
  }

  /* Mobile overlay */
  .sidebar.mobile-active::before {
    content: "";
    position: fixed;
    top: 0;
    left: 280px;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: -1;
    animation: fadeIn 0.3s ease;
  }

  .main-content {
    margin-left: 0 !important;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .mobile-menu-toggle {
    display: flex;
    order: -1;
  }

  /* Enhanced mobile hamburger animation - Presisi dan Smooth */
  .mobile-menu-toggle {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 8px;
  }

  .mobile-menu-toggle i {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
    position: relative;
    z-index: 2;
  }

  /* Animasi ketika mobile menu aktif */
  .sidebar.mobile-active ~ .main-content .mobile-menu-toggle i {
    transform: rotate(90deg) scale(0);
    opacity: 0;
  }

  .mobile-menu-toggle:hover {
    background-color: rgba(0, 0, 0, 0.08);
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .dark-mode .mobile-menu-toggle:hover {
    background-color: rgba(255, 255, 255, 0.08);
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
  }

  /* Mobile hamburger lines animation - Lebih presisi */
  .mobile-menu-toggle::before,
  .mobile-menu-toggle::after {
    content: "";
    position: absolute;
    width: 18px;
    height: 2px;
    background: currentColor;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    border-radius: 1px;
    z-index: 1;
  }

  .mobile-menu-toggle::before {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -6px);
  }

  .mobile-menu-toggle::after {
    top: 50%;
    left: 50%;
    transform: translate(-50%, 6px);
  }

  /* Animasi X ketika mobile menu aktif */
  .sidebar.mobile-active ~ .main-content .mobile-menu-toggle::before {
    opacity: 1;
    transform: translate(-50%, -50%) rotate(45deg);
  }

  .sidebar.mobile-active ~ .main-content .mobile-menu-toggle::after {
    opacity: 1;
    transform: translate(-50%, -50%) rotate(-45deg);
  }

  /* Background highlight ketika mobile menu aktif */
  .sidebar.mobile-active ~ .main-content .mobile-menu-toggle {
    background-color: rgba(0, 0, 0, 0.1);
  }

  .dark-mode .sidebar.mobile-active ~ .main-content .mobile-menu-toggle {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .modal-content {
    margin: 10px;
    width: calc(100% - 20px);
    max-height: calc(100vh - 20px);
    overflow-y: auto;
  }

  /* Mobile table improvements */
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .data-table {
    min-width: 600px;
  }

  .data-table th,
  .data-table td {
    padding: 8px 6px;
    font-size: 0.85rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .btn-edit,
  .btn-delete,
  .btn-view {
    width: 30px;
    height: 30px;
    font-size: 0.8rem;
  }

  /* Mobile form improvements */
  .form-group input,
  .form-group textarea,
  .form-group select {
    padding: 10px 12px;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .page-content {
    padding: 15px;
  }

  .page-header h2 {
    font-size: 1.5rem;
  }

  /* Mobile status badges */
  .status-badge {
    font-size: 0.7rem;
    padding: 4px 8px;
  }
}

@media (max-width: 480px) {
  :root {
    --sidebar-width: 260px;
  }

  .page-content {
    padding: 10px;
  }

  .modal-content {
    margin: 5px;
    width: calc(100% - 10px);
  }

  .data-table {
    min-width: 500px;
  }

  .selector-btn {
    padding: 8px 12px;
    font-size: 0.85rem;
  }

  .btn-primary,
  .btn-secondary,
  .btn-danger {
    padding: 10px 15px;
    font-size: 0.9rem;
  }
}

/* ===== User Dashboard Specific Styles ===== */

/* Account Page Styles */
.account-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
}

.page-header h2 {
  font-size: 2.2rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.page-header p {
  color: var(--text-muted);
  font-size: 1.1rem;
  margin: 0;
}

/* Account Card Styles */
.account-card {
  background: var(--card-bg);
  border-radius: 12px;
  box-shadow: 0 4px 20px var(--shadow-color);
  overflow: hidden;
  margin-bottom: 30px;
  border: 1px solid var(--border-color);
}

.account-card.google-account {
  border-left: 4px solid #4285f4;
}

.account-header {
  display: flex;
  align-items: center;
  padding: 30px;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--bg-color) 0%, var(--card-bg) 100%);
}

.account-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  overflow: hidden;
}

.account-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.account-avatar i {
  font-size: 2rem;
  color: white;
}

.account-info h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-color);
}

.account-type {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin-bottom: 5px;
}

.account-email {
  font-size: 1rem;
  color: var(--text-color);
  font-weight: 500;
}

.account-body {
  padding: 35px;
}

.info-section h4 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-section p {
  color: var(--text-muted);
  line-height: 1.7;
  margin-bottom: 25px;
  font-size: 1rem;
}

.form-note {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin-top: 15px;
  padding: 12px 16px;
  background: var(--bg-color);
  border-radius: 8px;
  border-left: 3px solid var(--accent-color);
}

.account-content {
  display: grid;
  gap: 30px;
}

/* Google Account Info Styles */
.google-account-info {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: 0 2px 10px var(--shadow-color);
}

.google-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #4285f4, #34a853);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 20px;
}

.profile-section {
  display: flex;
  gap: 30px;
  align-items: flex-start;
  margin-bottom: 30px;
}

.profile-image {
  flex-shrink: 0;
}

.profile-image img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--border-color);
}

.default-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: var(--accent-color);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 3rem;
}

.profile-info {
  flex: 1;
}

.google-info {
  background: rgba(66, 133, 244, 0.1);
  border: 1px solid rgba(66, 133, 244, 0.2);
  border-radius: var(--border-radius);
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.google-info i {
  color: #4285f4;
}

/* Form Sections */
.form-section {
  background: var(--card-bg);
  border-radius: 10px;
  padding: 30px;
  margin-bottom: 25px;
  border: 1px solid var(--border-color);
  position: relative;
}

.form-section h4 {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 25px;
  color: var(--text-color);
  padding-bottom: 15px;
  border-bottom: 2px solid var(--accent-color);
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-description {
  color: var(--text-muted);
  font-size: 0.9rem;
  margin-bottom: 20px;
}

.password-input {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: color 0.2s;
}

.toggle-password:hover {
  color: var(--text-color);
}

.password-strength {
  margin-top: 8px;
}

.strength-bar {
  height: 4px;
  background: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 5px;
}

.strength-fill {
  height: 100%;
  transition: width 0.3s, background-color 0.3s;
}

.strength-text {
  font-size: 0.8rem;
  font-weight: 500;
}

.strength-text + small {
  display: block;
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-top: 2px;
}

/* Account Statistics */
.account-stats {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 30px;
  box-shadow: 0 2px 10px var(--shadow-color);
}

.account-stats h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--text-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: rgba(63, 81, 181, 0.05);
  border-radius: var(--border-radius);
  border: 1px solid rgba(63, 81, 181, 0.1);
}

.stat-item .stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--accent-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-info {
  flex: 1;
}

.stat-number {
  display: block;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 2px;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--text-muted);
}

/* Basket/Cart Styles */
.basket-container {
  max-width: 1200px;
  margin: 0 auto;
}

.basket-header {
  margin-bottom: 30px;
  text-align: center;
}

.basket-header h2 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.basket-header p {
  color: var(--text-muted);
  font-size: 1.1rem;
}

.cart-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 40px;
}

.cart-item {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: 0 2px 10px var(--shadow-color);
  display: grid;
  grid-template-columns: 120px 1fr auto auto;
  gap: 20px;
  align-items: center;
}

.item-image img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.item-details h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-color);
}

.item-price {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--accent-color);
  margin-bottom: 15px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.qty-btn {
  width: 32px;
  height: 32px;
  border: 1px solid var(--border-color);
  background: var(--card-bg);
  color: var(--text-color);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.qty-btn:hover {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.qty-input {
  width: 60px;
  height: 32px;
  text-align: center;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background: var(--card-bg);
  color: var(--text-color);
}

.item-total {
  text-align: right;
}

.total-price {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 10px;
}

.remove-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  background: none;
  border: none;
  color: var(--danger-color);
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.2s;
  font-size: 0.9rem;
}

.remove-btn:hover {
  background: rgba(244, 67, 54, 0.1);
}

.item-actions {
  display: flex;
  justify-content: center;
}

.buy-now-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  font-size: 0.9rem;
}

/* Empty Cart */
.empty-cart {
  text-align: center;
  padding: 60px 20px;
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 10px var(--shadow-color);
}

.empty-icon {
  font-size: 4rem;
  color: var(--text-muted);
  margin-bottom: 20px;
}

.empty-cart h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.empty-cart p {
  color: var(--text-muted);
  margin-bottom: 30px;
}

/* Cart Container */
.cart-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.cart-wrapper {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Page Header */
.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
  border-bottom: 1px solid var(--border-color);
}

.page-header h2 {
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 10px;
}

.page-header p {
  font-size: 1.1rem;
  color: var(--text-muted);
  margin: 0;
}

/* Empty Cart Styling */
.empty-cart {
  text-align: center;
  padding: 80px 20px;
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 10px var(--shadow-color);
  margin: 40px auto;
  max-width: 500px;
}

.empty-cart-icon {
  font-size: 6.5rem;
  color: var(--text-muted);
  margin-bottom: 20px;
}

.empty-cart h3 {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 15px;
}

.empty-cart p {
  font-size: 1.1rem;
  color: var(--text-muted);
  margin-bottom: 30px;
}

.empty-cart .btn-primary {
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  background: var(--accent-color);
  color: white;
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.empty-cart .btn-primary:hover {
  background: var(--accent-color);
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
}

/* Cart Items Grid Layout */
.cart-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

/* Cart Item Cards - New Design */
.cart-item-card {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 4px 15px var(--shadow-color);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-color);
}

.cart-item-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px var(--shadow-color);
}

.cart-item-card .service-image {
  height: 220px;
  overflow: hidden;
  position: relative;
}

.cart-item-card .service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.cart-item-card:hover .service-image img {
  transform: scale(1.08);
}

.cart-item-card .service-content {
  padding: 25px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.cart-item-card .service-content h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-color);
  line-height: 1.4;
}

.cart-item-card .service-price {
  margin-bottom: 12px;
}

.cart-item-card .current-price {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--accent-color);
}

.cart-item-card .old-price {
  font-size: 1rem;
  color: var(--text-muted);
  text-decoration: line-through;
  margin-right: 10px;
}

.cart-item-card .price-change-notice {
  font-size: 0.85rem;
  color: var(--warning-color);
  font-style: italic;
  display: block;
  margin-top: 6px;
  padding: 4px 8px;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 4px;
  border-left: 3px solid var(--warning-color);
}

.cart-item-card .service-quantity {
  font-size: 1rem;
  color: var(--text-muted);
  margin-bottom: 12px;
  padding: 8px 12px;
  background: var(--bg-secondary);
  border-radius: 6px;
  display: inline-block;
  width: fit-content;
}

.cart-item-card .service-total {
  font-size: 1.1rem;
  margin-bottom: 20px;
  color: var(--text-color);
  font-weight: 600;
  padding: 10px 0;
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

.cart-item-card .service-actions {
  margin-top: auto;
}

.btn-buy-only {
  width: 100%;
  padding: 15px 25px;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-buy-only:hover {
  background: var(--accent-hover);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* Cart Summary */
.cart-summary {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 35px;
  box-shadow: 0 6px 20px var(--shadow-color);
  border: 1px solid var(--border-color);
  border-top: 4px solid var(--accent-color);
  margin-top: 30px;
  position: sticky;
  top: 20px;
}

.cart-summary .summary-content h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 20px;
  text-align: center;
}

.cart-summary .summary-total {
  background: var(--bg-secondary);
  padding: 20px;
  border-radius: var(--border-radius);
  margin-bottom: 25px;
  text-align: center;
  border: 2px solid var(--accent-color);
}

.cart-summary .summary-total strong {
  font-size: 1.4rem;
  color: var(--accent-color);
  font-weight: 700;
}

.cart-summary .summary-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.cart-summary .summary-actions button {
  padding: 15px 25px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cart-summary .btn-primary {
  background: var(--accent-color);
  color: white;
}

.cart-summary .btn-primary:hover {
  background: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.cart-summary .btn-secondary {
  background: var(--bg-secondary);
  color: var(--text-color);
  border: 2px solid var(--border-color);
}

.cart-summary .btn-secondary:hover {
  background: var(--text-muted);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid var(--border-color);
}

.summary-row:last-of-type {
  border-bottom: none;
}

.summary-row.total {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-color);
  border-top: 2px solid var(--border-color);
  margin-top: 10px;
  padding-top: 15px;
}

.cart-actions {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.cart-actions .btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* Transaction History Styles */
.transaction-history-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* Transaction Filters */
.transaction-filters {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: 0 2px 10px var(--shadow-color);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 15px;
}

.filter-group label {
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-color);
  white-space: nowrap;
}

.filter-group select {
  padding: 8px 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--border-radius);
  background: var(--bg-color);
  color: var(--text-color);
  font-size: 0.9rem;
  min-width: 180px;
  cursor: pointer;
  transition: border-color 0.2s;
}

.filter-group select:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(var(--accent-color-rgb), 0.1);
}

.history-header {
  margin-bottom: 30px;
  text-align: center;
}

.history-header h2 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.history-header p {
  color: var(--text-muted);
  font-size: 1.1rem;
}

/* Filter Section */
.filter-section {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 10px var(--shadow-color);
}

.filter-controls {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  display: flex;
  flex: 1;
  min-width: 250px;
}

.search-box input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: 6px 0 0 6px;
  background: var(--card-bg);
  color: var(--text-color);
}

.search-box button {
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-left: none;
  border-radius: 0 6px 6px 0;
  background: var(--accent-color);
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-box button:hover {
  background: #303f9f;
}

.status-filter select {
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--card-bg);
  color: var(--text-color);
  min-width: 150px;
}

/* Transaction Statistics */
.transaction-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

/* Transaction List */
.transaction-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.transaction-item {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 25px;
  box-shadow: 0 2px 10px var(--shadow-color);
  display: flex;
  flex-direction: column;
  gap: 20px;
  transition: transform 0.2s, box-shadow 0.2s;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.transaction-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px var(--shadow-color);
}

.transaction-image img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.transaction-details {
  flex: 1;
}

.transaction-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.transaction-header .transaction-id,
.transaction-header .transaction-date {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.transaction-header .label {
  font-size: 0.85rem;
  color: var(--text-muted);
  font-weight: 500;
}

.transaction-header .value {
  font-size: 0.95rem;
  color: var(--text-color);
  font-weight: 600;
}

.transaction-status {
  display: flex;
  align-items: center;
}

.transaction-id {
  font-size: 0.9rem;
  color: var(--text-muted);
  font-weight: 500;
}

.transaction-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
}

.info-row .label {
  font-size: 0.9rem;
  color: var(--text-muted);
}

.info-row .value {
  font-weight: 500;
  color: var(--text-color);
}

.info-row .total-amount {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--accent-color);
}

.transaction-status {
  text-align: center;
}

.status-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 15px;
}

.status-pending {
  background: rgba(255, 152, 0, 0.1);
  color: var(--warning-color);
  border: 1px solid rgba(255, 152, 0, 0.3);
}

.status-completed {
  background: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-cancelled {
  background: rgba(244, 67, 54, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(244, 67, 54, 0.3);
}

/* Transaction Content Styles */
.transaction-content {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.service-info {
  display: flex;
  gap: 20px;
  flex: 1;
  align-items: flex-start;
}

.service-image {
  flex-shrink: 0;
  width: 120px;
  height: 120px;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.service-image:hover img {
  transform: scale(1.05);
}

.service-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.service-details h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  line-height: 1.4;
}

.service-specs {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.spec-item:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.spec-item.total {
  border-top: 2px solid var(--accent-color);
  padding-top: 12px;
  margin-top: 8px;
}

.spec-label {
  font-size: 0.9rem;
  color: var(--text-muted);
  font-weight: 500;
}

.spec-value {
  font-size: 0.95rem;
  color: var(--text-color);
  font-weight: 600;
}

.spec-item.total .spec-value {
  font-size: 1.1rem;
  color: var(--accent-color);
  font-weight: 700;
}

.transaction-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 200px;
}

.action-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  font-weight: 500;
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-muted);
}

.action-info.success {
  background: rgba(76, 175, 80, 0.1);
  color: var(--success-color);
}

.action-info.error {
  background: rgba(244, 67, 54, 0.1);
  color: var(--danger-color);
}

.action-info i {
  font-size: 1rem;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 5px;
  justify-content: center;
  min-width: 100px;
}

/* Empty Transactions */
.empty-transactions {
  text-align: center;
  padding: 60px 20px;
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 10px var(--shadow-color);
}

.empty-transactions .empty-icon {
  font-size: 4rem;
  color: var(--text-muted);
  margin-bottom: 20px;
}

.empty-transactions h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.empty-transactions p {
  color: var(--text-muted);
  margin-bottom: 30px;
}

/* Recommended Services */
.recommended-services {
  margin-top: 40px;
}

.recommended-services h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--text-color);
}

.service-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.service-card {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 2px 10px var(--shadow-color);
  transition: transform 0.2s, box-shadow 0.2s;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px var(--shadow-color);
}

.service-image img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.service-info {
  padding: 20px;
}

.service-info h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-color);
}

.service-info .price {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--accent-color);
  margin-bottom: 5px;
}

/* Add Service Modal Styles */
#addServiceModal .modal-content {
  max-width: 900px;
  width: 95%;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  max-height: 500px;
  overflow-y: auto;
  padding: 10px;
}

.service-card-modal {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 2px 10px var(--shadow-color);
  transition: transform 0.2s, box-shadow 0.2s;
}

.service-card-modal:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px var(--shadow-color);
}

.service-card-modal .service-image {
  height: 150px;
  overflow: hidden;
}

.service-card-modal .service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.service-card-modal:hover .service-image img {
  transform: scale(1.05);
}

.service-card-modal .service-info {
  padding: 15px;
}

.service-card-modal .service-info h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-color);
}

.service-card-modal .service-price {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--accent-color);
  margin-bottom: 5px;
}

.service-card-modal .service-quantity {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin-bottom: 15px;
}

.btn-add-to-cart {
  width: 100%;
  padding: 10px 15px;
  background: var(--accent-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-add-to-cart:hover:not(:disabled) {
  background: var(--accent-hover);
  transform: translateY(-1px);
}

.btn-add-to-cart:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
  opacity: 0.6;
}

/* Empty cart icon styling */
.empty-cart-icon {
  font-size: 6.5rem;
  color: var(--text-muted);
  margin-bottom: 20px;
}

/* Responsive Design for Cart */
@media (max-width: 1024px) {
  .cart-container {
    padding: 15px;
  }

  .cart-items {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
  }

  .cart-summary {
    position: static;
    margin-top: 25px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 20px 0;
    margin-bottom: 30px;
  }

  .page-header h2 {
    font-size: 2rem;
  }

  .page-header p {
    font-size: 1rem;
  }

  .cart-items {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .cart-item-card .service-image {
    height: 200px;
  }

  .cart-item-card .service-content {
    padding: 20px;
  }

  .cart-summary {
    padding: 25px;
  }

  .cart-summary .summary-actions {
    flex-direction: column;
    gap: 12px;
  }

  .cart-summary .summary-actions button {
    width: 100%;
    padding: 12px 20px;
    font-size: 1rem;
  }

  .empty-cart {
    padding: 60px 20px;
    margin: 30px auto;
  }

  .empty-cart h3 {
    font-size: 1.5rem;
  }

  .empty-cart p {
    font-size: 1rem;
  }

  .empty-cart .btn-primary {
    padding: 12px 25px;
    font-size: 1rem;
  }

  #addServiceModal .modal-content {
    width: 95%;
    margin: 10px auto;
  }

  .services-grid {
    grid-template-columns: 1fr;
    max-height: 400px;
  }

  .service-card-modal .service-image {
    height: 140px;
  }
}

@media (max-width: 480px) {
  .cart-container {
    padding: 10px;
  }

  .page-header {
    padding: 15px 0;
    margin-bottom: 25px;
  }

  .page-header h2 {
    font-size: 1.8rem;
  }

  .cart-item-card .service-content {
    padding: 18px;
  }

  .cart-item-card .service-content h3 {
    font-size: 1.2rem;
  }

  .cart-item-card .service-image {
    height: 180px;
  }

  .btn-buy-only {
    padding: 12px 20px;
    font-size: 1rem;
  }

  .cart-summary {
    padding: 20px;
  }

  .cart-summary .summary-content h3 {
    font-size: 1.3rem;
  }

  .cart-summary .summary-total strong {
    font-size: 1.2rem;
  }

  .empty-cart {
    padding: 50px 15px;
  }

  .empty-cart-icon {
    font-size: 3.5rem;
  }

  .empty-cart h3 {
    font-size: 1.4rem;
  }

  .empty-cart .btn-primary {
    padding: 10px 20px;
    font-size: 0.95rem;
  }

  .services-grid {
    max-height: 350px;
  }
}

.service-info .stock {
  font-size: 0.9rem;
  color: var(--text-muted);
  margin-bottom: 15px;
}

.service-actions {
  display: flex;
  gap: 10px;
}

.service-actions .btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  font-size: 0.85rem;
  padding: 8px 12px;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  margin-top: 30px;
}

.page-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-color);
  background: var(--card-bg);
  color: var(--text-color);
  text-decoration: none;
  border-radius: 6px;
  transition: all 0.2s;
  font-weight: 500;
}

.page-btn:hover {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

.page-btn.active {
  background: var(--accent-color);
  color: white;
  border-color: var(--accent-color);
}

/* Checkout Modal Styles */
.checkout-summary {
  margin-bottom: 20px;
}

.summary-item {
  display: flex;
  gap: 15px;
  align-items: center;
  padding: 15px;
  background: rgba(63, 81, 181, 0.05);
  border-radius: var(--border-radius);
  border: 1px solid rgba(63, 81, 181, 0.1);
}

.summary-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: var(--border-radius);
  flex-shrink: 0;
}

.summary-details {
  flex: 1;
}

.summary-details h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-color);
}

.summary-details p {
  margin-bottom: 5px;
  color: var(--text-muted);
}

.summary-details .total {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--accent-color);
}

/* Detail Modal Styles */
.detail-section {
  margin-bottom: 25px;
}

.detail-section h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--text-color);
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 8px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row span:first-child {
  font-weight: 500;
  color: var(--text-muted);
}

.detail-row span:last-child {
  font-weight: 600;
  color: var(--text-color);
}

/* User Dashboard Mobile Responsive */
@media (max-width: 768px) {
  .profile-section {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .cart-item {
    grid-template-columns: 1fr;
    gap: 15px;
    text-align: center;
  }

  .item-details,
  .item-total,
  .item-actions {
    text-align: center;
  }

  .quantity-controls {
    justify-content: center;
  }

  .cart-actions {
    flex-direction: column;
  }

  .service-grid {
    grid-template-columns: 1fr;
  }

  .transaction-item {
    padding: 20px;
  }

  .transaction-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    align-items: center;
  }

  .transaction-content {
    flex-direction: column;
    gap: 20px;
  }

  .service-info {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .service-image {
    width: 150px;
    height: 150px;
    margin: 0 auto;
  }

  .service-details h3 {
    font-size: 1.2rem;
    text-align: center;
  }

  .spec-item {
    justify-content: center;
    gap: 10px;
    text-align: center;
  }

  .transaction-actions {
    justify-content: center;
    min-width: auto;
  }

  .transaction-info {
    grid-template-columns: 1fr;
  }

  .transaction-actions {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }

  .filter-controls {
    flex-direction: column;
    gap: 15px;
  }

  .search-box {
    min-width: auto;
  }

  .transaction-stats {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .account-header h2,
  .basket-header h2,
  .history-header h2 {
    font-size: 1.5rem;
  }

  .form-section,
  .account-stats,
  .google-account-info {
    padding: 20px;
  }

  .account-container {
    padding: 0 15px;
  }

  .account-header {
    flex-direction: column;
    text-align: center;
    gap: 15px;
    padding: 25px 20px;
  }

  .account-avatar {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .form-actions {
    margin: 20px -20px -20px -20px;
    padding: 20px;
  }

  .profile-image img,
  .default-avatar {
    width: 80px;
    height: 80px;
  }

  .default-avatar {
    font-size: 2rem;
  }

  .cart-item,
  .transaction-item {
    padding: 15px;
  }

  .service-actions {
    flex-direction: column;
  }

  .btn-sm {
    min-width: auto;
    width: 100%;
  }

  .pagination {
    flex-wrap: wrap;
  }

  .page-btn {
    width: 35px;
    height: 35px;
    font-size: 0.85rem;
  }
}
