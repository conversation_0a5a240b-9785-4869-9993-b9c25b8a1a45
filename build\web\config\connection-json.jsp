<%--
    Document   : connection-json
    Created on : June 17, 2025
    Author     : Arqeta
    Description: Database connection configuration for JSON responses
--%>

<%@page import="java.sql.*"%>

<%
    // Database connection parameters
    String jdbcURL = "******************************************************************************************************";
    String dbUser = "root";
    String dbPassword = "";
    Connection conn = null;

    try {
        // Load MySQL JDBC driver
        Class.forName("com.mysql.cj.jdbc.Driver");

        // Create database connection
        conn = DriverManager.getConnection(jdbcURL, dbUser, dbPassword);
    } catch (ClassNotFoundException e) {
        System.out.println("Database Driver Error: " + e.getMessage());
    } catch (SQLException e) {
        System.out.println("Database Connection Error: " + e.getMessage());
    }
%>
