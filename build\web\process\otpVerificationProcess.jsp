<%--
    Document   : otpVerificationProcess
    Created on : June 16, 2025
    Author     : Arqeta
    Description: Process OTP verification for password reset
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.time.LocalDateTime"%>
<%@page import="java.time.format.DateTimeFormatter"%>

<%
    // Disable caching
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setHeader("Pragma", "no-cache");
    response.setDateHeader("Expires", 0);

    String email = request.getParameter("email");
    String otp = request.getParameter("otp");
    
    if (email == null || email.trim().isEmpty() || otp == null || otp.trim().isEmpty()) {
        session.setAttribute("message", "Email dan kode OTP tidak boleh kosong!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp");
        return;
    }

    Connection conn = null;
    PreparedStatement ps = null;
    ResultSet rs = null;

    try {
        // Load database connection
        Class.forName("com.mysql.cj.jdbc.Driver");
        conn = DriverManager.getConnection("******************************************************************************************************", "root", "");

        // Check if OTP is valid and not expired (valid for 10 minutes)
        ps = conn.prepareStatement(
            "SELECT id, created_at FROM otp_log WHERE email = ? AND otp = ? AND used = 0 " +
            "AND created_at >= DATE_SUB(NOW(), INTERVAL 10 MINUTE) ORDER BY created_at DESC LIMIT 1"
        );
        ps.setString(1, email);
        ps.setString(2, otp);
        rs = ps.executeQuery();

        if (rs.next()) {
            int otpId = rs.getInt("id");
            
            // Mark OTP as used
            ps.close();
            ps = conn.prepareStatement("UPDATE otp_log SET used = 1 WHERE id = ?");
            ps.setInt(1, otpId);
            int updateResult = ps.executeUpdate();
            
            if (updateResult > 0) {
                // OTP verified successfully
                session.setAttribute("message", "Kode OTP berhasil diverifikasi!");
                session.setAttribute("messageType", "success");
                session.setAttribute("verifiedEmail", email);
                session.setAttribute("verifiedOtp", otp);
                
                // Redirect to password reset step
                response.sendRedirect("../form/forgotpassword.jsp?step=reset&email=" + email + "&otp=" + otp);
            } else {
                session.setAttribute("message", "Gagal memverifikasi kode OTP. Silakan coba lagi.");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../form/forgotpassword.jsp?step=otp&email=" + email);
            }
        } else {
            // Invalid or expired OTP
            session.setAttribute("message", "Kode OTP tidak valid atau sudah kedaluwarsa!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../form/forgotpassword.jsp?step=otp&email=" + email);
        }

    } catch(ClassNotFoundException e) {
        System.out.println("MySQL Driver not found: " + e.getMessage());
        session.setAttribute("message", "Driver database tidak ditemukan!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp");
    } catch(SQLException e) {
        System.out.println("Database error: " + e.getMessage());
        e.printStackTrace();
        session.setAttribute("message", "Terjadi kesalahan database: " + e.getMessage());
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp");
    } catch(Exception e) {
        System.out.println("General error: " + e.getMessage());
        e.printStackTrace();
        session.setAttribute("message", "Terjadi kesalahan sistem: " + e.getMessage());
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp");
    } finally {
        // Close database connection
        try {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
            if (conn != null && !conn.isClosed()) {
                conn.close();
            }
        } catch (SQLException se) {
            se.printStackTrace();
        }
    }
%>
