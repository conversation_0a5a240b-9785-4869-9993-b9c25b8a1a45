<%--
    Document   : signupdata
    Created on : Jun 14, 2025
    Author     : Arqeta
    Description: Admin page untuk mengelola data registrasi admin dan user dengan fitur CRUD lengkap
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Mendapatkan tipe data yang dipilih (admin atau user)
    String dataType = request.getParameter("type");
    if (dataType == null) {
        dataType = "admin";
    }
%>

<div class="page-content">
    <div class="page-header">
        <h2>Data Register</h2>
        <div class="data-type-selector">
            <button class="selector-btn <%= dataType.equals("admin") ? "active" : "" %>" onclick="switchRegisterType('admin')" id="adminBtn">
                <i data-feather="shield"></i> Data Admin
            </button>
            <button class="selector-btn <%= dataType.equals("user") ? "active" : "" %>" onclick="switchRegisterType('user')" id="userBtn">
                <i data-feather="user"></i> Data User
            </button>
        </div>
    </div>

    <div class="action-bar">
        <button class="btn-primary" onclick="openAddAccountModal()">
            <i data-feather="plus"></i> Tambah Akun
        </button>
        <div class="search-bar">
            <input type="text" id="searchInput" placeholder="Cari berdasarkan nama, username, atau email..." onkeyup="searchData()">
            <i data-feather="search"></i>
        </div>
    </div>

    <div class="table-container">
        <div class="table-responsive">
            <table class="data-table" id="registerTable">
                <thead>
                    <tr>
                        <th onclick="sortTable(0)">ID <i data-feather="arrow-up-down"></i></th>
                        <th onclick="sortTable(1)">Nama <i data-feather="arrow-up-down"></i></th>
                        <th onclick="sortTable(2)">Username <i data-feather="arrow-up-down"></i></th>
                        <th onclick="sortTable(3)">Email <i data-feather="arrow-up-down"></i></th>
                        <th onclick="sortTable(4)">Waktu Pembuatan <i data-feather="arrow-up-down"></i></th>
                        <th onclick="sortTable(5)">Waktu Perubahan <i data-feather="arrow-up-down"></i></th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody id="registerTableBody">
                    <%
                        try {
                            // Query untuk mengambil data berdasarkan tipe yang dipilih, diurutkan berdasarkan ID
                            String query = "SELECT * FROM " + dataType + " ORDER BY id ASC";
                            PreparedStatement ps = conn.prepareStatement(query);
                            ResultSet rs = ps.executeQuery();

                            while (rs.next()) {
                    %>
                    <tr>
                        <td><%= rs.getInt("id") %></td>
                        <td><%= rs.getString("name") %></td>
                        <td><%= rs.getString("username") %></td>
                        <td><%= rs.getString("email") %></td>
                        <td><%= rs.getTimestamp("created_at") %></td>
                        <td><%= rs.getTimestamp("updated_at") %></td>
                        <td class="action-buttons">
                            <button class="btn-edit" onclick="openEditAccountModal(<%= rs.getInt("id") %>, '<%= rs.getString("name").replace("'", "\\'") %>', '<%= rs.getString("username") %>', '<%= rs.getString("email") %>', '<%= dataType %>')" title="Edit">
                                <i data-feather="edit"></i>
                            </button>
                            <button class="btn-delete" onclick="openDeleteModal(<%= rs.getInt("id") %>, '<%= dataType %>')" title="Hapus">
                                <i data-feather="trash-2"></i>
                            </button>
                        </td>
                    </tr>
                    <%
                            }
                            rs.close();
                            ps.close();
                        } catch (SQLException e) {
                            out.println("<tr><td colspan='7'>Error: " + e.getMessage() + "</td></tr>");
                        }
                    %>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// Fungsi untuk beralih antara data admin dan user
function switchRegisterType(type) {
    // Update status tombol
    document.getElementById('adminBtn').classList.toggle('active', type === 'admin');
    document.getElementById('userBtn').classList.toggle('active', type === 'user');

    // Muat ulang halaman dengan tipe baru
    window.location.href = '?page=register&type=' + type;
}

// Fungsi untuk mencari data
function searchData() {
    const input = document.getElementById('searchInput');
    const filter = input.value.toLowerCase();
    const table = document.getElementById('registerTable');
    const rows = table.getElementsByTagName('tr');

    for (let i = 1; i < rows.length; i++) {
        const cells = rows[i].getElementsByTagName('td');
        let found = false;

        // Cari di kolom nama, username, dan email
        for (let j = 1; j <= 3; j++) {
            if (cells[j] && cells[j].textContent.toLowerCase().includes(filter)) {
                found = true;
                break;
            }
        }

        rows[i].style.display = found ? '' : 'none';
    }
}

// Fungsi untuk mengurutkan tabel
function sortTable(columnIndex) {
    const table = document.getElementById('registerTable');
    const tbody = table.getElementsByTagName('tbody')[0];
    const rows = Array.from(tbody.getElementsByTagName('tr'));

    // Tentukan arah pengurutan
    const isAscending = table.getAttribute('data-sort-direction') !== 'asc';
    table.setAttribute('data-sort-direction', isAscending ? 'asc' : 'desc');

    // Urutkan baris
    rows.sort((a, b) => {
        const aValue = a.getElementsByTagName('td')[columnIndex].textContent.trim();
        const bValue = b.getElementsByTagName('td')[columnIndex].textContent.trim();

        // Penanganan pengurutan numerik untuk kolom ID
        if (columnIndex === 0) {
            return isAscending ? parseInt(aValue) - parseInt(bValue) : parseInt(bValue) - parseInt(aValue);
        }

        // Penanganan pengurutan tanggal untuk kolom timestamp
        if (columnIndex === 4 || columnIndex === 5) {
            const aDate = new Date(aValue);
            const bDate = new Date(bValue);
            return isAscending ? aDate - bDate : bDate - aDate;
        }

        // Pengurutan string
        return isAscending ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    });

    // Tambahkan kembali baris yang sudah diurutkan
    rows.forEach(row => tbody.appendChild(row));

    // Update indikator pengurutan
    updateSortIndicators(columnIndex, isAscending);
}

// Fungsi untuk memperbarui indikator pengurutan
function updateSortIndicators(activeColumn, isAscending) {
    const headers = document.querySelectorAll('#registerTable th');
    headers.forEach((header, index) => {
        const icon = header.querySelector('i');
        if (icon) {
            if (index === activeColumn) {
                icon.setAttribute('data-feather', isAscending ? 'arrow-up' : 'arrow-down');
            } else {
                icon.setAttribute('data-feather', 'arrow-up-down');
            }
        }
    });
    feather.replace();
}

// Inisialisasi ikon feather saat halaman dimuat
document.addEventListener('DOMContentLoaded', function() {
    feather.replace();

    // Pastikan search icon terlihat dengan baik dan berada di dalam search bar
    setTimeout(function() {
        const searchIcon = document.querySelector('.search-bar i[data-feather="search"]');
        const searchBar = document.querySelector('.search-bar');
        const searchInput = document.querySelector('.search-bar input');

        if (searchIcon && searchBar && searchInput) {
            // Pastikan styling sesuai dengan desain yang diinginkan
            searchBar.style.position = 'relative';
            searchBar.style.display = 'inline-block';
            searchBar.style.width = 'fit-content';

            // Pastikan input memiliki styling yang benar
            searchInput.style.paddingRight = '45px';
            searchInput.style.paddingLeft = '20px';
            searchInput.style.border = '1px solid #ddd';
            searchInput.style.borderRadius = '25px';
            searchInput.style.background = '#ffffff';
            searchInput.style.color = '#333';
            searchInput.style.fontSize = '14px';
            searchInput.style.fontFamily = 'Quicksand, sans-serif';
            searchInput.style.fontWeight = '400';

            // Pastikan icon positioned dengan benar di dalam search bar
            searchIcon.style.position = 'absolute';
            searchIcon.style.right = '15px';
            searchIcon.style.top = '50%';
            searchIcon.style.transform = 'translateY(-50%)';
            searchIcon.style.zIndex = '2';
            searchIcon.style.display = 'flex';
            searchIcon.style.alignItems = 'center';
            searchIcon.style.justifyContent = 'center';
            searchIcon.style.pointerEvents = 'none';
            searchIcon.style.color = '#666';
            searchIcon.style.width = '18px';
            searchIcon.style.height = '18px';
            searchIcon.style.strokeWidth = '2';

            console.log('Search icon positioned successfully with new design');
        }
    }, 100);
});
</script>
