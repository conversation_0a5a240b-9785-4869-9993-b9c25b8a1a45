<%--
    Document   : getBlogDetails
    Created on : Jun 14, 2025
    Author     : Arqeta
    Description: Process untuk mengambil detail blog
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../config/connection.jsp" %>

<%
    String blogId = request.getParameter("id");
    
    if (blogId == null || blogId.trim().isEmpty()) {
        out.println("<div class='error-message'>ID blog tidak valid</div>");
        return;
    }

    try {
        PreparedStatement ps = conn.prepareStatement("SELECT * FROM blog WHERE id = ?");
        ps.setInt(1, Integer.parseInt(blogId));
        ResultSet rs = ps.executeQuery();

        if (rs.next()) {
%>
            <div class="blog-detail">
                <div class="blog-detail-header">
                    <h2><%= rs.getString("title") %></h2>
                    <div class="blog-detail-meta">
                        <span class="blog-date">
                            <i data-feather="calendar"></i>
                            <%= rs.getTimestamp("created_at") %>
                        </span>
                    </div>
                </div>
                
                <div class="blog-detail-image">
                    <img src="../dist/img/<%= rs.getString("image") %>" alt="<%= rs.getString("title") %>">
                </div>
                
                <div class="blog-detail-content">
                    <%= rs.getString("content").replaceAll("\n", "<br>") %>
                </div>
                
                <div class="blog-detail-footer">
                    <div class="blog-tags">
                        <span class="tag">UI/UX</span>
                        <span class="tag">Design</span>
                        <span class="tag">Web Development</span>
                    </div>
                    
                    <div class="blog-share">
                        <span>Bagikan:</span>
                        <a href="#" class="share-link" onclick="shareToSocial('facebook')">
                            <i data-feather="facebook"></i>
                        </a>
                        <a href="#" class="share-link" onclick="shareToSocial('twitter')">
                            <i data-feather="twitter"></i>
                        </a>
                        <a href="#" class="share-link" onclick="shareToSocial('linkedin')">
                            <i data-feather="linkedin"></i>
                        </a>
                    </div>
                </div>
            </div>

            <script>
                // Initialize feather icons for the modal content
                if (typeof feather !== 'undefined') {
                    feather.replace();
                }
                
                function shareToSocial(platform) {
                    const title = '<%= rs.getString("title").replaceAll("'", "\\'") %>';
                    const url = window.location.href;
                    
                    let shareUrl = '';
                    
                    switch(platform) {
                        case 'facebook':
                            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
                            break;
                        case 'twitter':
                            shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`;
                            break;
                        case 'linkedin':
                            shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
                            break;
                    }
                    
                    if (shareUrl) {
                        window.open(shareUrl, '_blank', 'width=600,height=400');
                    }
                }
            </script>
<%
        } else {
            out.println("<div class='error-message'>Blog tidak ditemukan</div>");
        }
        
        rs.close();
        ps.close();
        
    } catch (NumberFormatException e) {
        out.println("<div class='error-message'>ID blog tidak valid</div>");
    } catch (SQLException e) {
        out.println("<div class='error-message'>Terjadi kesalahan database: " + e.getMessage() + "</div>");
    } catch (Exception e) {
        out.println("<div class='error-message'>Terjadi kesalahan: " + e.getMessage() + "</div>");
    }
%>
