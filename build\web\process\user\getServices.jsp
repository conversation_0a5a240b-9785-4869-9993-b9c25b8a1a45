<%--
    Document   : getServices
    Created on : Jun 16, 2025
    Author     : Arqeta
    Description: Fetch available services for cart modal
--%>

<%@page contentType="application/json" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.math.BigDecimal"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Set response type to JSON
    response.setContentType("application/json");
    response.setCharacterEncoding("UTF-8");

    String jsonResponse = "";

    try {
        // Check if user is logged in
        String userId = (String) session.getAttribute("userId");
        Boolean isLoggedIn = (Boolean) session.getAttribute("isLoggedIn");

        if (userId == null || isLoggedIn == null || !isLoggedIn) {
            jsonResponse = "{\"success\": false, \"message\": \"Anda harus login terlebih dahulu\"}";
            out.print(jsonResponse);
            return;
        }

        // Get all available services
        PreparedStatement psServices = conn.prepareStatement(
            "SELECT id, name, images, quantity, price, created_at, updated_at FROM services ORDER BY name ASC"
        );
        ResultSet rsServices = psServices.executeQuery();

        StringBuilder servicesJson = new StringBuilder();
        servicesJson.append("[");

        boolean first = true;
        int totalServices = 0;

        while (rsServices.next()) {
            if (!first) {
                servicesJson.append(",");
            }
            first = false;
            totalServices++;

            int serviceId = rsServices.getInt("id");
            String serviceName = rsServices.getString("name");
            String serviceImages = rsServices.getString("images");
            int serviceQuantity = rsServices.getInt("quantity");
            BigDecimal servicePrice = rsServices.getBigDecimal("price");

            // Check if service is already in user's cart
            PreparedStatement psCartCheck = conn.prepareStatement(
                "SELECT quantity FROM cart WHERE user_id = ? AND service_id = ?"
            );
            psCartCheck.setInt(1, Integer.parseInt(userId));
            psCartCheck.setInt(2, serviceId);
            ResultSet rsCartCheck = psCartCheck.executeQuery();

            boolean inCart = false;
            int cartQuantity = 0;
            if (rsCartCheck.next()) {
                inCart = true;
                cartQuantity = rsCartCheck.getInt("quantity");
            }
            rsCartCheck.close();
            psCartCheck.close();

            // Build service JSON
            servicesJson.append("{");
            servicesJson.append("\"id\": ").append(serviceId).append(",");
            servicesJson.append("\"name\": \"").append(serviceName.replace("\"", "\\\"")).append("\",");
            servicesJson.append("\"images\": \"").append(serviceImages != null ? serviceImages.replace("\"", "\\\"") : "").append("\",");
            servicesJson.append("\"quantity\": ").append(serviceQuantity).append(",");
            servicesJson.append("\"price\": ").append(servicePrice).append(",");
            servicesJson.append("\"inCart\": ").append(inCart).append(",");
            servicesJson.append("\"cartQuantity\": ").append(cartQuantity);
            servicesJson.append("}");
        }
        servicesJson.append("]");

        rsServices.close();
        psServices.close();

        jsonResponse = "{\"success\": true, \"services\": " + servicesJson.toString() + ", \"totalServices\": " + totalServices + "}";

    } catch (SQLException e) {
        jsonResponse = "{\"success\": false, \"message\": \"Terjadi kesalahan database: " + e.getMessage().replace("\"", "\\\"") + "\"}";
        e.printStackTrace();
    } catch (Exception e) {
        jsonResponse = "{\"success\": false, \"message\": \"Terjadi kesalahan sistem: " + e.getMessage().replace("\"", "\\\"") + "\"}";
        e.printStackTrace();
    } finally {
        // Close database connection
        try {
            if (conn != null && !conn.isClosed()) {
                conn.close();
            }
        } catch (SQLException se) {
            se.printStackTrace();
        }
    }

    out.print(jsonResponse);
%>
