<%--
    Document   : getPortfolioDetails
    Created on : Jun 15, 2025
    Author     : Arqeta
    Description: Get portfolio details for modal view
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<%
    String idStr = request.getParameter("id");
    
    if (idStr != null && !idStr.trim().isEmpty()) {
        try {
            int id = Integer.parseInt(idStr);
            
            PreparedStatement ps = conn.prepareStatement("SELECT * FROM portfolio WHERE id = ?");
            ps.setInt(1, id);
            ResultSet rs = ps.executeQuery();
            
            if (rs.next()) {
                String name = rs.getString("name");
                String image = rs.getString("image");
                String description = rs.getString("description");
                String createdAt = rs.getString("created_at");
                String updatedAt = rs.getString("updated_at");
%>
                <div class="portfolio-detail">
                    <div class="portfolio-image">
                        <% if (image != null && !image.isEmpty()) { %>
                            <img src="../dist/img/<%= image %>" alt="<%= name %>" 
                                 onerror="this.src='../dist/img/default-portfolio.png';">
                        <% } else { %>
                            <div class="no-image-placeholder">
                                <i data-feather="image"></i>
                                <p>Tidak ada gambar</p>
                            </div>
                        <% } %>
                    </div>
                    <div class="portfolio-info">
                        <h4><%= name %></h4>
                        <div class="portfolio-meta">
                            <p><strong>Dibuat:</strong> <%= createdAt %></p>
                            <p><strong>Diperbarui:</strong> <%= updatedAt %></p>
                        </div>
                        <div class="portfolio-description">
                            <h5>Deskripsi:</h5>
                            <p><%= description %></p>
                        </div>
                    </div>
                </div>
<%
            } else {
                out.println("<p>Portfolio tidak ditemukan.</p>");
            }
            
            rs.close();
            ps.close();
            
        } catch (NumberFormatException e) {
            out.println("<p>ID portfolio tidak valid.</p>");
        } catch (SQLException e) {
            out.println("<p>Error: " + e.getMessage() + "</p>");
        }
    } else {
        out.println("<p>ID portfolio tidak diberikan.</p>");
    }
%>
