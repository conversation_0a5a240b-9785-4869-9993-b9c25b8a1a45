<%--
    Document   : editPortfolio
    Created on : Jun 15, 2025
    Author     : Arqeta
    Description: Process untuk mengedit portfolio
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>
<%@page import="java.io.*"%>
<%@page import="java.nio.file.*"%>
<%@page import="jakarta.servlet.http.Part"%>

<%
    // Pastikan request adalah POST
    if (!"POST".equalsIgnoreCase(request.getMethod())) {
        response.sendRedirect("../../dashboardadmin.jsp?page=portfolio");
        return;
    }

    try {
        // Ambil parameter dari form
        String idStr = request.getParameter("id");
        String name = request.getParameter("name");
        String description = request.getParameter("description");
        
        // Validasi input
        if (idStr == null || idStr.trim().isEmpty() ||
            name == null || name.trim().isEmpty() || 
            description == null || description.trim().isEmpty()) {
            session.setAttribute("message", "Semua field harus diisi!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../dashboardadmin.jsp?page=portfolio");
            return;
        }

        int id = Integer.parseInt(idStr);

        // Ambil data portfolio yang ada
        String currentImage = null;
        PreparedStatement psSelect = conn.prepareStatement("SELECT image FROM portfolio WHERE id = ?");
        psSelect.setInt(1, id);
        ResultSet rs = psSelect.executeQuery();
        if (rs.next()) {
            currentImage = rs.getString("image");
        }
        rs.close();
        psSelect.close();

        // Handle file upload jika ada
        String fileName = currentImage; // Default menggunakan gambar yang sudah ada
        Part filePart = request.getPart("image");
        
        if (filePart != null && filePart.getSize() > 0) {
            // Validasi tipe file
            String contentType = filePart.getContentType();
            if (!contentType.startsWith("image/")) {
                session.setAttribute("message", "File harus berupa gambar!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboardadmin.jsp?page=portfolio");
                return;
            }

            // Validasi ukuran file (maksimal 10MB)
            if (filePart.getSize() > 10 * 1024 * 1024) {
                session.setAttribute("message", "Ukuran file maksimal 10MB!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboardadmin.jsp?page=portfolio");
                return;
            }

            // Generate nama file unik
            String originalFileName = filePart.getSubmittedFileName();
            String fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
            fileName = "portfolio_" + System.currentTimeMillis() + fileExtension;

            // Path untuk menyimpan file
            String uploadPath = application.getRealPath("/dist/img/");
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            // Hapus file lama jika ada
            if (currentImage != null && !currentImage.isEmpty()) {
                File oldFile = new File(uploadPath + File.separator + currentImage);
                if (oldFile.exists()) {
                    oldFile.delete();
                }
            }

            // Simpan file baru
            String filePath = uploadPath + File.separator + fileName;
            try (InputStream input = filePart.getInputStream()) {
                Files.copy(input, Paths.get(filePath), StandardCopyOption.REPLACE_EXISTING);
            }
        }

        // Update database
        PreparedStatement ps = conn.prepareStatement(
            "UPDATE portfolio SET name = ?, image = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        );
        ps.setString(1, name.trim());
        ps.setString(2, fileName);
        ps.setString(3, description.trim());
        ps.setInt(4, id);

        int result = ps.executeUpdate();
        ps.close();

        if (result > 0) {
            session.setAttribute("message", "Portfolio berhasil diperbarui!");
            session.setAttribute("messageType", "success");
        } else {
            session.setAttribute("message", "Gagal memperbarui portfolio!");
            session.setAttribute("messageType", "error");
        }

    } catch (NumberFormatException e) {
        session.setAttribute("message", "ID portfolio tidak valid!");
        session.setAttribute("messageType", "error");
    } catch (Exception e) {
        session.setAttribute("message", "Error: " + e.getMessage());
        session.setAttribute("messageType", "error");
        e.printStackTrace();
    }

    response.sendRedirect("../../dashboardadmin.jsp?page=portfolio");
%>
