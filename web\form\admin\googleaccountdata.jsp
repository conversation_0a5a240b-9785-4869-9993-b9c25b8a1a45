<%--
    Document    : googleaccountdata
    Created on  : Jun 14, 2025
    Author      : Arqeta
    Description : Admin page untuk mengelola data akun Google admin dan user dengan fitur CRUD
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Mendapatkan tipe data yang dipilih (admin atau user)
    String dataType = request.getParameter("type");
    if (dataType == null) {
        dataType = "admin";
    }

    // Tentukan nama tabel berdasarkan tipe
    String tableName = dataType + "_google";
%>

<div class="page-content">
    <div class="page-header">
        <h2>Data Akun Google</h2>
        <div class="data-type-selector">
            <button class="selector-btn <%= dataType.equals("admin") ? "active" : "" %>" onclick="switchGoogleType('admin')" id="googleAdminBtn">
                <i data-feather="shield"></i> Data Admin Google
            </button>
            <button class="selector-btn <%= dataType.equals("user") ? "active" : "" %>" onclick="switchGoogleType('user')" id="googleUserBtn">
                <i data-feather="user"></i> Data User Google
            </button>
        </div>
    </div>

    <div class="action-bar">
        <button class="btn-primary" onclick="openAddGoogleAccountModal()">
            <i data-feather="plus"></i> Tambah Akun Google
        </button>
        <div class="search-bar">
            <input type="text" id="searchInput" placeholder="Cari berdasarkan nama atau email..." onkeyup="searchData()">
            <i data-feather="search"></i>
        </div>
    </div>

    <div class="table-container">
        <div class="table-responsive">
            <table class="data-table" id="googleTable">
                <thead>
                    <tr>
                        <th onclick="sortTable(0)">ID <i data-feather="arrow-up-down"></i></th>
                        <th onclick="sortTable(1)">Nama <i data-feather="arrow-up-down"></i></th>
                        <th onclick="sortTable(2)">Email <i data-feather="arrow-up-down"></i></th>
                        <th onclick="sortTable(3)"><%= dataType.equals("admin") ? "Admin ID" : "User ID" %> <i data-feather="arrow-up-down"></i></th>
                        <th>Foto</th>
                        <th onclick="sortTable(5)">Waktu Pembuatan <i data-feather="arrow-up-down"></i></th>
                        <th onclick="sortTable(6)">Terakhir Login <i data-feather="arrow-up-down"></i></th>
                        <th onclick="sortTable(7)">Waktu Perubahan <i data-feather="arrow-up-down"></i></th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody id="googleTableBody">
                    <%
                        try {
                            // Query untuk mengambil data berdasarkan tipe yang dipilih, diurutkan berdasarkan ID
                            String query = "SELECT * FROM " + tableName + " ORDER BY id ASC";
                            PreparedStatement ps = conn.prepareStatement(query);
                            ResultSet rs = ps.executeQuery();

                            while (rs.next()) {
                                String idColumn = dataType.equals("admin") ? "admin_id" : "user_id";
                    %>
                    <tr>
                        <td><%= rs.getInt("id") %></td>
                        <td><%= rs.getString("name") %></td>
                        <td><%= rs.getString("email") %></td>
                        <td><%= rs.getString(idColumn) %></td>
                        <td>
                            <% if (rs.getString("photo_url") != null && !rs.getString("photo_url").isEmpty()) { %>
                                <img src="<%= rs.getString("photo_url") %>" alt="Foto Profil" class="profile-photo" onerror="this.src='../../dist/img/default-avatar.png';">
                            <% } else { %>
                                <span class="no-photo">Tidak ada foto</span>
                            <% } %>
                        </td>
                        <td><%= rs.getTimestamp("created_at") %></td>
                        <td><%= rs.getTimestamp("last_login") != null ? rs.getTimestamp("last_login") : "Belum pernah login" %></td>
                        <td><%= rs.getTimestamp("updated_at") %></td>
                        <td class="action-buttons">
                            <button class="btn-delete" onclick="openDeleteGoogleModal(<%= rs.getInt("id") %>, '<%= dataType %>', '<%= rs.getString("name").replace("'", "\\'") %>')" title="Hapus">
                                <i data-feather="trash-2"></i>
                            </button>
                        </td>
                    </tr>
                    <%
                            }
                            rs.close();
                            ps.close();
                        } catch (SQLException e) {
                            out.println("<tr><td colspan='9'>Error: " + e.getMessage() + "</td></tr>");
                        }
                    %>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Konfirmasi Hapus -->
<div id="deleteGoogleModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Konfirmasi Hapus</h3>
            <span class="close-modal" onclick="closeModal('deleteGoogleModal')">&times;</span>
        </div>
        <div class="modal-body">
            <p>Apakah Anda yakin ingin menghapus akun Google "<span id="deleteGoogleName"></span>"?</p>
            <p class="warning-text">Tindakan ini tidak dapat dibatalkan.</p>
        </div>
        <div class="modal-actions">
            <button type="button" class="btn-secondary" onclick="closeModal('deleteGoogleModal')">Batal</button>
            <button type="button" class="btn-danger" onclick="confirmDeleteGoogle()">Hapus</button>
        </div>
    </div>
</div>

<script>
    let deleteGoogleId = null;
    let deleteGoogleType = null;

    // Fungsi untuk beralih antara data admin dan user Google
    function switchGoogleType(type) {
        // Update status tombol
        document.getElementById('googleAdminBtn').classList.toggle('active', type === 'admin');
        document.getElementById('googleUserBtn').classList.toggle('active', type === 'user');

        // Muat ulang halaman dengan tipe baru
        window.location.href = '?page=google_accounts&type=' + type;
    }

    // Fungsi untuk membuka modal tambah akun Google
    function openAddGoogleAccountModal() {
        console.log('openAddGoogleAccountModal called from googleaccountdata.jsp');

        try {
            // Buka popup Google login untuk menambah akun
            openGoogleLoginPopup();
        } catch (error) {
            console.error('Error opening Google login popup:', error);
            showNotificationSafe('Terjadi kesalahan: ' + error.message, 'error');
        }
    }

    // Fungsi untuk membuka modal hapus akun Google
    function openDeleteGoogleModal(id, type, name) {
        deleteGoogleId = id;
        deleteGoogleType = type;
        document.getElementById('deleteGoogleName').textContent = name;
        document.getElementById('deleteGoogleModal').style.display = 'block';
    }

    // Fungsi untuk konfirmasi hapus akun Google
    function confirmDeleteGoogle() {
        if (deleteGoogleId && deleteGoogleType) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '../../process/admin/deleteGoogleAccount.jsp';

            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'id';
            idInput.value = deleteGoogleId;

            const typeInput = document.createElement('input');
            typeInput.type = 'hidden';
            typeInput.name = 'type';
            typeInput.value = deleteGoogleType;

            form.appendChild(idInput);
            form.appendChild(typeInput);
            document.body.appendChild(form);
            form.submit();
        }
    }

    // Fungsi untuk mencari data
    function searchData() {
        const input = document.getElementById('searchInput');
        const filter = input.value.toLowerCase();
        const table = document.getElementById('googleTable');
        const rows = table.getElementsByTagName('tr');

        for (let i = 1; i < rows.length; i++) {
            const cells = rows[i].getElementsByTagName('td');
            let found = false;

            // Cari di kolom nama dan email
            for (let j = 1; j <= 2; j++) {
                if (cells[j] && cells[j].textContent.toLowerCase().includes(filter)) {
                    found = true;
                    break;
                }
            }

            rows[i].style.display = found ? '' : 'none';
        }
    }

    // Fungsi untuk mengurutkan tabel
    function sortTable(columnIndex) {
        const table = document.getElementById('googleTable');
        const tbody = table.getElementsByTagName('tbody')[0];
        const rows = Array.from(tbody.getElementsByTagName('tr'));

        // Tentukan arah pengurutan
        const isAscending = table.getAttribute('data-sort-direction') !== 'asc';
        table.setAttribute('data-sort-direction', isAscending ? 'asc' : 'desc');

        // Urutkan baris
        rows.sort((a, b) => {
            const aValue = a.getElementsByTagName('td')[columnIndex].textContent.trim();
            const bValue = b.getElementsByTagName('td')[columnIndex].textContent.trim();

            // Penanganan pengurutan numerik untuk kolom ID
            if (columnIndex === 0 || columnIndex === 3) {
                return isAscending ? parseInt(aValue) - parseInt(bValue) : parseInt(bValue) - parseInt(aValue);
            }

            // Penanganan pengurutan tanggal untuk kolom timestamp
            if (columnIndex === 5 || columnIndex === 6 || columnIndex === 7) {
                const aDate = new Date(aValue);
                const bDate = new Date(bValue);
                return isAscending ? aDate - bDate : bDate - aDate;
            }

            // Pengurutan string
            return isAscending ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
        });

        // Tambahkan kembali baris yang sudah diurutkan
        rows.forEach(row => tbody.appendChild(row));

        // Update indikator pengurutan
        updateSortIndicators(columnIndex, isAscending);
    }

    // Fungsi untuk memperbarui indikator pengurutan
    function updateSortIndicators(activeColumn, isAscending) {
        const headers = document.querySelectorAll('#googleTable th');
        headers.forEach((header, index) => {
            const icon = header.querySelector('i');
            if (icon) {
                if (index === activeColumn) {
                    icon.setAttribute('data-feather', isAscending ? 'arrow-up' : 'arrow-down');
                } else {
                    icon.setAttribute('data-feather', 'arrow-up-down');
                }
            }
        });
        feather.replace();
    }

    // Fungsi untuk menutup modal
    function closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }

    // Tutup modal ketika klik di luar modal
    window.onclick = function(event) {
        const deleteModal = document.getElementById('deleteGoogleModal');
        const googleLoginModal = document.getElementById('googleLoginModal');

        if (event.target === deleteModal) {
            closeModal('deleteGoogleModal');
        }

        if (event.target === googleLoginModal) {
            closeGoogleLoginModal();
        }
    }

    // Fungsi untuk membuka popup Google login
    function openGoogleLoginPopup() {
        // Dapatkan tipe akun saat ini dari URL atau default ke admin
        const urlParams = new URLSearchParams(window.location.search);
        const currentType = urlParams.get('type') || 'admin';
        selectedAccountType = currentType;

        // Buat modal popup untuk Google login
        const modal = document.createElement('div');
        modal.id = 'googleLoginModal';
        modal.className = 'modal';
        modal.style.display = 'block';
        modal.style.zIndex = '9999';
        modal.style.position = 'fixed';
        modal.style.top = '0';
        modal.style.left = '0';
        modal.style.width = '100%';
        modal.style.height = '100%';
        modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';

        modal.innerHTML =
            '<div class="modal-content google-login-modal">' +
                '<div class="modal-header">' +
                    '<h3>Tambah Akun Google</h3>' +
                    '<span class="close-modal" onclick="closeGoogleLoginModal()">&times;</span>' +
                '</div>' +
                '<div class="modal-body">' +
                    '<p>Pilih tipe akun yang akan ditambahkan:</p>' +
                    '<div class="account-type-selector">' +
                        '<button class="selector-btn ' + (currentType === 'admin' ? 'active' : '') + '" onclick="selectAccountType(\'admin\')" id="adminTypeBtn">' +
                            '<i data-feather="shield"></i> Admin Google' +
                        '</button>' +
                        '<button class="selector-btn ' + (currentType === 'user' ? 'active' : '') + '" onclick="selectAccountType(\'user\')" id="userTypeBtn">' +
                            '<i data-feather="user"></i> User Google' +
                        '</button>' +
                    '</div>' +
                    '<div class="google-login-section">' +
                        '<div id="googleLoginBtn" class="btn-google">' +
                            '<img src="dist/svg/googleicon.svg" alt="Google" onerror="this.style.display=\'none\'; this.nextElementSibling.style.marginLeft=\'0\';" />' +
                            '<span>Login dengan Google</span>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>' +
            '<div id="loadingOverlay" class="loading-overlay" style="display: none;">' +
                '<div class="loading-spinner"></div>' +
                '<p>Menghubungkan dengan Google...</p>' +
            '</div>';

        document.body.appendChild(modal);
        feather.replace();

        // Initialize Firebase if not already done
        initializeFirebaseForAdmin()
            .then(() => {
                // Set up Google login click handler after Firebase is ready
                document.getElementById('googleLoginBtn').addEventListener('click', handleGoogleLogin);
            })
            .catch((error) => {
                console.error('Firebase initialization failed:', error);
                showNotificationSafe('Gagal menginisialisasi Firebase: ' + error.message, 'error');
            });
    }

    // Variabel untuk menyimpan tipe akun yang dipilih
    let selectedAccountType = 'admin';

    // Fungsi untuk memilih tipe akun
    function selectAccountType(type) {
        selectedAccountType = type;
        document.getElementById('adminTypeBtn').classList.toggle('active', type === 'admin');
        document.getElementById('userTypeBtn').classList.toggle('active', type === 'user');
    }

    // Fungsi untuk menutup modal Google login
    function closeGoogleLoginModal() {
        const modal = document.getElementById('googleLoginModal');
        if (modal) {
            modal.remove();
        }
    }

    // Firebase configuration dan initialization untuk admin
    function initializeFirebaseForAdmin() {
        return new Promise((resolve, reject) => {
            if (typeof firebase === 'undefined') {
                reject(new Error('Firebase scripts not loaded'));
                return;
            }

            if (!firebase.apps.length) {
                initFirebaseConfig();
            }

            resolve();
        });
    }

    function initFirebaseConfig() {
        const firebaseConfig = {
            apiKey: "AIzaSyDF1xa0UEu0wTCYzDPdC0AFJKY5Y5PjBhc",
            authDomain: "thearqeta.firebaseapp.com",
            projectId: "thearqeta",
            storageBucket: "thearqeta.firebasestorage.app",
            messagingSenderId: "560559961893",
            appId: "1:560559961893:web:6d51f4b364c763bfcc4ccc",
            measurementId: "G-BPLQBHFFX1"
        };

        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }
    }

    // Fungsi untuk menangani Google login
    function handleGoogleLogin() {
        console.log('Google login clicked');
        const loadingOverlay = document.getElementById('loadingOverlay');
        loadingOverlay.style.display = 'flex';

        try {
            console.log('Creating Google provider');
            const googleProvider = new firebase.auth.GoogleAuthProvider();
            googleProvider.addScope('profile');
            googleProvider.addScope('email');

            firebase.auth()
                .signInWithPopup(googleProvider)
                .then(function(result) {
                    const user = result.user;
                    const name = user.displayName;
                    const email = user.email;
                    const photoURL = user.photoURL;
                    const uid = user.uid;

                    // Kirim data ke server untuk menambah akun Google
                    window.location.href = 'process/admin/addGoogleAccountNew.jsp' +
                        '?name=' + encodeURIComponent(name) +
                        '&email=' + encodeURIComponent(email) +
                        '&photoURL=' + encodeURIComponent(photoURL) +
                        '&uid=' + encodeURIComponent(uid) +
                        '&type=' + selectedAccountType;
                })
                .catch(function(error) {
                    loadingOverlay.style.display = 'none';
                    showNotificationSafe('Login gagal: ' + error.message, 'error');
                    console.error("Google Sign-In Error", error);
                });
        } catch (error) {
            loadingOverlay.style.display = 'none';
            showNotificationSafe('Firebase belum siap: ' + error.message, 'error');
            console.error("Firebase Error", error);
        }
    }

    // Fungsi notifikasi yang aman - akan menggunakan showNotification jika tersedia, atau alert sebagai fallback
    function showNotificationSafe(message, type) {
        // Try to use the main dashboard's showNotification function
        if (typeof window.showNotification === 'function') {
            window.showNotification(message, type);
        } else if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else if (window.parent && typeof window.parent.showNotification === 'function') {
            window.parent.showNotification(message, type);
        } else {
            // Fallback ke alert jika showNotification tidak tersedia
            alert(message);
        }
    }

    // Inisialisasi ikon feather saat halaman dimuat
    document.addEventListener('DOMContentLoaded', function() {
        feather.replace();

        // Pastikan search icon terlihat dengan baik dan berada di dalam search bar
        setTimeout(function() {
            const searchIcon = document.querySelector('.search-bar i[data-feather="search"]');
            const searchBar = document.querySelector('.search-bar');
            const searchInput = document.querySelector('.search-bar input');

            if (searchIcon && searchBar && searchInput) {
                // Pastikan styling sesuai dengan desain yang diinginkan
                searchBar.style.position = 'relative';
                searchBar.style.display = 'inline-block';
                searchBar.style.width = 'fit-content';

                // Pastikan input memiliki styling yang benar
                searchInput.style.paddingRight = '45px';
                searchInput.style.paddingLeft = '20px';
                searchInput.style.border = '1px solid #ddd';
                searchInput.style.borderRadius = '25px';
                searchInput.style.background = '#ffffff';
                searchInput.style.color = '#333';
                searchInput.style.fontSize = '14px';
                searchInput.style.fontFamily = 'Quicksand, sans-serif';
                searchInput.style.fontWeight = '400';

                // Pastikan icon positioned dengan benar di dalam search bar
                searchIcon.style.position = 'absolute';
                searchIcon.style.right = '15px';
                searchIcon.style.top = '50%';
                searchIcon.style.transform = 'translateY(-50%)';
                searchIcon.style.zIndex = '2';
                searchIcon.style.display = 'flex';
                searchIcon.style.alignItems = 'center';
                searchIcon.style.justifyContent = 'center';
                searchIcon.style.pointerEvents = 'none';
                searchIcon.style.color = '#666';
                searchIcon.style.width = '18px';
                searchIcon.style.height = '18px';
                searchIcon.style.strokeWidth = '2';

                console.log('Search icon positioned successfully with new design');
            }
        }, 100);

        // Test Firebase availability
        console.log('Firebase available:', typeof firebase !== 'undefined');
        if (typeof firebase !== 'undefined') {
            console.log('Firebase apps:', firebase.apps.length);
        }
    });
</script>

<style>
    /* Styling khusus untuk halaman Google accounts */
    .profile-photo {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid var(--border-color);
    }

    .no-photo {
        color: var(--text-muted);
        font-style: italic;
        font-size: 0.9rem;
    }

    .data-type-selector {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    .selector-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px 20px;
        border: 2px solid var(--border-color);
        background: var(--card-bg);
        color: var(--text-color);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .selector-btn:hover {
        border-color: var(--accent-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--shadow-color);
    }

    .selector-btn.active {
        background: var(--accent-color);
        color: white;
        border-color: var(--accent-color);
    }

    /* Search Bar - Desain sesuai gambar "desain yang diinginkan.png" */
    .search-bar {
        position: relative;
        display: inline-block;
        width: fit-content;
    }

    .search-bar input {
        padding: 12px 45px 12px 20px;
        border: 1px solid #ddd;
        border-radius: 25px;
        background: #ffffff;
        color: #333;
        font-size: 14px;
        width: 300px;
        height: 44px;
        box-sizing: border-box;
        outline: none;
        transition: all 0.3s ease;
        font-family: 'Quicksand', sans-serif;
        font-weight: 400;
    }

    .search-bar input:focus {
        border-color: #999;
        box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
    }

    .search-bar input::placeholder {
        color: #999;
        font-size: 14px;
    }

    .search-bar i {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
        pointer-events: none;
        z-index: 2;
        width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        stroke-width: 2;
    }

    /* Hover effect untuk search bar */
    .search-bar:hover input {
        border-color: #666;
    }

    .search-bar:hover i {
        color: #333;
    }

    /* Styling khusus untuk memastikan icon search berada di dalam input */
    .search-bar {
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    .search-bar input::placeholder {
        color: var(--text-muted);
        opacity: 0.7;
    }

    /* Pastikan icon tidak terpotong */
    .search-bar i {
        min-width: 18px;
        min-height: 18px;
        flex-shrink: 0;
    }

    /* Hover effect untuk search bar */
    .search-bar:hover input {
        border-color: var(--accent-color);
    }

    /* Focus state yang lebih jelas */
    .search-bar input:focus {
        border-color: var(--accent-color) !important;
        box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.1) !important;
    }

    .warning-text {
        color: var(--danger-color);
        font-size: 0.9rem;
        margin-top: 10px;
    }

    /* Styling untuk Google login modal */
    .google-login-modal {
        max-width: 500px;
        width: 90%;
    }

    .account-type-selector {
        display: flex;
        gap: 10px;
        margin: 20px 0;
        justify-content: center;
    }

    .account-type-selector .selector-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        border: 2px solid var(--border-color);
        background: var(--card-bg);
        color: var(--text-color);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
        font-size: 0.9rem;
    }

    .account-type-selector .selector-btn:hover {
        border-color: var(--accent-color);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px var(--shadow-color);
    }

    .account-type-selector .selector-btn.active {
        background: var(--accent-color);
        color: white;
        border-color: var(--accent-color);
    }

    .google-login-section {
        text-align: center;
        margin-top: 30px;
    }

    .btn-google {
        display: inline-flex;
        align-items: center;
        gap: 12px;
        padding: 12px 24px;
        background: white;
        border: 2px solid #dadce0;
        border-radius: var(--border-radius);
        color: #3c4043;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        font-size: 0.95rem;
    }

    .btn-google:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        border-color: #dadce0;
        transform: translateY(-1px);
    }

    .btn-google img {
        width: 20px;
        height: 20px;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: none;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        z-index: 10000;
    }

    .loading-spinner {
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top: 4px solid #ffffff;
        width: 50px;
        height: 50px;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
    }

    .loading-overlay p {
        color: white;
        font-family: "Quicksand", sans-serif;
        font-weight: 500;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
</style>