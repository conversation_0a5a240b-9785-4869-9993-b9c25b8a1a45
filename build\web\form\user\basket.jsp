<%--
    Document   : basket
    Created on : Jun 16, 2025
    Author     : Arqeta
    Description: User shopping cart page
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Mendapatkan informasi user yang sedang login
    String userId = (String) session.getAttribute("userId");
    if (userId == null) {
        response.sendRedirect("form/signin.jsp");
        return;
    }
%>

<div class="page-content">
    <div class="page-header">
        <h2>Keranjang Belanja</h2>
        <p><PERSON><PERSON><PERSON> layanan yang ingin Anda beli</p>
    </div>

    <div class="cart-container">
        <div class="cart-wrapper">
        <div class="services-container" id="cartItems">
            <%
                try {
                    // Mengambil data keranjang dari database
                    PreparedStatement ps = conn.prepareStatement(
                        "SELECT c.*, s.name as service_name, s.images, s.price as current_price " +
                        "FROM cart c " +
                        "LEFT JOIN services s ON c.service_id = s.id " +
                        "WHERE c.user_id = ? " +
                        "ORDER BY c.created_at DESC"
                    );
                    ps.setInt(1, Integer.parseInt(userId));
                    ResultSet rs = ps.executeQuery();

                    boolean hasItems = false;
                    double totalAmount = 0;

                    while (rs.next()) {
                        hasItems = true;
                        int cartId = rs.getInt("id");
                        int serviceId = rs.getInt("service_id");
                        String serviceName = rs.getString("service_name");
                        String serviceImage = rs.getString("images");
                        double price = rs.getDouble("price");
                        double currentPrice = rs.getDouble("current_price");
                        int quantity = rs.getInt("quantity");
                        double itemTotal = price * quantity;
                        totalAmount += itemTotal;

                        // Cek apakah harga berubah
                        boolean priceChanged = (currentPrice != price);
            %>
                        <div class="service-card" data-cart-id="<%= cartId %>" data-service-id="<%= serviceId %>">
                            <div class="service-img">
                                <img src="../../dist/img/<%= serviceImage %>" alt="<%= serviceName %>" onerror="this.src='../../dist/img/default-service.png';">
                            </div>
                            <div class="service-info">
                                <h3><%= serviceName %></h3>
                                <p>Kuota: <%= quantity %></p>
                                <% if (priceChanged) { %>
                                    <p class="service-price old-price">Rp <%= String.format("%,.0f", price) %></p>
                                    <p class="service-price current-price">Rp <%= String.format("%,.0f", currentPrice) %></p>
                                    <p class="price-change-notice">Harga telah berubah</p>
                                <% } else { %>
                                    <p class="service-price">Rp <%= String.format("%,.0f", price) %></p>
                                <% } %>
                                <div class="service-total">
                                    <strong>Total: Rp <%= String.format("%,.0f", itemTotal) %></strong>
                                </div>
                                <div class="service-actions">
                                    <button class="btn-buy" onclick="buyNow(<%= serviceId %>, '<%= serviceName.replace("'", "\\'") %>', <%= priceChanged ? currentPrice : price %>, <%= quantity %>)">
                                        <i data-feather="credit-card"></i> Beli Saja
                                    </button>
                                    <button class="btn-cart" onclick="removeFromCart(<%= cartId %>)">
                                        <i data-feather="trash-2"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
            <%
                    }

                    if (!hasItems) {
            %>
                        <div class="empty-cart">
                            <div class="empty-cart-icon">
                                <i data-feather="shopping-cart"></i>
                            </div>
                            <h3>Keranjang Kosong</h3>
                            <p>Belum ada layanan yang ditambahkan ke keranjang</p>
                            <a href="${pageContext.request.contextPath}/home.jsp#services" class="btn-primary">
                                <i data-feather="package"></i>
                                Lihat Layanan
                            </a>
                        </div>
            <%
                    } else {
            %>
                        <div class="cart-summary">
                            <div class="summary-content">
                                <h3>Ringkasan Keranjang</h3>
                                <div class="summary-total">
                                    <strong>Total Keseluruhan: Rp <%= String.format("%,.0f", totalAmount) %></strong>
                                </div>
                                <div class="summary-actions">
                                    <button class="btn-primary" onclick="showAddServiceModal()">
                                        <i data-feather="plus"></i>
                                        Tambah Layanan
                                    </button>
                                    <button class="btn-secondary" onclick="clearCart()">
                                        <i data-feather="trash-2"></i>
                                        Kosongkan Keranjang
                                    </button>
                                </div>
                            </div>
                        </div>
            <%
                    }

                    rs.close();
                    ps.close();
                } catch (SQLException e) {
                    out.println("<div class='error-message'>Error: " + e.getMessage() + "</div>");
                } catch (Exception e) {
                    out.println("<div class='error-message'>Error: " + e.getMessage() + "</div>");
                }
            %>
        </div>
        </div>
    </div>
</div>

<!-- Add Service Modal -->
<div id="addServiceModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Tambah Layanan ke Keranjang</h3>
            <span class="close-modal" onclick="closeAddServiceModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div class="services-grid" id="availableServices">
                <!-- Services will be loaded here via JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Transaction Modal -->
<div id="cartTransactionModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Konfirmasi Pembelian</h3>
            <span class="close-modal" onclick="closeCartTransactionModal()">&times;</span>
        </div>
        <form id="cartTransactionForm" action="../../process/user/cartTransactionProcess.jsp" method="POST">
            <input type="hidden" id="cartServiceId" name="serviceId">
            <input type="hidden" id="cartQuantity" name="quantity">
            <div class="form-group">
                <label for="cartServiceName">Layanan</label>
                <input type="text" id="cartServiceName" name="serviceName" readonly>
            </div>
            <div class="form-group">
                <label for="cartServicePrice">Harga per Unit</label>
                <input type="text" id="cartServicePrice" name="servicePrice" readonly>
            </div>
            <div class="form-group">
                <label for="cartTotalPrice">Total Harga</label>
                <input type="text" id="cartTotalPrice" name="totalPrice" readonly>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn-secondary" onclick="closeCartTransactionModal()">Batal</button>
                <button type="submit" class="btn-primary">Konfirmasi Pembelian</button>
            </div>
        </form>
    </div>
</div>

<script>
    // Update quantity in cart
    function updateQuantity(cartId, newQuantity) {
        if (newQuantity < 1) return;
        
        fetch('../../process/user/updateCart.jsp', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=update&cartId=${cartId}&quantity=${newQuantity}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload(); // Reload to show updated quantities and totals
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Terjadi kesalahan saat memperbarui keranjang', 'error');
        });
    }

    // Remove item from cart
    function removeFromCart(cartId) {
        if (confirm('Apakah Anda yakin ingin menghapus item ini dari keranjang?')) {
            fetch('../../process/user/updateCart.jsp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=remove&cartId=${cartId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    location.reload();
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Terjadi kesalahan saat menghapus item', 'error');
            });
        }
    }

    // Clear entire cart
    function clearCart() {
        if (confirm('Apakah Anda yakin ingin mengosongkan seluruh keranjang?')) {
            fetch('../../process/user/updateCart.jsp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=clear'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    location.reload();
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Terjadi kesalahan saat mengosongkan keranjang', 'error');
            });
        }
    }

    // Buy now from cart
    function buyNow(serviceId, serviceName, price, quantity) {
        document.getElementById('cartServiceId').value = serviceId;
        document.getElementById('cartServiceName').value = serviceName;
        document.getElementById('cartServicePrice').value = 'Rp ' + new Intl.NumberFormat('id-ID').format(price);
        document.getElementById('cartQuantity').value = quantity;
        
        const totalPrice = price * quantity;
        document.getElementById('cartTotalPrice').value = 'Rp ' + new Intl.NumberFormat('id-ID').format(totalPrice);
        
        document.getElementById('cartTransactionModal').style.display = 'block';
    }

    // Close transaction modal
    function closeCartTransactionModal() {
        document.getElementById('cartTransactionModal').style.display = 'none';
    }

    // Show add service modal
    function showAddServiceModal() {
        loadAvailableServices();
        document.getElementById('addServiceModal').style.display = 'block';
    }

    // Close add service modal
    function closeAddServiceModal() {
        document.getElementById('addServiceModal').style.display = 'none';
    }

    // Load available services
    function loadAvailableServices() {
        fetch('../../process/user/getServices.jsp')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayServices(data.services);
            } else {
                document.getElementById('availableServices').innerHTML = '<p>Gagal memuat layanan</p>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('availableServices').innerHTML = '<p>Terjadi kesalahan saat memuat layanan</p>';
        });
    }

    // Display services in modal
    function displayServices(services) {
        const container = document.getElementById('availableServices');
        let html = '';

        services.forEach(service => {
            const formattedPrice = new Intl.NumberFormat('id-ID').format(service.price);
            const isDisabled = service.quantity <= 0;
            const buttonText = isDisabled ? 'Stok Habis' : 'Tambah ke Keranjang';
            const disabledAttr = isDisabled ? 'disabled' : '';

            html += `
                <div class="service-card-modal">
                    <div class="service-image">
                        <img src="../../dist/img/${service.images}" alt="${service.name}" onerror="this.src='../../dist/img/default-service.svg';">
                    </div>
                    <div class="service-info">
                        <h4>${service.name}</h4>
                        <p class="service-price">Rp ${formattedPrice}</p>
                        <p class="service-quantity">Kuota: ${service.quantity}</p>
                        <button class="btn-add-to-cart" onclick="addServiceToCart(${service.id}, '${service.name}', ${service.price})" ${disabledAttr}>
                            <i data-feather="plus"></i>
                            ${buttonText}
                        </button>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
        feather.replace(); // Re-initialize feather icons
    }

    // Add service to cart from modal
    function addServiceToCart(serviceId, serviceName, servicePrice) {
        fetch('../../process/user/addToCart.jsp', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `serviceId=${serviceId}&quantity=1`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                closeAddServiceModal();
                location.reload(); // Reload to show updated cart
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Terjadi kesalahan saat menambahkan ke keranjang', 'error');
        });
    }

    // Initialize feather icons
    document.addEventListener('DOMContentLoaded', function() {
        feather.replace();
    });
</script>
