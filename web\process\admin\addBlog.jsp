<%--
  Document   : addBlog
  Created on : Jun 15, 2025
  Author     : Arqeta
  Description: Process untuk menambah blog baru
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.io.*"%>
<%@page import="java.nio.file.*"%>
<%@page import="jakarta.servlet.http.Part"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Pastikan request adalah POST, jika bukan, redirect kembali.
    if (!"POST".equalsIgnoreCase(request.getMethod())) {
        response.sendRedirect("../../dashboardadmin.jsp?page=blog");
        return;
    }

    try {
        // 1. Ambil parameter dari form
        String title = request.getParameter("title");
        String content = request.getParameter("content");

        // 2. Validasi input tidak boleh kosong
        if (title == null || title.trim().isEmpty() || content == null || content.trim().isEmpty()) {
            session.setAttribute("message", "Semua field harus diisi!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../dashboardadmin.jsp?page=blog");
            return;
        }

        // 3. Handle proses upload file gambar
        String fileName = null;
        Part filePart = request.getPart("image");

        if (filePart != null && filePart.getSize() > 0) {
            // Validasi tipe file (harus gambar)
            String contentType = filePart.getContentType();
            if (!contentType.startsWith("image/")) {
                session.setAttribute("message", "File harus berupa gambar!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboardadmin.jsp?page=blog");
                return;
            }

            // Validasi ukuran file (maksimal 10MB)
            if (filePart.getSize() > 10 * 1024 * 1024) {
                session.setAttribute("message", "Ukuran file maksimal 10MB!");
                session.setAttribute("messageType", "error");
                response.sendRedirect("../../dashboardadmin.jsp?page=blog");
                return;
            }

            // Generate nama file unik untuk mencegah duplikasi
            String originalFileName = filePart.getSubmittedFileName();
            String fileExtension = originalFileName.substring(originalFileName.lastIndexOf("."));
            fileName = "blog_" + System.currentTimeMillis() + fileExtension;

            // Path untuk menyimpan file - simpan ke direktori build dan source
            // untuk konsistensi saat development dan deployment.
            String buildUploadPath = application.getRealPath("/dist/img/");
            String sourceUploadPath = application.getRealPath("/").replace("build" + File.separator + "web", "web") + "dist" + File.separator + "img";

            // Buat direktori jika belum ada
            File buildUploadDir = new File(buildUploadPath);
            File sourceUploadDir = new File(sourceUploadPath);
            if (!buildUploadDir.exists()) {
                buildUploadDir.mkdirs();
            }
            if (!sourceUploadDir.exists()) {
                sourceUploadDir.mkdirs();
            }

            // Simpan file ke kedua lokasi
            String buildFilePath = buildUploadPath + File.separator + fileName;
            String sourceFilePath = sourceUploadPath + File.separator + fileName;

            // Menggunakan Files.copy untuk penanganan stream yang lebih aman
            try (InputStream input = filePart.getInputStream()) {
                Files.copy(input, Paths.get(buildFilePath), StandardCopyOption.REPLACE_EXISTING);
            }
            // Buka kembali stream untuk copy kedua karena stream hanya bisa dibaca sekali
            try (InputStream input = filePart.getInputStream()) {
                Files.copy(input, Paths.get(sourceFilePath), StandardCopyOption.REPLACE_EXISTING);
            }

        } else {
            // Jika tidak ada file yang diupload
            session.setAttribute("message", "Gambar blog harus diupload!");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../../dashboardadmin.jsp?page=blog");
            return;
        }

        // 4. Simpan data ke database
        PreparedStatement ps = conn.prepareStatement("INSERT INTO blog (title, image, content) VALUES (?, ?, ?)");
        ps.setString(1, title.trim());
        ps.setString(2, fileName);
        ps.setString(3, content.trim());

        int result = ps.executeUpdate();
        ps.close();

        if (result > 0) {
            session.setAttribute("message", "Blog berhasil ditambahkan!");
            session.setAttribute("messageType", "success");
        } else {
            session.setAttribute("message", "Gagal menambahkan blog!");
            session.setAttribute("messageType", "error");
        }

    } catch (Exception e) {
        session.setAttribute("message", "Terjadi kesalahan sistem: " + e.getMessage());
        session.setAttribute("messageType", "error");
        e.printStackTrace(); // Penting untuk debugging di console server
    }

    // 5. Redirect kembali ke halaman blog setelah semua proses selesai
    response.sendRedirect("../../dashboardadmin.jsp?page=blog");
%>
