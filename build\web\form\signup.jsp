<%--
    Document   : signup
    Created on : Jun 1, 2025, 3:28:22 PM
    Author     : Arqeta
    Description: Registration page for Arqeta website
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../config/connection.jsp" %>

<!DOCTYPE html>
<html lang="id">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="description" content="Daftar di Arqeta" />
        <title>Daftar | Arqeta</title>

        <link rel="stylesheet" href="../dist/css/auth.css" />
        <link
            href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600&display=swap"
            rel="stylesheet"
        />
        <script src="https://unpkg.com/feather-icons"></script>

        <script>
            document.addEventListener("contextmenu", function (e) {
                e.preventDefault();
                // Using custom notification instead of alert()
                showNotification("Klik kanan dinonaktifkan pada situs ini!", "error");
            });

            // Prevent inspect element
            document.addEventListener("keydown", function (e) {
                if (
                    e.keyCode === 123 ||
                    (e.ctrlKey && e.shiftKey && e.keyCode === 73)
                ) {
                    e.preventDefault();
                    // Using custom notification instead of alert()
                    showNotification("Inspeksi elemen dinonaktifkan pada situs ini!", "error");
                }
            });
        </script>
    </head>
    <body>
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <h1>Daftar</h1>
                    <p>Buat akun baru di Arqeta</p>
                </div>

                <form
                    class="auth-form"
                    id="signupForm"
                    action="../process/signupProcess.jsp"
                    method="POST"
                >
                    <div class="form-group">
                        <label for="name">Nama</label>
                        <input
                            type="text"
                            id="name"
                            name="name"
                            placeholder="Masukkan nama lengkap"
                            required
                        />
                    </div>

                    <div class="form-group">
                        <label for="username">Username</label>
                        <input
                            type="text"
                            id="username"
                            name="username"
                            placeholder="Masukkan username"
                            required
                        />
                    </div>

                    <div class="form-group">
                        <label for="email">Email</label>
                        <input
                            type="email"
                            id="email"
                            name="email"
                            placeholder="Masukkan email"
                            required
                        />
                    </div>

                    <div class="form-group">
                        <label for="password">Kata Sandi</label>
                        <div class="password-input">
                            <input
                                type="password"
                                id="password"
                                name="password"
                                placeholder="Masukkan kata sandi"
                                required
                            />
                            <span
                                class="password-toggle"
                                onclick="togglePassword('password', 'passwordIcon')"
                            >
                                <i id="passwordIcon" data-feather="eye-off"></i>
                            </span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">Konfirmasi Kata Sandi</label>
                        <div class="password-input">
                            <input
                                type="password"
                                id="confirmPassword"
                                name="confirmPassword"
                                placeholder="Konfirmasi kata sandi"
                                required
                            />
                            <span
                                class="password-toggle"
                                onclick="togglePassword('confirmPassword', 'confirmPasswordIcon')"
                            >
                                <i id="confirmPasswordIcon" data-feather="eye-off"></i>
                            </span>
                        </div>
                    </div>

                    <button type="submit" class="btn-auth">Daftar</button>

                    <div class="auth-footer">
                        <p>Sudah memiliki akun? <a href="signin.jsp">Login</a></p>
                    </div>
                </form>
            </div>
        </div>

        <div class="notification" id="notification">
            <div class="notification-content">
                <span class="notification-message" id="notificationMessage"></span>
                <span class="close-notification" onclick="closeNotification()"
                    >&times;</span
                >
            </div>
        </div>

        <script>
            // Initialize feather icons
            feather.replace();

            // Toggle password visibility
            function togglePassword(inputId, iconId) {
                const passwordInput = document.getElementById(inputId);
                const passwordIcon = document.getElementById(iconId);

                if (passwordInput.type === "password") {
                    passwordInput.type = "text";
                    passwordIcon.setAttribute("data-feather", "eye");
                } else {
                    passwordInput.type = "password";
                    passwordIcon.setAttribute("data-feather", "eye-off");
                }

                feather.replace();
            }

            // Clear form on page refresh
            window.onload = function () {
                document.getElementById("signupForm").reset();
            };

            // Form validation
            document
                .getElementById("signupForm")
                .addEventListener("submit", function (e) {
                    const password = document.getElementById("password").value;
                    const confirmPassword =
                        document.getElementById("confirmPassword").value;

                    if (password !== confirmPassword) {
                        e.preventDefault();
                        // Using custom notification instead of alert()
                        showNotification("Kata sandi dan konfirmasi kata sandi tidak cocok.", "error");
                    }
                });

            // Notification functions (copied from signin.jsp for consistency)
            function showNotification(message, type = "info") {
                const notification = document.getElementById("notification");
                const notificationMessage = document.getElementById(
                    "notificationMessage"
                );

                notificationMessage.textContent = message;
                notification.className = "notification";
                notification.classList.add(`notification-${type}`);
                notification.classList.add("show");

                // Auto hide after 10 seconds
                setTimeout(() => {
                    closeNotification();
                }, 10000);
            }

            function closeNotification() {
                const notification = document.getElementById("notification");
                notification.classList.remove("show");
            }

            // Prevent copy paste
            document.addEventListener("copy", function (e) {
                e.preventDefault();
                showNotification("Maaf, tindakan salin telah dinonaktifkan di situs ini.", "error");
            });

            document.addEventListener("paste", function (e) {
                e.preventDefault();
                showNotification("Maaf, tindakan tempel telah dinonaktifkan di situs ini.", "error");
            });
        </script>
    </body>
</html>
