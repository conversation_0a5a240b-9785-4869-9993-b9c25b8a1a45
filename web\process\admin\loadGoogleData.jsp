<%--
    Document   : loadGoogleData
    Created on : Jun 14, 2025
    Author     : Arqeta
    Description: Load Google account data (admin/user) via AJAX untuk dashboard admin
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Mendapatkan tipe data yang diminta (admin atau user)
    String type = request.getParameter("type");
    if (type == null || (!type.equals("admin") && !type.equals("user"))) {
        type = "admin";
    }

    // Tentukan nama tabel berdasarkan tipe
    String tableName = type + "_google";
%>

<div class="table-responsive">
    <table class="data-table" id="googleTable">
        <thead>
            <tr>
                <th onclick="sortTable(0)">ID <i data-feather="arrow-up-down"></i></th>
                <th onclick="sortTable(1)">Nama <i data-feather="arrow-up-down"></i></th>
                <th onclick="sortTable(2)">Email <i data-feather="arrow-up-down"></i></th>
                <th onclick="sortTable(3)"><%= type.equals("admin") ? "Admin ID" : "User ID" %> <i data-feather="arrow-up-down"></i></th>
                <th>Foto</th>
                <th onclick="sortTable(5)">Waktu Pembuatan <i data-feather="arrow-up-down"></i></th>
                <th onclick="sortTable(6)">Terakhir Login <i data-feather="arrow-up-down"></i></th>
                <th onclick="sortTable(7)">Waktu Perubahan <i data-feather="arrow-up-down"></i></th>
                <th>Aksi</th>
            </tr>
        </thead>
        <tbody id="googleTableBody">
            <%
                try {
                    // Query untuk mengambil data berdasarkan tipe yang dipilih, diurutkan berdasarkan ID
                    String query = "SELECT * FROM " + tableName + " ORDER BY id ASC";
                    PreparedStatement ps = conn.prepareStatement(query);
                    ResultSet rs = ps.executeQuery();

                    while (rs.next()) {
                        String idColumn = type.equals("admin") ? "admin_id" : "user_id";
            %>
            <tr>
                <td><%= rs.getInt("id") %></td>
                <td><%= rs.getString("name") %></td>
                <td><%= rs.getString("email") %></td>
                <td><%= rs.getString(idColumn) %></td>
                <td>
                    <% if (rs.getString("photo_url") != null && !rs.getString("photo_url").isEmpty()) { %>
                        <img src="<%= rs.getString("photo_url") %>" alt="Foto Profil" class="profile-photo" onerror="this.src='../../dist/img/default-avatar.png';">
                    <% } else { %>
                        <span class="no-photo">Tidak ada foto</span>
                    <% } %>
                </td>
                <td><%= rs.getTimestamp("created_at") %></td>
                <td><%= rs.getTimestamp("last_login") != null ? rs.getTimestamp("last_login") : "Belum pernah login" %></td>
                <td><%= rs.getTimestamp("updated_at") %></td>
                <td class="action-buttons">
                    <button class="btn-delete" onclick="openDeleteGoogleModal(<%= rs.getInt("id") %>, '<%= type %>', '<%= rs.getString("name").replace("'", "\\'") %>')" title="Hapus">
                        <i data-feather="trash-2"></i>
                    </button>
                </td>
            </tr>
            <%
                    }
                    rs.close();
                    ps.close();
                } catch (SQLException e) {
                    out.println("<tr><td colspan='9'>Error: " + e.getMessage() + "</td></tr>");
                }
            %>
        </tbody>
    </table>
</div>

<script>
// Inisialisasi ikon feather untuk tabel yang baru dimuat
feather.replace();

// Fungsi untuk mencari data dalam tabel
function searchData() {
    const input = document.getElementById('searchInput');
    const filter = input.value.toLowerCase();
    const table = document.getElementById('googleTable');
    const rows = table.getElementsByTagName('tr');

    for (let i = 1; i < rows.length; i++) {
        const cells = rows[i].getElementsByTagName('td');
        let found = false;

        // Cari di kolom nama dan email
        for (let j = 1; j <= 2; j++) {
            if (cells[j] && cells[j].textContent.toLowerCase().includes(filter)) {
                found = true;
                break;
            }
        }

        rows[i].style.display = found ? '' : 'none';
    }
}

// Fungsi untuk mengurutkan tabel
function sortTable(columnIndex) {
    const table = document.getElementById('googleTable');
    const tbody = table.getElementsByTagName('tbody')[0];
    const rows = Array.from(tbody.getElementsByTagName('tr'));

    // Tentukan arah pengurutan
    const isAscending = table.getAttribute('data-sort-direction') !== 'asc';
    table.setAttribute('data-sort-direction', isAscending ? 'asc' : 'desc');

    // Urutkan baris
    rows.sort((a, b) => {
        const aValue = a.getElementsByTagName('td')[columnIndex].textContent.trim();
        const bValue = b.getElementsByTagName('td')[columnIndex].textContent.trim();

        // Penanganan pengurutan numerik untuk kolom ID
        if (columnIndex === 0 || columnIndex === 3) {
            return isAscending ? parseInt(aValue) - parseInt(bValue) : parseInt(bValue) - parseInt(aValue);
        }

        // Penanganan pengurutan tanggal untuk kolom timestamp
        if (columnIndex === 5 || columnIndex === 6 || columnIndex === 7) {
            const aDate = new Date(aValue);
            const bDate = new Date(bValue);
            return isAscending ? aDate - bDate : bDate - aDate;
        }

        // Pengurutan string
        return isAscending ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    });

    // Tambahkan kembali baris yang sudah diurutkan
    rows.forEach(row => tbody.appendChild(row));

    // Update indikator pengurutan
    updateSortIndicators(columnIndex, isAscending);
}

// Fungsi untuk memperbarui indikator pengurutan
function updateSortIndicators(activeColumn, isAscending) {
    const headers = document.querySelectorAll('#googleTable th');
    headers.forEach((header, index) => {
        const icon = header.querySelector('i');
        if (icon) {
            if (index === activeColumn) {
                icon.setAttribute('data-feather', isAscending ? 'arrow-up' : 'arrow-down');
            } else {
                icon.setAttribute('data-feather', 'arrow-up-down');
            }
        }
    });
    feather.replace();
}
</script>

<style>
/* Styling khusus untuk foto profil dalam tabel */
.profile-photo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-color);
}

.no-photo {
    color: var(--text-muted);
    font-style: italic;
    font-size: 0.9rem;
}
</style>

