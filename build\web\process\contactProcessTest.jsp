<%--
    Document   : contactProcessTest
    Created on : Jun 14, 2025
    Author     : Arqeta
    Description: Test contact form submission
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../config/connection.jsp" %>

<%
    // Get form data and sanitize
    String name = request.getParameter("name");
    String email = request.getParameter("email");
    String subject = request.getParameter("subject");
    String message = request.getParameter("message");

    // Validate input
    if (name == null || name.trim().isEmpty() ||
        email == null || email.trim().isEmpty() ||
        subject == null || subject.trim().isEmpty() ||
        message == null || message.trim().isEmpty()) {
        // Store error message in session
        session.setAttribute("message", "Semua kolom harus diisi.");
        session.setAttribute("messageType", "error");
        // Redirect back to home page
        response.sendRedirect("../home.jsp#contact");
        return;
    }

    // Sanitize input to prevent XSS
    name = name.trim().replaceAll("<", "&lt;").replaceAll(">", "&gt;");
    email = email.trim().toLowerCase();
    subject = subject.trim().replaceAll("<", "&lt;").replaceAll(">", "&gt;");
    message = message.trim().replaceAll("<", "&lt;").replaceAll(">", "&gt;");

    // Validate email format
    if (!email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")) {
        session.setAttribute("message", "Format email tidak valid.");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../home.jsp#contact");
        return;
    }

    try {
        if (conn == null || conn.isClosed()) {
            session.setAttribute("message", "Koneksi database tidak tersedia. Silakan coba lagi nanti.");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../home.jsp#contact");
            return;
        }

        // Insert contact message into database
        PreparedStatement ps = conn.prepareStatement(
            "INSERT INTO contact (name, email, subject, message, status, created_at, updated_at) VALUES (?, ?, ?, ?, 'unread', NOW(), NOW())"
        );
        ps.setString(1, name);
        ps.setString(2, email);
        ps.setString(3, subject);
        ps.setString(4, message);
        int result = ps.executeUpdate();
        ps.close();

        if (result > 0) {
            // Message sent successfully
            session.setAttribute("message", "Pesan Anda telah berhasil dikirim. Kami akan segera menghubungi Anda.");
            session.setAttribute("messageType", "success");
        } else {
            // Message sending failed
            session.setAttribute("message", "Gagal mengirim pesan. Silakan coba lagi.");
            session.setAttribute("messageType", "error");
        }
    } catch (SQLException e) {
        // Database error
        System.out.println("Contact form error: " + e.getMessage());
        e.printStackTrace();
        session.setAttribute("message", "Terjadi kesalahan sistem. Silakan coba lagi nanti.");
        session.setAttribute("messageType", "error");
    } catch (Exception e) {
        // General error
        System.out.println("Contact form error: " + e.getMessage());
        e.printStackTrace();
        session.setAttribute("message", "Terjadi kesalahan tidak terduga. Silakan coba lagi nanti.");
        session.setAttribute("messageType", "error");
    }

    // Redirect back to home page
    response.sendRedirect("../home.jsp#contact");
%>
