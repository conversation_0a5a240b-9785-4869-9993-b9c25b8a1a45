<%--
    Document   : transactionhistory
    Created on : Jun 16, 2025
    Author     : Arqeta
    Description: User transaction history page
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../../config/connection.jsp" %>

<%
    // Mendapatkan informasi user yang sedang login
    String userId = (String) session.getAttribute("userId");
    if (userId == null) {
        response.sendRedirect("../signin.jsp");
        return;
    }
%>

<div class="page-content">
    <div class="page-header">
        <h2>Riwayat Transaksi</h2>
        <p>Lihat semua transaksi yang pernah Anda lakukan</p>
    </div>

    <div class="transaction-filters">
        <div class="filter-group">
            <label for="statusFilter">Filter Status:</label>
            <select id="statusFilter" onchange="filterTransactions()">
                <option value="">Semua Status</option>
                <option value="pending">Menunggu <PERSON>fi<PERSON></option>
                <option value="completed">Diterima</option>
                <option value="cancelled">Ditolak</option>
            </select>
        </div>
    </div>

    <div class="transaction-container">
        <div class="transaction-list" id="transactionList">
            <%
                try {
                    // Mengambil data transaksi user dari database
                    PreparedStatement ps = conn.prepareStatement(
                        "SELECT t.*, s.name as service_name, s.images " +
                        "FROM transaction t " +
                        "LEFT JOIN services s ON t.service_id = s.id " +
                        "WHERE t.user_id = ? " +
                        "ORDER BY t.created_at DESC"
                    );
                    ps.setInt(1, Integer.parseInt(userId));
                    ResultSet rs = ps.executeQuery();
                    boolean hasTransactions = false;

                    while (rs.next()) {
                        hasTransactions = true;
                        int transactionId = rs.getInt("id");
                        String serviceName = rs.getString("service_name");
                        String serviceImage = rs.getString("images");
                        double amount = rs.getDouble("amount");
                        int unit = rs.getInt("unit");
                        double total = rs.getDouble("total");
                        String status = rs.getString("status");
                        String createdAt = rs.getString("created_at");

                        String statusClass = "";
                        String statusText = "";
                        String statusIcon = "";

                        switch(status) {
                            case "pending":
                                statusClass = "status-pending";
                                statusText = "Menunggu Konfirmasi";
                                statusIcon = "clock";
                                break;
                            case "completed":
                                statusClass = "status-completed";
                                statusText = "Diterima";
                                statusIcon = "check-circle";
                                break;
                            case "cancelled":
                                statusClass = "status-cancelled";
                                statusText = "Ditolak";
                                statusIcon = "x-circle";
                                break;
                            default:
                                statusClass = "status-pending";
                                statusText = status;
                                statusIcon = "help-circle";
                        }
            %>
            <div class="transaction-item" data-status="<%= status %>">
                <div class="transaction-header">
                    <div class="transaction-id">
                        <span class="label">ID Transaksi:</span>
                        <span class="value">#<%= String.format("%06d", transactionId) %></span>
                    </div>
                    <div class="transaction-date">
                        <span class="label">Tanggal:</span>
                        <span class="value"><%= createdAt %></span>
                    </div>
                    <div class="transaction-status">
                        <span class="status-badge <%= statusClass %>">
                            <i data-feather="<%= statusIcon %>"></i>
                            <%= statusText %>
                        </span>
                    </div>
                </div>

                <div class="transaction-content">
                    <div class="service-info">
                        <div class="service-image">
                            <!-- Debug: Image name = <%= serviceImage %> -->
                            <% if (serviceImage != null && !serviceImage.isEmpty()) { %>
                                <img src="dist/img/<%= serviceImage %>" alt="<%= serviceName %>"
                                     onerror="this.src='dist/img/default-service.png';"
                                     style="width: 120px; height: 120px; object-fit: cover; border-radius: 8px;"
                                     title="Image: <%= serviceImage %>">
                            <% } else { %>
                                <img src="dist/img/default-service.png" alt="<%= serviceName != null ? serviceName : "Layanan" %>"
                                     style="width: 120px; height: 120px; object-fit: cover; border-radius: 8px;"
                                     title="Default image (no service image found)">
                            <% } %>
                        </div>
                        <div class="service-details">
                            <h3>
                                <%= serviceName != null ? serviceName : "Layanan Tidak Ditemukan" %>
                            </h3>
                            <div class="service-specs">
                                <div class="spec-item">
                                    <span class="spec-label">Harga per Unit:</span>
                                    <span class="spec-value">Rp <%= String.format("%,.0f", amount) %></span>
                                </div>
                                <div class="spec-item">
                                    <span class="spec-label">Jumlah:</span>
                                    <span class="spec-value"><%= unit %> unit</span>
                                </div>
                                <div class="spec-item total">
                                    <span class="spec-label">Total Pembayaran:</span>
                                    <span class="spec-value">Rp <%= String.format("%,.0f", total) %></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="transaction-actions">
                        <% if ("pending".equals(status)) { %>
                        <div class="action-info">
                            <i data-feather="info"></i>
                            <span>Menunggu konfirmasi dari admin</span>
                        </div>
                        <% } else if ("completed".equals(status)) { %>
                        <div class="action-info success">
                            <i data-feather="check"></i>
                            <span>Pesanan telah diterima dan sedang diproses</span>
                        </div>
                        <% } else if ("cancelled".equals(status)) { %>
                        <div class="action-info error">
                            <i data-feather="x"></i>
                            <span>Pesanan ditolak oleh admin</span>
                        </div>
                        <% } %>
                    </div>
                </div>
            </div>
            <%
                    }
                    if (!hasTransactions) {
            %>
            <div class="empty-transactions">
                <div class="empty-icon">
                    <i data-feather="file-text"></i>
                </div>
                <h3>Belum Ada Transaksi</h3>
                <p>Anda belum melakukan transaksi apapun</p>
                <a href="home.jsp#services" class="btn-primary">
                    <i data-feather="package"></i>
                    Lihat Layanan
                </a>
            </div>
            <%
                    }
                    rs.close();
                    ps.close();
                } catch (SQLException e) {
                    out.println("<div class=\"error-message\">Error: " + e.getMessage() + "</div>");
                }
            %>
        </div>
    </div>
</div>

<script>
    // Filter transactions by status
    function filterTransactions() {
        const filter = document.getElementById("statusFilter").value;
        const transactions = document.querySelectorAll(".transaction-item");

        transactions.forEach((transaction) => {
            const status = transaction.getAttribute("data-status");
            if (filter === "" || status === filter) {
                transaction.style.display = "block";
            } else {
                transaction.style.display = "none";
            }
        });

        // Show/hide empty state
        const visibleTransactions = document.querySelectorAll(
            '.transaction-item[style="display: block"], .transaction-item:not([style*="display: none"])'
        );
        const emptyState = document.querySelector(".empty-transactions");

        if (visibleTransactions.length === 0 && !emptyState) {
            // Create temporary empty state for filtered results
            const transactionList = document.getElementById("transactionList");
            const tempEmpty = document.createElement("div");
            tempEmpty.className = "empty-transactions temp-empty";
            tempEmpty.innerHTML = `
                <div class="empty-icon">
                    <i data-feather="search"></i>
                </div>
                <h3>Tidak Ada Transaksi</h3>
                <p>Tidak ada transaksi dengan status yang dipilih</p>
            `;
            transactionList.appendChild(tempEmpty);
            feather.replace();
        } else if (visibleTransactions.length > 0) {
            // Remove temporary empty state
            const tempEmpty = document.querySelector(".temp-empty");
            if (tempEmpty) {
                tempEmpty.remove();
            }
        }
    }

    // Initialize feather icons
    document.addEventListener("DOMContentLoaded", function () {
        feather.replace();
    });
</script>
