<%--
  Document    : resetPasswordProcess
  Created on  : June 16, 2025
  Author      : Arqeta
  Description : Process password reset after OTP verification
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.security.MessageDigest"%>
<%@page import="java.security.NoSuchAlgorithmException"%>

<%
    // Disable caching
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setHeader("Pragma", "no-cache");
    response.setDateHeader("Expires", 0);

    String email = request.getParameter("email");
    String otp = request.getParameter("otp");
    String newPassword = request.getParameter("newPassword");
    String confirmPassword = request.getParameter("confirmPassword");

    if (email == null || email.trim().isEmpty() || otp == null || otp.trim().isEmpty() || newPassword == null || newPassword.trim().isEmpty() || confirmPassword == null || confirmPassword.trim().isEmpty()) {
        session.setAttribute("message", "Semua field harus diisi!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp");
        return;
    }

    // Validate password match
    if (!newPassword.equals(confirmPassword)) {
        session.setAttribute("message", "Konfirmasi kata sandi tidak sesuai!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp?step=reset&email=" + email + "&otp=" + otp);
        return;
    }

    // Validate password length
    if (newPassword.length() < 8) {
        session.setAttribute("message", "Kata sandi minimal 8 karakter!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp?step=reset&email=" + email + "&otp=" + otp);
        return;
    }

    Connection conn = null;
    PreparedStatement ps = null;
    ResultSet rs = null;

    try {
        // Load database connection
        Class.forName("com.mysql.cj.jdbc.Driver");
        conn = DriverManager.getConnection("******************************************************************************************************", "root", "");

        // Verify OTP is still valid and used
        String otpCheckSql = "SELECT id FROM otp_log WHERE email = ? AND otp = ? AND used = 1 " 
                         + "AND created_at >= DATE_SUB(NOW(), INTERVAL 30 MINUTE) ORDER BY created_at DESC LIMIT 1";
        ps = conn.prepareStatement(otpCheckSql);
        ps.setString(1, email);
        ps.setString(2, otp);
        rs = ps.executeQuery();

        if (!rs.next()) {
            session.setAttribute("message", "Sesi reset password sudah kedaluwarsa. Silakan mulai ulang.");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../form/forgotpassword.jsp");
            return;
        }
        rs.close();
        ps.close();

        // Hash the new password
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.update(newPassword.getBytes());
        byte[] hashedBytes = md.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : hashedBytes) {
            sb.append(String.format("%02x", b));
        }
        String hashedPassword = sb.toString();

        // Check if email exists in admin table
        boolean isAdmin = false;
        ps = conn.prepareStatement("SELECT id FROM admin WHERE email = ?");
        ps.setString(1, email);
        rs = ps.executeQuery();
        if (rs.next()) {
            isAdmin = true;
        }
        rs.close();
        ps.close();

        // Update password in appropriate table
        if (isAdmin) {
            ps = conn.prepareStatement("UPDATE admin SET password = ?, updated_at = NOW() WHERE email = ?");
        } else {
            ps = conn.prepareStatement("UPDATE user SET password = ?, updated_at = NOW() WHERE email = ?");
        }
        ps.setString(1, hashedPassword);
        ps.setString(2, email);

        int result = ps.executeUpdate();
        ps.close();

        if (result > 0) {
            // Password updated successfully
            session.setAttribute("message", "Kata sandi berhasil direset! Silakan login dengan kata sandi baru.");
            session.setAttribute("messageType", "success");
            // Note: OTP records are kept in database for history tracking
            // Admin can manage OTP history through Dashboard Admin -> Data Riwayat OTP
            response.sendRedirect("../form/signin.jsp");
        } else {
            session.setAttribute("message", "Gagal mereset kata sandi. Silakan coba lagi.");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../form/forgotpassword.jsp?step=reset&email=" + email + "&otp=" + otp);
        }
        
    } catch(ClassNotFoundException e) {
        System.out.println("MySQL Driver not found: " + e.getMessage());
        session.setAttribute("message", "Driver database tidak ditemukan!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp");
    } catch(NoSuchAlgorithmException e) {
        System.out.println("Hashing algorithm not found: " + e.getMessage());
        session.setAttribute("message", "Algoritma enkripsi tidak ditemukan!");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp");
    } catch(SQLException e) {
        System.out.println("Database error: " + e.getMessage());
        e.printStackTrace();
        session.setAttribute("message", "Terjadi kesalahan database: " + e.getMessage());
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp");
    } catch(Exception e) {
        System.out.println("General error: " + e.getMessage());
        e.printStackTrace();
        session.setAttribute("message", "Terjadi kesalahan sistem: " + e.getMessage());
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/forgotpassword.jsp");
    } finally {
        // Close database connection
        try {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
            if (conn != null && !conn.isClosed()) {
                conn.close();
            }
        } catch (SQLException se) {
            se.printStackTrace();
        }
    }
%>
