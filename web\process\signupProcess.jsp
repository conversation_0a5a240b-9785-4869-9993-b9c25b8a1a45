<%--
    Document   : signupProcess
    Created on : May 30, 2025, 4:25:30 PM
    Author     : Arqeta
    Description: Process user registration form submission
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@include file="../config/connection.jsp" %>
<%@page import="java.security.MessageDigest" %>
<%@page import="java.math.BigInteger" %>

<%
    // Get registration form data
    String name = request.getParameter("name");
    String username = request.getParameter("username");
    String email = request.getParameter("email");
    String password = request.getParameter("password");
    String confirmPassword = request.getParameter("confirmPassword");

    // Validate input
    if (name == null || name.trim().isEmpty() ||
        username == null || username.trim().isEmpty() ||
        email == null || email.trim().isEmpty() ||
        password == null || password.trim().isEmpty() ||
        confirmPassword == null || confirmPassword.trim().isEmpty()) {

        // Store error message in session
        session.setAttribute("message", "Semua kolom harus diisi.");
        session.setAttribute("messageType", "error");

        // Redirect back to signup page
        response.sendRedirect("../form/signup.jsp");
        return;
    }

    // Check if passwords match
    if (!password.equals(confirmPassword)) {
        session.setAttribute("message", "Kata sandi dan konfirmasi kata sandi tidak cocok.");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/signup.jsp");
        return;
    }

    // Check if email or username already exists
    try {
        PreparedStatement ps = conn.prepareStatement("SELECT * FROM user WHERE email = ? OR username = ?");
        ps.setString(1, email);
        ps.setString(2, username);
        ResultSet rs = ps.executeQuery();

        if (rs.next()) {
            String existingEmail = rs.getString("email");
            String existingUsername = rs.getString("username");

            if (email.equals(existingEmail)) {
                session.setAttribute("message", "Email sudah terdaftar. Silakan gunakan email lain.");
                session.setAttribute("messageType", "error");
            } else if (username.equals(existingUsername)) {
                session.setAttribute("message", "Username sudah digunakan. Silakan pilih username lain.");
                session.setAttribute("messageType", "error");
            }
            response.sendRedirect("../form/signup.jsp");
            return;
        }
        rs.close();
        ps.close();

        // Hash the password
        String hashedPassword = "";
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(password.getBytes("UTF-8"));
            BigInteger number = new BigInteger(1, hash);
            StringBuilder hexString = new StringBuilder(number.toString(16));

            // Pad with leading zeros
            while (hexString.length() < 64) {
                hexString.insert(0, '0');
            }
            hashedPassword = hexString.toString();
        } catch (Exception e) {
            out.println("Error: " + e.getMessage());
            return;
        }

        // Insert new user
        ps = conn.prepareStatement("INSERT INTO user (name, username, email, password, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())");
        ps.setString(1, name);
        ps.setString(2, username);
        ps.setString(3, email);
        ps.setString(4, hashedPassword);

        int result = ps.executeUpdate();
        ps.close();

        if (result > 0) {
            // Registration successful
            session.setAttribute("message", "Pendaftaran berhasil. Silakan login.");
            session.setAttribute("messageType", "success");
            response.sendRedirect("../form/signin.jsp");
        } else {
            // Registration failed
            session.setAttribute("message", "Pendaftaran gagal. Silakan coba lagi.");
            session.setAttribute("messageType", "error");
            response.sendRedirect("../form/signup.jsp");
        }
    } catch (Exception e) {
        out.println("Error: " + e.getMessage());
    }
%>
