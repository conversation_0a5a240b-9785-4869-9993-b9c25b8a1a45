<%--
    Document   : googleLoginProcess
    Created on : Jun 1, 2025, 5:20:45 PM
    Author     : Arqeta
    Description: Process Google login and registration
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@page import="java.sql.*"%>
<%@page import="java.time.LocalDateTime"%>
<%@include file="../config/connection.jsp" %>

<%
    // Get Google user data from URL parameters
    String name = request.getParameter("name");
    String email = request.getParameter("email");
    String photoURL = request.getParameter("photoURL");
    String uid = request.getParameter("uid");

    if (name == null || email == null) {
        session.setAttribute("message", "Data login tidak lengkap");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/loginwithgoogle.jsp");
        return;
    }

    try {
        // First check if the user exists in admin_google table
        PreparedStatement psCheckAdminGoogle = conn.prepareStatement("SELECT * FROM admin_google WHERE email = ?");
        psCheckAdminGoogle.setString(1, email);
        ResultSet rsAdminGoogle = psCheckAdminGoogle.executeQuery();

        if (rsAdminGoogle.next()) {
            // It's an admin with Google login
            int adminGoogleId = rsAdminGoogle.getInt("id");
            int adminId = rsAdminGoogle.getInt("admin_id");

            // Get admin details
            PreparedStatement psAdmin = conn.prepareStatement("SELECT * FROM admin WHERE id = ?");
            psAdmin.setInt(1, adminId);
            ResultSet rsAdmin = psAdmin.executeQuery();

            if (rsAdmin.next()) {
                // Set session variables
                session.setAttribute("adminId", String.valueOf(adminId));
                session.setAttribute("name", name);
                session.setAttribute("email", email);
                session.setAttribute("isLoggedIn", true);
                session.setAttribute("isGoogleLogin", true);

                // Update last login timestamp
                PreparedStatement psUpdateLogin = conn.prepareStatement(
                    "UPDATE admin_google SET last_login = NOW(), updated_at = NOW() WHERE id = ?"
                );
                psUpdateLogin.setInt(1, adminGoogleId);
                psUpdateLogin.executeUpdate();

                // Redirect to admin dashboard
                session.setAttribute("message", "Login berhasil! Selamat datang, " + name);
                session.setAttribute("messageType", "success");
                response.sendRedirect("../dashboardadmin.jsp");
                return;
            }
        }

        // If not an admin, check if user exists in user_google table
        PreparedStatement psCheckUserGoogle = conn.prepareStatement("SELECT * FROM user_google WHERE email = ?");
        psCheckUserGoogle.setString(1, email);
        ResultSet rsUserGoogle = psCheckUserGoogle.executeQuery();

        if (rsUserGoogle.next()) {
            // Existing Google user
            int userGoogleId = rsUserGoogle.getInt("id");
            int userId = rsUserGoogle.getInt("user_id");

            // Set session variables
            session.setAttribute("userId", String.valueOf(userId));
            session.setAttribute("name", name);
            session.setAttribute("email", email);
            session.setAttribute("isLoggedIn", true);
            session.setAttribute("isGoogleLogin", true);

            // Update last login timestamp
            PreparedStatement psUpdateLogin = conn.prepareStatement(
                "UPDATE user_google SET last_login = NOW(), updated_at = NOW() WHERE id = ?"
            );
            psUpdateLogin.setInt(1, userGoogleId);
            psUpdateLogin.executeUpdate();

            // Redirect to home page
            session.setAttribute("message", "Login berhasil! Selamat datang, " + name);
            session.setAttribute("messageType", "success");
            response.sendRedirect("../home.jsp");
            return;
        }

        // New user, create account in user table and user_google table
        // First create user record
        PreparedStatement psCreateUser = conn.prepareStatement(
            "INSERT INTO user (name, username, email, password, created_at, updated_at) VALUES (?, ?, ?, '', NOW(), NOW())",
            Statement.RETURN_GENERATED_KEYS
        );
        psCreateUser.setString(1, name);
        psCreateUser.setString(2, email.split("@")[0]); // Use first part of email as username
        psCreateUser.setString(3, email);
        int userResult = psCreateUser.executeUpdate();

        if (userResult > 0) {
            // Get the auto-generated ID
            ResultSet generatedKeys = psCreateUser.getGeneratedKeys();
            if (generatedKeys.next()) {
                int userId = generatedKeys.getInt(1);

                // Create user_google record
                PreparedStatement psCreateUserGoogle = conn.prepareStatement(
                    "INSERT INTO user_google (name, email, user_id, photo_url, created_at, last_login, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW(), NOW())"
                );
                psCreateUserGoogle.setString(1, name);
                psCreateUserGoogle.setString(2, email);
                psCreateUserGoogle.setInt(3, userId);
                psCreateUserGoogle.setString(4, photoURL);
                int userGoogleResult = psCreateUserGoogle.executeUpdate();

                if (userGoogleResult > 0) {
                    // Set session variables
                    session.setAttribute("userId", String.valueOf(userId));
                    session.setAttribute("name", name);
                    session.setAttribute("email", email);
                    session.setAttribute("isLoggedIn", true);
                    session.setAttribute("isGoogleLogin", true);

                    // Redirect to home page
                    session.setAttribute("message", "Akun berhasil dibuat! Selamat datang, " + name);
                    session.setAttribute("messageType", "success");
                    response.sendRedirect("../home.jsp");
                    return;
                }
            }
        }

        // If we get here, something went wrong with account creation
        session.setAttribute("message", "Gagal membuat akun baru");
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/loginwithgoogle.jsp");

    } catch (Exception e) {
        session.setAttribute("message", "Terjadi kesalahan: " + e.getMessage());
        session.setAttribute("messageType", "error");
        response.sendRedirect("../form/loginwithgoogle.jsp");
    }
%>
