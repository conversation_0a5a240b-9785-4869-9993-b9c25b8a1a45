<%-- Document : transactiondata Created on : Jun 14, 2025 Author : Arqeta
Description : Admin page untuk mengelola data transaksi. --%> <%@page
contentType="text/html" pageEncoding="UTF-8"%> <%@include
file="../../config/connection.jsp" %>

<div class="page-content">
  <div class="page-header">
    <h2>Data Transaksi</h2>
    <div class="action-bar">
      <div class="export-buttons">
        <button
          class="btn-export btn-excel"
          onclick="exportToExcel()"
          title="Ekspor ke Excel"
        >
          <i data-feather="file-text"></i>
          <span>Excel</span>
        </button>
        <button
          class="btn-export btn-pdf"
          onclick="exportToPDF()"
          title="Ekspor ke PDF"
        >
          <i data-feather="file"></i>
          <span>PDF</span>
        </button>
      </div>
      <div class="status-filter">
        <select id="statusFilter" onchange="filterTransactions()">
          <option value="">Semua Status</option>
          <option value="pending">Menunggu</option>
          <option value="completed">Selesai</option>
          <option value="cancelled">Dibatalkan</option>
        </select>
      </div>
    </div>
  </div>

  <div class="table-container">
    <div class="table-responsive">
      <table class="data-table">
        <thead>
          <tr>
            <th>ID</th>
            <th>Nama Layanan</th>
            <th>Pengguna</th>
            <th>Jumlah</th>
            <th>Unit</th>
            <th>Total</th>
            <th>Status</th>
            <th>Tanggal</th>
            <th>Aksi</th>
          </tr>
        </thead>
        <tbody id="transactionTableBody">
          <% try { // Query untuk mengambil data transaksi dengan join ke tabel
          user dan services String query = "SELECT t.*, u.name as user_name,
          s.name as service_name " + "FROM transaction t " + "LEFT JOIN user u
          ON t.user_id = u.id " + "LEFT JOIN services s ON t.service_id = s.id "
          + "ORDER BY t.id ASC"; PreparedStatement ps =
          conn.prepareStatement(query); ResultSet rs = ps.executeQuery(); while
          (rs.next()) { int id = rs.getInt("id"); String serviceName =
          rs.getString("service_name"); String userName =
          rs.getString("user_name"); double amount = rs.getDouble("amount"); int
          unit = rs.getInt("unit"); double total = rs.getDouble("total"); String
          status = rs.getString("status"); String createdAt =
          rs.getString("created_at"); String statusClass = ""; String statusText
          = ""; switch(status) { case "menunggu": case "pending": statusClass =
          "status-pending"; statusText = "Menunggu"; break; case "diterima":
          case "completed": statusClass = "status-approved"; statusText =
          "Diterima"; break; case "ditolak": case "cancelled": statusClass =
          "status-rejected"; statusText = "Ditolak"; break; default: statusClass
          = "status-pending"; statusText = status; } %>
          <tr data-status="<%= status %>">
            <td><%= id %></td>
            <td>
              <%= serviceName != null ? serviceName : "Layanan Tidak Ditemukan"
              %>
            </td>
            <td><%= userName != null ? userName : "User Tidak Ditemukan" %></td>
            <td>Rp <%= String.format("%,.0f", amount) %></td>
            <td><%= unit %></td>
            <td>Rp <%= String.format("%,.0f", total) %></td>
            <td>
              <span class="status-badge <%= statusClass %>">
                <%= statusText %>
              </span>
            </td>
            <td><%= createdAt %></td>
            <td>
              <div class="action-buttons">
                <% if ("menunggu".equals(status) || "pending".equals(status)) {
                %>
                <button
                  class="btn-edit"
                  onclick="manageTransaction(<%= id %>, 'accept')"
                  title="Terima"
                >
                  <i data-feather="check"></i>
                </button>
                <button
                  class="btn-delete"
                  onclick="manageTransaction(<%= id %>, 'reject')"
                  title="Tolak"
                >
                  <i data-feather="x"></i>
                </button>
                <% } else { %>
                <span class="status-info">Transaksi sudah diproses</span>
                <% } %>
              </div>
            </td>
          </tr>
          <% } rs.close(); ps.close(); } catch (SQLException e) { out.println("
          <tr>
            <td colspan="9">Error: " + e.getMessage() + "</td>
          </tr>
          "); } %>
        </tbody>
      </table>
    </div>
  </div>
</div>

<script>
  /**
   * Mengelola aksi transaksi seperti menerima atau menolak.
   * @param {number} id - ID transaksi.
   * @param {string} action - Aksi yang akan dilakukan ('accept' atau 'reject').
   */
  function manageTransaction(id, action) {
    let confirmMessage = "";
    switch (action) {
      case "accept":
        confirmMessage = "Apakah Anda yakin ingin menerima transaksi ini?";
        break;
      case "reject":
        confirmMessage = "Apakah Anda yakin ingin menolak transaksi ini?";
        break;
      default:
        confirmMessage = "Apakah Anda yakin ingin melakukan tindakan ini?";
    }

    if (confirm(confirmMessage)) {
      const form = document.createElement("form");
      form.method = "POST";
      // Arahkan ke file processor yang benar
      form.action =
        "<%= request.getContextPath() %>/process/admin/manageTransaction.jsp";

      const idInput = document.createElement("input");
      idInput.type = "hidden";
      idInput.name = "id";
      idInput.value = id;

      const actionInput = document.createElement("input");
      actionInput.type = "hidden";
      actionInput.name = "action";
      actionInput.value = action;

      form.appendChild(idInput);
      form.appendChild(actionInput);
      document.body.appendChild(form);
      form.submit();
    }
  }

  /**
   * Menyaring baris tabel transaksi berdasarkan status yang dipilih.
   */
  function filterTransactions() {
    const filter = document.getElementById("statusFilter").value;
    const rows = document.querySelectorAll("#transactionTableBody tr");

    rows.forEach((row) => {
      const status = row.getAttribute("data-status");
      if (filter === "" || status === filter) {
        row.style.display = "";
      } else {
        row.style.display = "none";
      }
    });
  }

  /**
   * Ekspor data transaksi ke format Excel.
   */
  function exportToExcel() {
    // Tampilkan loading indicator
    if (typeof showNotification === "function") {
      showNotification("Sedang memproses ekspor Excel...", "info");
    }

    // Buat form untuk submit ke processor
    const form = document.createElement("form");
    form.method = "POST";
    form.action =
      "<%= request.getContextPath() %>/process/admin/exportTransactions.jsp";

    const formatInput = document.createElement("input");
    formatInput.type = "hidden";
    formatInput.name = "format";
    formatInput.value = "excel";

    form.appendChild(formatInput);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
  }

  /**
   * Ekspor data transaksi ke format PDF.
   */
  function exportToPDF() {
    // Tampilkan loading indicator
    if (typeof showNotification === "function") {
      showNotification("Sedang memproses ekspor PDF...", "info");
    }

    // Buat form untuk submit ke processor
    const form = document.createElement("form");
    form.method = "POST";
    form.action =
      "<%= request.getContextPath() %>/process/admin/exportTransactions.jsp";

    const formatInput = document.createElement("input");
    formatInput.type = "hidden";
    formatInput.name = "format";
    formatInput.value = "pdf";

    form.appendChild(formatInput);
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
  }
</script>
