<%--
    Document   : getTransactionDetails
    Created on : Jun 16, 2025, 5:00:00 PM
    Author     : Arqeta
    Description: Get transaction details for modal display
--%>

<%@page contentType="application/json" pageEncoding="UTF-8"%>
<%@include file="../config/connection.jsp" %>

<%
    // Set response type to JSON
    response.setContentType("application/json");
    
    // Check if user is logged in
    String userId = (String) session.getAttribute("userId");
    if (userId == null) {
        out.print("{\"success\": false, \"message\": \"Sesi telah berakhir.\"}");
        return;
    }

    // Get transaction ID
    String transactionIdStr = request.getParameter("id");
    
    if (transactionIdStr == null || transactionIdStr.trim().isEmpty()) {
        out.print("{\"success\": false, \"message\": \"Transaction ID tidak valid.\"}");
        return;
    }

    try {
        int transactionId = Integer.parseInt(transactionIdStr);

        // Get transaction details with service information
        PreparedStatement ps = conn.prepareStatement(
            "SELECT t.*, s.name as service_name, s.images as service_image " +
            "FROM transaction t " +
            "JOIN services s ON t.service_id = s.id " +
            "WHERE t.id = ? AND t.user_id = ?"
        );
        ps.setInt(1, transactionId);
        ps.setInt(2, Integer.parseInt(userId));
        ResultSet rs = ps.executeQuery();

        if (!rs.next()) {
            out.print("{\"success\": false, \"message\": \"Transaksi tidak ditemukan.\"}");
            rs.close();
            ps.close();
            return;
        }

        // Get transaction data
        String serviceName = rs.getString("service_name");
        String serviceImage = rs.getString("service_image");
        double amount = rs.getDouble("amount");
        int unit = rs.getInt("unit");
        double total = rs.getDouble("total");
        String status = rs.getString("status");
        String createdAt = rs.getString("created_at");
        String updatedAt = rs.getString("updated_at");
        
        rs.close();
        ps.close();

        // Convert status to readable text
        String statusText = "";
        switch (status) {
            case "pending":
                statusText = "Menunggu Konfirmasi";
                break;
            case "completed":
                statusText = "Selesai";
                break;
            case "cancelled":
                statusText = "Dibatalkan";
                break;
            default:
                statusText = status;
        }

        // Format dates
        String formattedCreatedAt = createdAt;
        String formattedUpdatedAt = updatedAt;
        
        try {
            java.text.SimpleDateFormat inputFormat = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            java.text.SimpleDateFormat outputFormat = new java.text.SimpleDateFormat("dd MMMM yyyy, HH:mm", new java.util.Locale("id", "ID"));
            
            java.util.Date createdDate = inputFormat.parse(createdAt);
            java.util.Date updatedDate = inputFormat.parse(updatedAt);
            
            formattedCreatedAt = outputFormat.format(createdDate);
            formattedUpdatedAt = outputFormat.format(updatedDate);
        } catch (Exception e) {
            // Use original format if parsing fails
        }

        // Return JSON response
        out.print("{");
        out.print("\"success\": true,");
        out.print("\"id\": " + transactionId + ",");
        out.print("\"serviceName\": \"" + serviceName.replace("\"", "\\\"") + "\",");
        out.print("\"serviceImage\": \"" + serviceImage + "\",");
        out.print("\"amount\": " + amount + ",");
        out.print("\"unit\": " + unit + ",");
        out.print("\"total\": " + total + ",");
        out.print("\"status\": \"" + status + "\",");
        out.print("\"statusText\": \"" + statusText + "\",");
        out.print("\"createdAt\": \"" + formattedCreatedAt + "\",");
        out.print("\"updatedAt\": \"" + formattedUpdatedAt + "\"");
        out.print("}");

    } catch (NumberFormatException e) {
        out.print("{\"success\": false, \"message\": \"Transaction ID tidak valid.\"}");
    } catch (SQLException e) {
        out.print("{\"success\": false, \"message\": \"Terjadi kesalahan database: " + e.getMessage().replace("\"", "\\\"") + "\"}");
    } catch (Exception e) {
        out.print("{\"success\": false, \"message\": \"Terjadi kesalahan: " + e.getMessage().replace("\"", "\\\"") + "\"}");
    }
%>
